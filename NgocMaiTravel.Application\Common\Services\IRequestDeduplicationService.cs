using System;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Common.Services
{
    /// <summary>
    /// Service for deduplicating concurrent requests with same parameters
    /// </summary>
    public interface IRequestDeduplicationService
    {
        /// <summary>
        /// Execute a function with request deduplication
        /// If multiple requests with same key are made concurrently, only one will execute
        /// and all will receive the same result
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="key">Unique key for the request</param>
        /// <param name="factory">Function to execute if not already running</param>
        /// <param name="cacheDurationSeconds">How long to cache the result (default: 30 seconds)</param>
        /// <returns>Result from the function</returns>
        Task<T> ExecuteAsync<T>(string key, Func<Task<T>> factory, int cacheDurationSeconds = 30);

        /// <summary>
        /// Clear cached result for a specific key
        /// </summary>
        /// <param name="key">Key to clear</param>
        void ClearCache(string key);

        /// <summary>
        /// Clear all cached results
        /// </summary>
        void ClearAllCache();

        /// <summary>
        /// Get cache statistics
        /// </summary>
        /// <returns>Cache statistics</returns>
        object GetCacheStats();

        /// <summary>
        /// Cleanup expired semaphores (maintenance operation)
        /// </summary>
        void CleanupExpiredSemaphores();
    }
}
