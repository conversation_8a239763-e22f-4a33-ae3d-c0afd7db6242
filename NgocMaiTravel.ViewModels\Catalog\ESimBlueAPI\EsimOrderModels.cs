using System;
using System.ComponentModel.DataAnnotations;

namespace NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI
{
    /// <summary>
    /// ESim Order Request Model - matches ESimBlue API format
    /// </summary>
    public class EsimOrderRq
    {
        [Required]
        public string RequestId { get; set; } = string.Empty;

        [Required]
        public List<PackageInfo> PackageInfoList { get; set; } = new();

        // Additional fields for internal use (not sent to API)
        [EmailAddress]
        public string? CustomerEmail { get; set; }

        public string? CustomerPhone { get; set; }

        public string? CustomerName { get; set; }

        public string? Notes { get; set; }
    }

    /// <summary>
    /// Package information for order
    /// </summary>
    public class PackageInfo
    {
        [Required]
        public string Sku { get; set; } = string.Empty;

        [Required]
        [Range(1, 100)]
        public int Quantity { get; set; } = 1;
    }

    /// <summary>
    /// ESim Order Response Model - matches ESimBlue API format
    /// </summary>
    public class EsimOrderRp
    {
        public string OrderPublicId { get; set; } = string.Empty;

        public List<SerialInfo> SerialList { get; set; } = [];
    }

    /// <summary>
    /// Serial information in order response
    /// </summary>
    public class SerialInfo
    {
        public string Serial { get; set; } = string.Empty;

        public int ProductId { get; set; }

        /// <summary>
        /// Status: 0 = Created, 1 = Activated, etc.
        /// </summary>
        public int Status { get; set; }

        public string EsimPublicId { get; set; } = string.Empty;
    }

    /// <summary>
    /// ESim Order Status Response Model
    /// </summary>
    public class EsimOrderStatusRp
    {
        public string OrderPublicId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public List<SerialInfo> SerialList { get; set; } = [];
        public DateTime CreatedAt { get; set; }
        public DateTime? ActivatedAt { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public string? CustomerEmail { get; set; }
        public string? CustomerPhone { get; set; }
        public decimal Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string PlanName { get; set; } = string.Empty;
        public EsimUsageInfo? UsageInfo { get; set; }
        public string Message { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// ESim Activation Response Model
    /// </summary>
    public class EsimActivationRp
    {
        public string Serial { get; set; } = string.Empty;
        public string ICCID { get; set; } = string.Empty;
        public string LPA { get; set; } = string.Empty;
        public string QRCode { get; set; } = string.Empty;
        public DateTime ActivatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }
    }

    /// <summary>
    /// ESim Usage Response Model
    /// </summary>
    public class EsimUsageRp
    {
        public string Serial { get; set; } = string.Empty;
        public string ICCID { get; set; } = string.Empty;
        public EsimUsageInfo UsageInfo { get; set; } = new();
        public string Status { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// ESim Usage Information
    /// </summary>
    public class EsimUsageInfo
    {
        public long DataUsedBytes { get; set; }
        public long DataLimitBytes { get; set; }
        public double DataUsedPercentage { get; set; }
        public int DaysUsed { get; set; }
        public int DaysLimit { get; set; }
        public double DaysUsedPercentage { get; set; }
        public DateTime? FirstUsed { get; set; }
        public DateTime? LastUsed { get; set; }
        public bool IsUnlimited { get; set; }
        public string CurrentCountry { get; set; } = string.Empty;
        public string CurrentNetwork { get; set; } = string.Empty;
    }

    /// <summary>
    /// ESim Redeem Response Model
    /// </summary>
    public class EsimRedeemRp
    {
        public string Serial { get; set; } = string.Empty;
        public string ICCID { get; set; } = string.Empty;
        public string LPA { get; set; } = string.Empty;
        public string QRCode { get; set; } = string.Empty;
        public DateTime RedeemedAt { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }
        public EsimActivationInfo? ActivationInfo { get; set; }
    }

    /// <summary>
    /// ESim Activation Information
    /// </summary>
    public class EsimActivationInfo
    {
        public string ActivationCode { get; set; } = string.Empty;
        public string InstallationInstructions { get; set; } = string.Empty;
        public string SupportedDevices { get; set; } = string.Empty;
        public DateTime ValidUntil { get; set; }
        public bool RequiresManualActivation { get; set; }
    }

    /// <summary>
    /// ESim Order Status Enum
    /// </summary>
    public enum EsimOrderStatus
    {
        Pending,
        Processing,
        Completed,
        Failed,
        Cancelled,
        Refunded,
        Activated,
        Expired
    }

    /// <summary>
    /// ESim Status Enum
    /// </summary>
    public enum EsimStatus
    {
        Created,
        Activated,
        InUse,
        Suspended,
        Expired,
        Cancelled
    }
}
