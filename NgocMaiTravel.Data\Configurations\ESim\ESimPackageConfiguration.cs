using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NgocMaiTravel.Data.Entities.ESim;

namespace NgocMaiTravel.Data.Configurations.ESim
{
    /// <summary>
    /// Entity configuration for ESimPackage
    /// </summary>
    public class ESimPackageConfiguration : IEntityTypeConfiguration<ESimPackage>
    {
        public void Configure(EntityTypeBuilder<ESimPackage> builder)
        {
            // Table name
            builder.ToTable("tblESimPackage");

            // Primary key
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasComment("Primary key");

            // PackageId - external identifier
            builder.Property(e => e.PackageId)
                .IsRequired()
                .HasMaxLength(50)
                .HasComment("External package ID from API");

            builder.HasIndex(e => e.PackageId)
                .IsUnique()
                .HasDatabaseName("UK_tblESimPackage_PackageId");

            // Sku - unique identifier
            builder.Property(e => e.Sku)
                .IsRequired()
                .HasMaxLength(100)
                .HasComment("Package SKU");

            builder.HasIndex(e => e.Sku)
                .IsUnique()
                .HasDatabaseName("UK_tblESimPackage_Sku");

            // Package details
            builder.Property(e => e.PackageName)
                .IsRequired()
                .HasMaxLength(255)
                .HasComment("Package display name");

            builder.Property(e => e.Description)
                .HasMaxLength(2000)
                .HasComment("Package description");

            // Data specifications
            builder.Property(e => e.DataAmount)
                .IsRequired()
                .HasComment("Data amount in bytes");

            builder.Property(e => e.ValidityDays)
                .IsRequired()
                .HasComment("Validity period in days");

            // Pricing
            builder.Property(e => e.Price)
                .IsRequired()
                .HasColumnType("decimal(18,2)")
                .HasComment("Package price");

            builder.Property(e => e.Currency)
                .IsRequired()
                .HasMaxLength(10)
                .HasDefaultValue("USD")
                .HasComment("Price currency");

            builder.Property(e => e.OriginalPrice)
                .HasColumnType("decimal(18,2)")
                .HasComment("Original price before discount");

            builder.Property(e => e.DiscountPercent)
                .HasColumnType("decimal(5,2)")
                .HasComment("Discount percentage");

            // Package characteristics
            builder.Property(e => e.PackageType)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("DATA")
                .HasComment("Package type: DATA, VOICE, SMS, COMBO");

            builder.Property(e => e.NetworkType)
                .HasMaxLength(50)
                .HasComment("Network type: 3G, 4G, 5G");

            builder.Property(e => e.IsUnlimited)
                .IsRequired()
                .HasDefaultValue(false)
                .HasComment("Whether data is unlimited");

            builder.Property(e => e.IsTopUpSupported)
                .IsRequired()
                .HasDefaultValue(false)
                .HasComment("Whether top-up is supported");

            // Status and metadata
            builder.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValue(true)
                .HasComment("Whether the package is active");

            builder.Property(e => e.ApiSource)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("ESimBlue")
                .HasComment("Source API provider");

            builder.Property(e => e.ExternalData)
                .HasColumnType("NVARCHAR(MAX)")
                .HasComment("JSON data from external API");

            // Audit fields
            builder.Property(e => e.CreatedDate)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Creation timestamp");

            builder.Property(e => e.UpdatedDate)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Last update timestamp");

            builder.Property(e => e.LastSyncDate)
                .HasComment("Last sync from API timestamp");

            builder.Property(e => e.CreatedBy)
                .HasMaxLength(255)
                .HasComment("User who created the record");

            builder.Property(e => e.UpdatedBy)
                .HasMaxLength(255)
                .HasComment("User who last updated the record");

            // Indexes for performance
            builder.HasIndex(e => e.IsActive)
                .HasDatabaseName("IX_tblESimPackage_IsActive");

            builder.HasIndex(e => e.PackageType)
                .HasDatabaseName("IX_tblESimPackage_PackageType");

            builder.HasIndex(e => e.Price)
                .HasDatabaseName("IX_tblESimPackage_Price");

            builder.HasIndex(e => e.DataAmount)
                .HasDatabaseName("IX_tblESimPackage_DataAmount");

            builder.HasIndex(e => e.ValidityDays)
                .HasDatabaseName("IX_tblESimPackage_ValidityDays");

            builder.HasIndex(e => e.LastSyncDate)
                .HasDatabaseName("IX_tblESimPackage_LastSyncDate");

            // Navigation properties
            builder.HasMany(e => e.GroupPackages)
                .WithOne(gp => gp.Package)
                .HasForeignKey(gp => gp.PackageId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
