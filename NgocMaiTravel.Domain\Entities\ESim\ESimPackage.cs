using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NgocMaiTravel.Domain.Entities.ESim
{
    /// <summary>
    /// ESim Package Entity
    /// </summary>
    [Table("tblESimPackage")]
    public class ESimPackage
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(50)]
        public string PackageId { get; set; } = string.Empty; // External package ID

        [Required]
        [StringLength(100)]
        public string Sku { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string PackageName { get; set; } = string.Empty;

        [StringLength(2000)]
        public string? Description { get; set; }

        public long DataAmount { get; set; } // Data in bytes

        public int ValidityDays { get; set; } // Validity period in days

        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        [Required]
        [StringLength(10)]
        public string Currency { get; set; } = "USD";

        [Column(TypeName = "decimal(18,2)")]
        public decimal? OriginalPrice { get; set; }

        [Column(TypeName = "decimal(5,2)")]
        public decimal? DiscountPercent { get; set; }

        [Required]
        [StringLength(50)]
        public string PackageType { get; set; } = "DATA"; // DATA, VOICE, SMS, COMBO

        [StringLength(50)]
        public string? NetworkType { get; set; } // 4G, 5G, 3G

        public bool IsUnlimited { get; set; } = false;

        public bool IsTopUpSupported { get; set; } = false;

        public bool IsActive { get; set; } = true;

        [Required]
        [StringLength(50)]
        public string ApiSource { get; set; } = "ESimBlue";

        public string? ExternalData { get; set; } // JSON data from external API

        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;

        public DateTime? LastSyncDate { get; set; }

        [StringLength(255)]
        public string? CreatedBy { get; set; }

        [StringLength(255)]
        public string? UpdatedBy { get; set; }

        // Navigation properties
        public virtual ICollection<ESimGroupPackage> GroupPackages { get; set; } = new List<ESimGroupPackage>();

        // Helper property to get groups
        [NotMapped]
        public virtual IEnumerable<ESimGroup> Groups => GroupPackages.Where(gp => gp.IsActive).Select(gp => gp.Group);

        // Helper methods
        [NotMapped]
        public string FormattedDataAmount
        {
            get
            {
                if (IsUnlimited) return "Unlimited";
                
                if (DataAmount >= 1024 * 1024 * 1024) // GB
                    return $"{DataAmount / (1024 * 1024 * 1024)} GB";
                else if (DataAmount >= 1024 * 1024) // MB
                    return $"{DataAmount / (1024 * 1024)} MB";
                else if (DataAmount >= 1024) // KB
                    return $"{DataAmount / 1024} KB";
                else
                    return $"{DataAmount} Bytes";
            }
        }

        [NotMapped]
        public string FormattedValidity => ValidityDays == 1 ? "1 Day" : $"{ValidityDays} Days";

        [NotMapped]
        public string FormattedPrice => $"{Price:F2} {Currency}";
    }

    /// <summary>
    /// ESim Package Types
    /// </summary>
    public static class ESimPackageTypes
    {
        public const string Data = "DATA";
        public const string Voice = "VOICE";
        public const string Sms = "SMS";
        public const string Combo = "COMBO";
    }

    /// <summary>
    /// Network Types
    /// </summary>
    public static class NetworkTypes
    {
        public const string ThreeG = "3G";
        public const string FourG = "4G";
        public const string FiveG = "5G";
    }
}
