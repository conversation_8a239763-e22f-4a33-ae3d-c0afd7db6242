-- <PERSON><PERSON>t to clean up duplicate ESim log records
-- Run this script to remove duplicate entries caused by Entity Framework tracking conflicts

-- 1. First, let's see the duplicate records
SELECT 
    TraceId,
    Action,
    COUNT(*) as DuplicateCount,
    MIN(Timestamp) as FirstTimestamp,
    MAX(Timestamp) as LastTimestamp
FROM tblESimLogs 
WHERE Action = 'search_plans'
    AND Timestamp >= DATEADD(hour, -1, GETDATE()) -- Last hour only
GROUP BY TraceId, Action
HAVING COUNT(*) > 1
ORDER BY DuplicateCount DESC;

-- 2. Clean up duplicates - Keep the first record (earliest timestamp) for each TraceId + Action
WITH DuplicateRecords AS (
    SELECT 
        Id,
        TraceId,
        Action,
        Timestamp,
        ROW_NUMBER() OVER (
            PARTITION BY TraceId, Action 
            ORDER BY Timestamp ASC
        ) as RowNum
    FROM tblESimLogs 
    WHERE Action = 'search_plans'
        AND Timestamp >= DATEADD(hour, -1, GETDATE()) -- Last hour only
)
DELETE FROM tblESimLogs 
WHERE Id IN (
    SELECT Id 
    FROM DuplicateRecords 
    WHERE RowNum > 1
);

-- 3. Verify cleanup - Should return 0 duplicates
SELECT 
    TraceId,
    Action,
    COUNT(*) as RecordCount
FROM tblESimLogs 
WHERE Action = 'search_plans'
    AND Timestamp >= DATEADD(hour, -1, GETDATE())
GROUP BY TraceId, Action
HAVING COUNT(*) > 1;

-- 4. Optional: Clean up orphaned log files
-- (This would need to be done programmatically, not in SQL)
-- Check for RequestData and ResponseData file paths that no longer exist in database

PRINT 'Duplicate ESim log cleanup completed.';
