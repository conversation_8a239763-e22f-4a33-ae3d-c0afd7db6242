﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NgocMaiTravel.ApiIntegration;
using NgocMaiTravel.ApiIntegration.NMBookingAPI;
using NgocMaiTravel.ApiIntegration.RateLimiter.BookTrip;
using NgocMaiTravel.Application.Catalog.Airport;
using NgocMaiTravel.Application.Catalog.Feature;
using NgocMaiTravel.Application.Catalog.Flight;
using NgocMaiTravel.Application.System.XDevice;
using NgocMaiTravel.Utilities.Constants;
using NgocMaiTravel.ViewModels.Catalog.Airport;
using NgocMaiTravel.ViewModels.Catalog.Flight;
using NgocMaiTravel.ViewModels.Common;
using NgocMaiTravel.ViewModels.System.Users;
using System.Text;
using System.Threading.RateLimiting;


namespace NgocMaiTravel.BackendApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class FlightsController : ControllerBase
    {
        private readonly ILogger<FlightsController> _logger;
        private readonly IFlightService _flightService;
        private readonly IBookingRateLimiter _bookingRateLimiter;
        private readonly IAirportService _airportService;
        private readonly INMBookingApiClient _nMBookingApiClient;
        private readonly IXDeviceService _xDeviceService;
        private readonly IFeatureService _featureService;

        public FlightsController(ILogger<FlightsController> logger, IFlightService flightService, IBookingRateLimiter bookingRateLimiter, IAirportService airportService, INMBookingApiClient nMBookingApiClient, IXDeviceService xDeviceService, IFeatureService featureService)
        {
            _logger = logger;
            _flightService = flightService;
            _bookingRateLimiter = bookingRateLimiter;
            _airportService = airportService;
            _nMBookingApiClient = nMBookingApiClient;
            _xDeviceService = xDeviceService;
            _featureService = featureService;
        }

        /// <summary>
        /// Sent request to get flight
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("sent-request")]
        [AllowAnonymous]
        public async Task<IActionResult> SentRequest([FromBody] FlightRequestVM request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var result = await _flightService.SentRequest(request);
            return Ok(result);
        }
        /// <summary>
        /// add or update airport
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("airport")]
        public async Task<IActionResult> Post([FromForm] AirportPostRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var result = await _airportService.Post(request);
            return Ok(result);
        }
        /// <summary>
        /// get all airport with paging
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("airport/paging")]
        [AllowAnonymous]
        public async Task<IActionResult> Paging([FromBody] AirportPagingRequest request)
        {
            var result = await _airportService.Paging(request);
            return Ok(result);
        }

        /// <summary>
        /// get all airport with tree structure
        /// </summary>
        /// <returns></returns>
        [HttpGet("airport/tree")]
        [AllowAnonymous]
        public async Task<IActionResult> GetTree()
        {
            var result = await _airportService.GetTree();
            return Ok(result);
        }

        /// <summary>
        /// get airport by id
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet("airport/{Id}")]
        public async Task<IActionResult> GetAirportByID(string Id)
        {
            var result = await _airportService.GetByID(Id);
            return Ok(result);
        }
        /// <summary>
        /// display or hide airport
        /// </summary>
        /// <param name="listId"></param>
        /// <param name="show"></param>
        /// <returns></returns>
        [HttpPut("airport")]
        public async Task<IActionResult> display([FromQuery] string[] listId, bool show)
        {
            var result = await _airportService.display(listId, show);
            return Ok(result);
        }

        /// <summary>
        /// get all flight request with paging
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("request/paging")]
        public async Task<IActionResult> PagingRequest(FlightRequestPagingRequest request)
        {
            var result = await _flightService.PagingRequest(request);
            return Ok(result);
        }

        /// <summary>
        /// get flight request by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("request/{id}")]
        public async Task<IActionResult> GetRequestByID(string id)
        {
            var result = await _flightService.GetRequestByID(id);
            return Ok(result);
        }

        /// <summary>
        /// get flight request by id for admin
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("request/admin/{id}")]
        public async Task<IActionResult> GetRequestDetailsByID(string id)
        {
            var result = await _flightService.GetRequestDetailsByID(id);
            return Ok(result);
        }

        /// <summary>
        /// update status of flight request
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        [HttpPut("request/{Id}/status/{status}")]
        public async Task<IActionResult> UpdateStatus(string Id, int status)
        {
            var result = await _flightService.UpdateStatus(Id, status);
            return Ok(result);
        }

        /// <summary>
        /// search trip
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("SearchTrip")]
        [AllowAnonymous]
        public async Task<IActionResult> SearchTrip([FromBody] EncryptModelRequest request)
            => await ProcessRequest<RqSearchTripModel, ApiResult<RpSearchItineraryModel>>(request, _nMBookingApiClient.SearchTrip);

        /// <summary>
        /// price ancillary
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("PriceAncillary")]
        [AllowAnonymous]
        public async Task<IActionResult> PriceAncillary([FromBody] EncryptModelRequest request)
            => await ProcessRequest<RqPriceAncillary, ApiResult<RpPriceItineraryModel>>(request, _nMBookingApiClient.PriceAncillary);

        /// <summary>
        /// book trip
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("BookTrip")]
        [AllowAnonymous]
        public async Task<IActionResult> BookTrip([FromBody] EncryptModelRequest request)
            => await ProcessRequest<RqBookTripModel, ApiResult<RpBookRetrieveModel>>(request, _nMBookingApiClient.BookTrip);

        /// <summary>
        /// retrieve
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("Retrieve")]
        [AllowAnonymous]
        public async Task<IActionResult> Retrieve([FromBody] EncryptModelRequest request)
            => await ProcessRequest<RqRetrieveBookingModel, ApiResult<RpRetrieveBookingModel>>(request, _nMBookingApiClient.Retrieve);

        /// <summary>
        /// request trip
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("RequestTrip")]
        [AllowAnonymous]
        public async Task<IActionResult> RequestTrip([FromBody] EncryptModelRequest request)
            => await ProcessRequest<FlightRequestVM, ApiResult<TripAvailableRequest>>(request, _flightService.RequestTrip);

        /// <summary>
        /// available trip
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("AvailableTrip")]
        [AllowAnonymous]
        public async Task<IActionResult> AvailableTrip([FromBody] EncryptModelRequest request)
            => await ProcessRequest<TripAvailableRequest, ApiResult<OrderTripVM>>(request, _flightService.TripAvailable);

        /// <summary>
        /// repayment trip
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("RePayment")]
        [AllowAnonymous]
        public async Task<IActionResult> RePayment([FromBody] EncryptModelRequest request)
           => await ProcessRequest<TripRePaymentRequest, ApiResult<string>>(request, _flightService.RePayment);

        /// <summary>
        /// Get flight request by partner id
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("request/partner/paging")]
        [Authorize]
        public async Task<IActionResult> GetFlightRequestByPartnerID([FromBody] PartnerTicketPagingRequest request)
        {
            var result = await _flightService.PartnerPagingRequest(request);
            return Ok(result);
        }

        /// <summary>
        /// add note for flight request by user
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost("AddNoteUser")]
        public async Task<IActionResult> AddNoteUser([FromBody] FlightNoteUserRq request)
        {
            var result = await _flightService.AddNoteUser(request);
            return Ok(result);
        }

        #region Process request with endrypt
        private async Task<IActionResult> ProcessRequest<TRequest, TResponse>(EncryptModelRequest request, Func<TRequest, Task<TResponse>> apiCall) where TResponse : ApiResultBase
        {
            var xDeviceInfor = _xDeviceService.GetKeyDevice();
            if (!xDeviceInfor.IsSuccessed)
                return StatusCode(StatusCodes.Status403Forbidden, xDeviceInfor.Message);

            var serverPrivateKey = Encoding.UTF8.GetString(Convert.FromBase64String(xDeviceInfor.ResultObj.RSAprivateKey));
            var dataRequest = _xDeviceService.HybridDecryptInternal(serverPrivateKey, request.EncryptData);
            var requestModel = JsonConvert.DeserializeObject<TRequest>(dataRequest);

            var result = new ApiResultBase();

            if (requestModel is FlightRequestVM)
            {
                if (!_bookingRateLimiter.CanBook())
                {
                    result = new ApiErrorResult<bool>("Bạn đã đặt quá nhiều vé trong thời gian ngắn, vui lòng thử lại sau.");
                }
                else
                {
                    result = await apiCall(requestModel);

                }
                _bookingRateLimiter.RecordBooking();
            }
            else
            {

                result = await apiCall(requestModel!);
            } 

            
            if (requestModel is RqSearchTripModel) {
                #region penalty search trip
                var checkSearchPenalty = _bookingRateLimiter.getSearchPenalty();
                if (checkSearchPenalty)
                {
                    await Task.Delay(15000);
                }
                #endregion

                #region add ticketIssuanceFee
                var resulttemp = result as ApiResult<RpSearchItineraryModel>;
                if (resulttemp?.IsSuccessed == true && resulttemp.ResultObj != null)
                {
                    var clonedObj = JsonConvert.DeserializeObject<RpSearchItineraryModel>(JsonConvert.SerializeObject(resulttemp.ResultObj)); // Deep clone
                    var rr = _nMBookingApiClient.SearchTripAddIssuanceFee(clonedObj!, SystemConstants.TicketIssuanceFee);
                    resulttemp.ResultObj = rr;
                    result = resulttemp;
                }

                #endregion
            }


            var resultJson = JsonConvert.SerializeObject(result);
            var resultEncrypt = await _xDeviceService.HybridEncrypt(xDeviceInfor.ResultObj.EncryptionPublicKey, resultJson);

            return Ok(resultEncrypt);
        }

        #endregion
    }
}
