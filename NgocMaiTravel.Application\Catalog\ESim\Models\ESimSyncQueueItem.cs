namespace NgocMaiTravel.Application.Catalog.ESim.Models
{
    /// <summary>
    /// Queue item for ESim sync operations
    /// </summary>
    public class ESimSyncQueueItem
    {
        /// <summary>
        /// Unique identifier for the sync operation
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString("N");

        /// <summary>
        /// Type of sync operation
        /// </summary>
        public string SyncType { get; set; } = string.Empty;

        /// <summary>
        /// Optional group code for group-specific sync
        /// </summary>
        public string? GroupCode { get; set; }

        /// <summary>
        /// Who/what requested this sync
        /// </summary>
        public string RequestedBy { get; set; } = string.Empty;

        /// <summary>
        /// Priority of the sync operation (1 = highest, 10 = lowest)
        /// </summary>
        public int Priority { get; set; } = 5;

        /// <summary>
        /// When this item was queued
        /// </summary>
        public DateTime QueuedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When this sync should be executed (for delayed execution)
        /// </summary>
        public DateTime? ScheduledAt { get; set; }

        /// <summary>
        /// Number of retry attempts
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// Maximum number of retry attempts
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// Additional metadata for the sync operation
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// Check if this item is ready to be processed
        /// </summary>
        public bool IsReadyToProcess => ScheduledAt == null || ScheduledAt <= DateTime.UtcNow;

        /// <summary>
        /// Check if this item has exceeded max retries
        /// </summary>
        public bool HasExceededMaxRetries => RetryCount >= MaxRetries;

        /// <summary>
        /// Create a full sync queue item
        /// </summary>
        public static ESimSyncQueueItem CreateFullSync(string requestedBy, int priority = 5)
        {
            return new ESimSyncQueueItem
            {
                SyncType = ESimSyncTypes.FullSync,
                RequestedBy = requestedBy,
                Priority = priority
            };
        }

        /// <summary>
        /// Create an incremental sync queue item
        /// </summary>
        public static ESimSyncQueueItem CreateIncrementalSync(string requestedBy, int priority = 5)
        {
            return new ESimSyncQueueItem
            {
                SyncType = ESimSyncTypes.IncrementalSync,
                RequestedBy = requestedBy,
                Priority = priority
            };
        }

        /// <summary>
        /// Create a group sync queue item
        /// </summary>
        public static ESimSyncQueueItem CreateGroupSync(string groupCode, string requestedBy, int priority = 5)
        {
            return new ESimSyncQueueItem
            {
                SyncType = ESimSyncTypes.GroupSync,
                GroupCode = groupCode,
                RequestedBy = requestedBy,
                Priority = priority
            };
        }

        /// <summary>
        /// Create a scheduled sync queue item
        /// </summary>
        public static ESimSyncQueueItem CreateScheduledSync(string requestedBy, DateTime scheduledAt, int priority = 5)
        {
            return new ESimSyncQueueItem
            {
                SyncType = ESimSyncTypes.ScheduledSync,
                RequestedBy = requestedBy,
                Priority = priority,
                ScheduledAt = scheduledAt
            };
        }
    }
}
