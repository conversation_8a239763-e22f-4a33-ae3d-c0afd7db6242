using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using NgocMaiTravel.ApiIntegration.Common.Logger;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace NgocMaiTravel.ApiIntegration.ESimBlueAPI
{
    public class ESimBlueAPIClient : IESimBlueAPIClient
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly NgocMaiTravelDbContext _context;
        private readonly ILoggerService _loggerService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly string _baseUrl;
        private readonly string _apiKey;

        public ESimBlueAPIClient(
            HttpClient httpClient,
            IConfiguration configuration,
            NgocMaiTravelDbContext context,
            ILoggerService loggerService,
            IHttpContextAccessor httpContextAccessor)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _loggerService = loggerService ?? throw new ArgumentNullException(nameof(loggerService));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));

            _baseUrl = _configuration["ESimBlue:BaseUrl"] ?? "https://dev-api.peacom.co";
            _apiKey = _configuration["ESimBlue:ApiKey"] ?? "b0159970-80bd-4685-b619-dce33796862a";

            _httpClient.BaseAddress = new Uri(_baseUrl);
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        public async Task<ApiResult<PagedResult<ESimPlanVM>>> SearchPlansAsync(ESimSearchRequest request)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");

            try
            {
                await LogRequestAsync("search_plans", JsonSerializer.Serialize(request), traceId);

                // Build query parameters
                var queryParams = new List<string>();
                if (!string.IsNullOrEmpty(request.Country))
                    queryParams.Add($"country={Uri.EscapeDataString(request.Country)}");
                if (!string.IsNullOrEmpty(request.Region))
                    queryParams.Add($"region={Uri.EscapeDataString(request.Region)}");
                if (request.DataAmount.HasValue)
                    queryParams.Add($"data={request.DataAmount}");
                if (request.ValidityDays.HasValue)
                    queryParams.Add($"validity={request.ValidityDays}");
                if (request.MinPrice.HasValue)
                    queryParams.Add($"minPrice={request.MinPrice}");
                if (request.MaxPrice.HasValue)
                    queryParams.Add($"maxPrice={request.MaxPrice}");

                var queryString = queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
                var endpoint = $"/api/v1/plans{queryString}";

                var response = await _httpClient.GetAsync(endpoint);
                var responseContent = await response.Content.ReadAsStringAsync();

                stopwatch.Stop();

                if (response.IsSuccessStatusCode)
                {
                    // Parse response and convert to our models
                    var plans = ParsePlansResponse(responseContent);
                    var pagedResult = new PagedResult<ESimPlanVM>
                    {
                        Items = plans.Skip((request.PageIndex - 1) * request.PageSize).Take(request.PageSize).ToList(),
                        TotalRecords = plans.Count,
                        PageIndex = request.PageIndex,
                        PageSize = request.PageSize
                    };

                    await LogResponseAsync("search_plans", responseContent, "success", traceId, stopwatch.Elapsed, endpoint, (int)response.StatusCode);
                    return new ApiSuccessResult<PagedResult<ESimPlanVM>>(pagedResult);
                }
                else
                {
                    await LogResponseAsync("search_plans", responseContent, "error", traceId, stopwatch.Elapsed, endpoint, (int)response.StatusCode, $"HTTP {response.StatusCode}");
                    return new ApiErrorResult<PagedResult<ESimPlanVM>>($"API Error: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                await LogResponseAsync("search_plans", null, "error", traceId, stopwatch.Elapsed, "/api/v1/plans", 0, ex.Message);
                return new ApiErrorResult<PagedResult<ESimPlanVM>>($"Exception: {ex.Message}");
            }
        }

        public async Task<ApiResult<List<ESimCountryVM>>> GetCountriesAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");

            try
            {
                await LogRequestAsync("get_countries", "{}", traceId);

                var response = await _httpClient.GetAsync("/api/v1/countries");
                var responseContent = await response.Content.ReadAsStringAsync();

                stopwatch.Stop();

                if (response.IsSuccessStatusCode)
                {
                    var countries = ParseCountriesResponse(responseContent);
                    await LogResponseAsync("get_countries", responseContent, "success", traceId, stopwatch.Elapsed, "/api/v1/countries", (int)response.StatusCode);
                    return new ApiSuccessResult<List<ESimCountryVM>>(countries);
                }
                else
                {
                    await LogResponseAsync("get_countries", responseContent, "error", traceId, stopwatch.Elapsed, "/api/v1/countries", (int)response.StatusCode, $"HTTP {response.StatusCode}");
                    return new ApiErrorResult<List<ESimCountryVM>>($"API Error: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                await LogResponseAsync("get_countries", null, "error", traceId, stopwatch.Elapsed, "/api/v1/countries", 0, ex.Message);
                return new ApiErrorResult<List<ESimCountryVM>>($"Exception: {ex.Message}");
            }
        }

        public async Task<ApiResult<ESimPlanVM>> GetPlanDetailsAsync(string planId)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");

            try
            {
                await LogRequestAsync("get_plan_details", JsonSerializer.Serialize(new { planId }), traceId);

                var response = await _httpClient.GetAsync($"/api/v1/plans/{planId}");
                var responseContent = await response.Content.ReadAsStringAsync();

                stopwatch.Stop();

                if (response.IsSuccessStatusCode)
                {
                    var plan = ParsePlanDetailsResponse(responseContent);
                    await LogResponseAsync("get_plan_details", responseContent, "success", traceId, stopwatch.Elapsed, $"/api/v1/plans/{planId}", (int)response.StatusCode);
                    return new ApiSuccessResult<ESimPlanVM>(plan);
                }
                else
                {
                    await LogResponseAsync("get_plan_details", responseContent, "error", traceId, stopwatch.Elapsed, $"/api/v1/plans/{planId}", (int)response.StatusCode, $"HTTP {response.StatusCode}");
                    return new ApiErrorResult<ESimPlanVM>($"API Error: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                await LogResponseAsync("get_plan_details", null, "error", traceId, stopwatch.Elapsed, $"/api/v1/plans/{planId}", 0, ex.Message);
                return new ApiErrorResult<ESimPlanVM>($"Exception: {ex.Message}");
            }
        }

        // Placeholder implementations for other interface methods
        public async Task<ApiResult<ESimOrderVM>> CreateOrderAsync(ESimOrderRequest request)
        {
            // Implementation will be added based on ESimBlue API documentation
            await Task.Delay(1);
            return new ApiErrorResult<ESimOrderVM>("Not implemented yet");
        }

        public async Task<ApiResult<ESimOrderVM>> GetOrderStatusAsync(string orderId)
        {
            await Task.Delay(1);
            return new ApiErrorResult<ESimOrderVM>("Not implemented yet");
        }

        public async Task<ApiResult<bool>> CancelOrderAsync(string orderId)
        {
            await Task.Delay(1);
            return new ApiErrorResult<bool>("Not implemented yet");
        }

        public async Task<ApiResult<ESimActivationVM>> ActivateESimAsync(ESimActivationRequest request)
        {
            await Task.Delay(1);
            return new ApiErrorResult<ESimActivationVM>("Not implemented yet");
        }

        public async Task<ApiResult<ESimActivationVM>> GetActivationStatusAsync(string orderId)
        {
            await Task.Delay(1);
            return new ApiErrorResult<ESimActivationVM>("Not implemented yet");
        }

        public async Task<ApiResult<string>> CreatePaymentUrlAsync(string orderId, string paymentMethod)
        {
            await Task.Delay(1);
            return new ApiErrorResult<string>("Not implemented yet");
        }

        public async Task<ApiResult<bool>> ConfirmPaymentAsync(string orderId, string transactionId)
        {
            await Task.Delay(1);
            return new ApiErrorResult<bool>("Not implemented yet");
        }

        public async Task<ApiResult<PagedResult<ESimLogVM>>> GetLogsAsync(ESimLogSearchRequest request)
        {
            await Task.Delay(1);
            return new ApiErrorResult<PagedResult<ESimLogVM>>("Not implemented yet");
        }

        public async Task<ApiResult<ESimStatsVM>> GetStatsAsync(DateTime fromDate, DateTime toDate)
        {
            await Task.Delay(1);
            return new ApiErrorResult<ESimStatsVM>("Not implemented yet");
        }

        // Helper methods for logging and parsing
        private async Task LogRequestAsync(string action, string requestData, string traceId)
        {
            try
            {
                var log = new tblESimLog
                {
                    Id = Guid.NewGuid(),
                    Action = action,
                    RequestData = requestData,
                    Status = "pending",
                    Timestamp = DateTime.Now,
                    TraceId = traceId,
                    IpAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString(),
                    UserAgent = _httpContextAccessor.HttpContext?.Request?.Headers["User-Agent"].ToString()
                };

                _context.tblESimLogs.Add(log);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Failed to log ESim request: {ex.Message}", traceId, "", false);
            }
        }

        private async Task LogResponseAsync(string action, string? responseData, string status, string traceId, TimeSpan duration, string endpoint, int statusCode, string? errorMessage = null)
        {
            try
            {
                var log = await _context.tblESimLogs.FirstOrDefaultAsync(x => x.TraceId == traceId && x.Action == action);
                if (log != null)
                {
                    log.ResponseData = responseData;
                    log.Status = status;
                    log.Duration = duration;
                    log.ApiEndpoint = endpoint;
                    log.HttpStatusCode = statusCode;
                    log.ErrorMessage = errorMessage;

                    _context.tblESimLogs.Update(log);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Failed to log ESim response: {ex.Message}", traceId, "", false);
            }
        }

        private List<ESimPlanVM> ParsePlansResponse(string responseContent)
        {
            // Mock implementation - replace with actual parsing based on ESimBlue API response format
            return new List<ESimPlanVM>
            {
                new ESimPlanVM
                {
                    PlanId = "plan_001",
                    PlanName = "Vietnam 30 Days 10GB",
                    Country = "Vietnam",
                    CountryCode = "VN",
                    Region = "Asia",
                    DataAmount = 10240, // 10GB in MB
                    IsUnlimited = false,
                    ValidityDays = 30,
                    Price = 500000,
                    Currency = "VND",
                    Provider = "ESimBlue",
                    Description = "High-speed data plan for Vietnam",
                    Features = new List<string> { "4G/5G", "Hotspot", "No roaming charges" },
                    CoverageArea = "Nationwide",
                    NetworkType = "4G/5G",
                    IsAvailable = true
                }
            };
        }

        private List<ESimCountryVM> ParseCountriesResponse(string responseContent)
        {
            // Mock implementation
            return new List<ESimCountryVM>
            {
                new ESimCountryVM
                {
                    CountryCode = "VN",
                    CountryName = "Vietnam",
                    Region = "Asia",
                    AvailablePlans = 15,
                    MinPrice = 200000,
                    MaxPrice = 1000000,
                    Currency = "VND",
                    IsPopular = true
                }
            };
        }

        private ESimPlanVM ParsePlanDetailsResponse(string responseContent)
        {
            // Mock implementation
            return new ESimPlanVM
            {
                PlanId = "plan_001",
                PlanName = "Vietnam 30 Days 10GB",
                Country = "Vietnam",
                CountryCode = "VN",
                Region = "Asia",
                DataAmount = 10240,
                IsUnlimited = false,
                ValidityDays = 30,
                Price = 500000,
                Currency = "VND",
                Provider = "ESimBlue",
                Description = "High-speed data plan for Vietnam with nationwide coverage",
                Features = new List<string> { "4G/5G", "Hotspot", "No roaming charges", "Instant activation" },
                CoverageArea = "Nationwide including major cities and tourist areas",
                NetworkType = "4G/5G",
                IsAvailable = true
            };
        }
    }
}
