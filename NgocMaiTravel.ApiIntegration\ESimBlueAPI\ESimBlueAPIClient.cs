﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using NgocMaiTravel.ApiIntegration.Common.Logger;
using NgocMaiTravel.ApiIntegration.Esim;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace NgocMaiTravel.ApiIntegration.ESimBlueAPI
{
    public class ESimBlueAPIClient : IESimBlueAPIClient
    {
        private readonly HttpClient _httpClient;
        private readonly NgocMaiTravelDbContext _context;
        private readonly ILoggerService _loggerService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IConfiguration _configuration;

        public ESimBlueAPIClient(
            HttpClient httpClient,
            NgocMaiTravelDbContext context,
            ILoggerService loggerService,
            IHttpContextAccessor httpContextAccessor,
            IConfiguration configuration)
        {
            _httpClient = httpClient;
            _context = context;
            _loggerService = loggerService;
            _httpContextAccessor = httpContextAccessor;
            _configuration = configuration;

            // Initialize ESimBlue client with base URL and other configurations
            var baseUrl = configuration["ESimBlue:BaseUrl"] ?? "https://dev-api.peacom.co";
            var apiKey = configuration["ESimBlue:ApiKey"];
            if (!string.IsNullOrEmpty(apiKey))
            {
                httpClient.DefaultRequestHeaders.Add("apikey", apiKey);
            }
            _httpClient.BaseAddress = new Uri(baseUrl);

        }

        public async Task<ApiResult<BalanceRp>> GetBalanceAsync()
        {
            return await CallApiAsync<BalanceRp>(
                HttpMethod.Get,
                "/eip/partner/company/balance",
                action: "get_balance"
            );
        }

        public async Task<ApiResult<EsimPackagesRp>> GetAllDataPackagesAsync(EsimPackagesRq rq)
        {
            var queryParams = new List<string>();
            if (!string.IsNullOrEmpty(rq.type)) queryParams.Add($"type={rq.type}");
            if (!string.IsNullOrEmpty(rq.sku)) queryParams.Add($"sku={rq.sku}");
            if (!string.IsNullOrEmpty(rq.locationCode)) queryParams.Add($"locationCode={rq.locationCode}");
            if (!string.IsNullOrEmpty(rq.unnamed)) queryParams.Add($"unnamed={rq.unnamed}");
            var endpoint = $"/eip/partner/esim/packages?{string.Join("&", queryParams)}";
            return await CallApiAsync<EsimPackagesRp>(
                HttpMethod.Get,
                endpoint,
                action: "get_all_data_packages"
            );
        }

 



  private async Task<ApiResult<TResponse>> CallApiAsync<TResponse>(
            HttpMethod method,
            string endpoint,
            object? body = null,
            string? action = null,
            string? traceId = null,
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            traceId ??= Guid.NewGuid().ToString("N");
            try
            {
                string requestData = body != null ? JsonSerializer.Serialize(body) : "{}";
                if (!string.IsNullOrEmpty(action))
                    await LogRequestAsync(action, requestData, traceId);

                var request = new HttpRequestMessage(method, endpoint);
                if (body != null)
                {
                    request.Content = new StringContent(requestData, System.Text.Encoding.UTF8, "application/json");
                }

                var response = await _httpClient.SendAsync(request, cancellationToken);
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

                stopwatch.Stop();

                if (response.IsSuccessStatusCode)
                {
                    if (typeof(TResponse) == typeof(EsimPackagesRp))
                    {
                        var result = EsimPackagesRp.FromJson(responseContent);
                        if (result != null)
                        {
                            if (!string.IsNullOrEmpty(action))
                                await LogResponseAsync(action, responseContent, "success", traceId, stopwatch.Elapsed, endpoint, (int)response.StatusCode);

                            return new ApiSuccessResult<TResponse>((TResponse)(object)result);
                        }
                    }
                    else
                    {
                        var result = JsonSerializer.Deserialize<TResponse>(responseContent);
                        if (result != null)
                        {
                            if (!string.IsNullOrEmpty(action))
                                await LogResponseAsync(action, responseContent, "success", traceId, stopwatch.Elapsed, endpoint, (int)response.StatusCode);

                            return new ApiSuccessResult<TResponse>(result);
                        }
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(action))
                        await LogResponseAsync(action, responseContent, "error", traceId, stopwatch.Elapsed, endpoint, (int)response.StatusCode, responseContent);
                    return new ApiErrorResult<TResponse>($"API error: {response.StatusCode} - {responseContent}");
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                if (!string.IsNullOrEmpty(action))
                    await LogResponseAsync(action, null, "error", traceId, stopwatch.Elapsed, endpoint, 0, ex.Message);
                return new ApiErrorResult<TResponse>($"Exception: {ex.Message}");
            }

            // Ensure all code paths return a value
            return new ApiErrorResult<TResponse>("Unhandled error occurred.");
        }

        // Helper methods for logging and parsing
        private async Task LogRequestAsync(string action, string requestData, string traceId)
        {
            try
            {
                var log = new tblESimLog
                {
                    Id = Guid.NewGuid(),
                    Action = action,
                    RequestData = requestData,
                    Status = "pending",
                    Timestamp = DateTime.Now,
                    TraceId = traceId,
                    IpAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString(),
                    UserAgent = _httpContextAccessor.HttpContext?.Request?.Headers["User-Agent"].ToString()
                };

                _context.tblESimLogs.Add(log);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Failed to log ESim request: {ex.Message}", traceId, "", false);
            }
        }

        private async Task LogResponseAsync(string action, string? responseData, string status, string traceId, TimeSpan duration, string endpoint, int statusCode, string? errorMessage = null)
        {
            try
            {
                var log = await _context.tblESimLogs.FirstOrDefaultAsync(x => x.TraceId == traceId && x.Action == action);
                if (log != null)
                {
                    log.ResponseData = responseData;
                    log.Status = status;
                    log.Duration = duration;
                    log.ApiEndpoint = endpoint;
                    log.HttpStatusCode = statusCode;
                    log.ErrorMessage = errorMessage;

                    _context.tblESimLogs.Update(log);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Failed to log ESim response: {ex.Message}", traceId, "", false);
            }
        }


    }
}
