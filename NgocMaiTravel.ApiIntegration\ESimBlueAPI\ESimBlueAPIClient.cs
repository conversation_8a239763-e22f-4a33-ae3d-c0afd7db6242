using Microsoft.Extensions.Configuration;
using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace NgocMaiTravel.ApiIntegration.ESimBlueAPI
{
    /// <summary>
    /// Simple ESimBlue API client - only handles API calls, no logging
    /// </summary>
    public class ESimBlueAPIClient : IESimBlueAPIClient
    {
        private readonly HttpClient _httpClient;

        public ESimBlueAPIClient(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));

            // Initialize ESimBlue client with base URL and other configurations
            var baseUrl = configuration["ESimBlue:BaseUrl"] ?? "https://dev-api.peacom.co";
            var apiKey = configuration["ESimBlue:ApiKey"];

            _httpClient.BaseAddress = new Uri(baseUrl);

            if (!string.IsNullOrEmpty(apiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("apikey", apiKey);
            }
        }

        public async Task<ApiResult<BalanceRp>> GetBalanceAsync()
        {
            return await CallApiAsync<BalanceRp>(HttpMethod.Get, "/eip/partner/company/balance");
        }

        public async Task<ApiResult<EsimPackagesRp>> GetAllDataPackagesAsync(EsimPackagesRq rq)
        {
            var queryParams = new List<string>();
            if (!string.IsNullOrEmpty(rq.type)) queryParams.Add($"type={rq.type}");
            if (!string.IsNullOrEmpty(rq.sku)) queryParams.Add($"sku={rq.sku}");
            if (!string.IsNullOrEmpty(rq.locationCode)) queryParams.Add($"locationCode={rq.locationCode}");
            if (!string.IsNullOrEmpty(rq.unnamed)) queryParams.Add($"unnamed={rq.unnamed}");
            var endpoint = $"/eip/partner/esim/packages?{string.Join("&", queryParams)}";

            return await CallApiAsync<EsimPackagesRp>(HttpMethod.Get, endpoint);
        }

        public async Task<ApiResult<EsimProflieRp>> GetEsimProflieAsync(string serial)
        {
            var endpoint = $"/eip/partner/esim/{serial}/query";
            return await CallApiAsync<EsimProflieRp>(HttpMethod.Get, endpoint);
        }

        public async Task<ApiResult<EsimOrderRp>> CreateOrderAsync(EsimOrderRq orderRequest)
        {
            var endpoint = "/eip/partner/esim/orders";
            return await CallApiAsync<EsimOrderRp>(HttpMethod.Post, endpoint, orderRequest);
        }

        public async Task<ApiResult<EsimUsageRp>> GetEsimUsageAsync(string serial)
        {
            var endpoint = $"/eip/partner/esim/{serial}/usage";
            return await CallApiAsync<EsimUsageRp>(HttpMethod.Get, endpoint);
        }

        public async Task<ApiResult<EsimRedeemRp>> RedeemEsimAsync(string serial)
        {
            var endpoint = $"/eip/partner/esim/{serial}/redeem";
            return await CallApiAsync<EsimRedeemRp>(HttpMethod.Post, endpoint);
        }

        private async Task<ApiResult<TResponse>> CallApiAsync<TResponse>(
            HttpMethod method,
            string endpoint,
            object? body = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var request = new HttpRequestMessage(method, endpoint);

                if (body != null)
                {
                    var requestData = JsonSerializer.Serialize(body);
                    request.Content = new StringContent(requestData, Encoding.UTF8, "application/json");
                }

                var response = await _httpClient.SendAsync(request, cancellationToken);
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

                if (response.IsSuccessStatusCode)
                {
                    if (typeof(TResponse) == typeof(EsimPackagesRp))
                    {
                        var result = EsimPackagesRp.FromJson(responseContent);
                        if (result != null)
                        {
                            return new ApiSuccessResult<TResponse>((TResponse)(object)result);
                        }
                    }
                    else
                    {
                        var result = JsonSerializer.Deserialize<TResponse>(responseContent);
                        if (result != null)
                        {
                            return new ApiSuccessResult<TResponse>(result);
                        }
                    }
                }

                // Handle error response
                var errorMessage = $"API call failed: {response.StatusCode} - {responseContent}";
                return new ApiErrorResult<TResponse>(errorMessage);
            }
            catch (Exception ex)
            {
                var errorMessage = $"API call exception: {ex.Message}";
                return new ApiErrorResult<TResponse>(errorMessage);
            }
        }
    }
}