﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace NgocMaiTravel.Data.Migrations
{
    /// <inheritdoc />
    public partial class addnoteuser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("34c70a56-a3ea-44ef-b222-342c70e7d40c"));

            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("e9172683-329d-4a0d-ae07-77a122286d6a"));

            migrationBuilder.AddColumn<string>(
                name: "UserNote",
                table: "FlightRequests",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedDate",
                table: "AppLockUserHistories",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 5, 13, 53, 26, 960, DateTimeKind.Local).AddTicks(5866),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 7, 5, 9, 2, 27, 823, DateTimeKind.Local).AddTicks(5279));

            migrationBuilder.InsertData(
                table: "AppRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "Description", "Name", "NormalizedName", "TimeCreate", "UserCreateID" },
                values: new object[,]
                {
                    { new Guid("0468ad51-08c4-495c-bbcd-51adce08164a"), null, "Employee role", "employee", "employee", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null },
                    { new Guid("7ca0f1fa-a22c-4c42-af6b-f3bd4a1f7571"), null, "Client role", "client", "client", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null }
                });

            migrationBuilder.UpdateData(
                table: "AppUsers",
                keyColumn: "Id",
                keyValue: new Guid("69bd714f-9576-45ba-b5b7-f00649be00de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "2561198b-3558-41d4-ae24-92f4979fd02e", "AQAAAAIAAYagAAAAECA1eeToLn58Fie0ihR1CJDRaK/xlrZONjo2Ky/YwKIKyhOlMCAhi4BeGEkRwkUJrQ==" });

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab53"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 5, 13, 53, 27, 8, DateTimeKind.Local).AddTicks(5317));

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab54"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 5, 13, 53, 27, 8, DateTimeKind.Local).AddTicks(5354));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("0468ad51-08c4-495c-bbcd-51adce08164a"));

            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("7ca0f1fa-a22c-4c42-af6b-f3bd4a1f7571"));

            migrationBuilder.DropColumn(
                name: "UserNote",
                table: "FlightRequests");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedDate",
                table: "AppLockUserHistories",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 5, 9, 2, 27, 823, DateTimeKind.Local).AddTicks(5279),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 7, 5, 13, 53, 26, 960, DateTimeKind.Local).AddTicks(5866));

            migrationBuilder.InsertData(
                table: "AppRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "Description", "Name", "NormalizedName", "TimeCreate", "UserCreateID" },
                values: new object[,]
                {
                    { new Guid("34c70a56-a3ea-44ef-b222-342c70e7d40c"), null, "Client role", "client", "client", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null },
                    { new Guid("e9172683-329d-4a0d-ae07-77a122286d6a"), null, "Employee role", "employee", "employee", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null }
                });

            migrationBuilder.UpdateData(
                table: "AppUsers",
                keyColumn: "Id",
                keyValue: new Guid("69bd714f-9576-45ba-b5b7-f00649be00de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "513086ba-f07a-40a2-84f5-d22db5cc005f", "AQAAAAIAAYagAAAAEJS65n5BuvB84bWY5dBBj5R2XdWxr/bcFGSU8qhIiP9n3qtMzRe9dDJYDaRhyPbqxQ==" });

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab53"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 5, 9, 2, 27, 868, DateTimeKind.Local).AddTicks(7250));

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab54"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 5, 9, 2, 27, 868, DateTimeKind.Local).AddTicks(7379));
        }
    }
}
