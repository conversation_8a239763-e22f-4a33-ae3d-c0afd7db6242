eSIM Bluesim API
eSIM Bluesim Partner API
Deliver eSIM data plan packages via the eSIM Bluesim HTTP API

Version - V1
1.0 - 5/02/2025 - init:

Initial Release.
1.1 - 15/04/2025 - updates:

Add Get Balance API for company.

Update request params infoCustomer for API /esim/redeem.

Update Whitelist IP for API.

1.2 - 21/04/2025 - updates:

Add location , coverages information for packages BLC-03.

Remove orderPublicId and Add CANCEL status to the Get Data Usage api.

1.2.1 - 28/05/2025 - updates:

Add API to get eSIM profiles by serial.
Environments and Endpoints
Authentication
Request your API keys in your online account

Standards
Country codes use Alpha-2 ISO. Data values are in MB.

Error Codes
AUTHORIZATION
API Key
Key
apikey

Value
eSIMs
Authentication methods
Key in Header
Use the apikey as the API key in header authentication method.

AUTHORIZATION
API Key
This folder is using API Key from collectioneSIM Bluesim API
GET
Get Balance
https://api.peacom.co/eip/partner/company/balance
Returns the current balance information of the company.

Request Parameters
-

Response Paramerters

Name	Type	M/O	Description
companyId	number	mandatory	
balance	number	mandatory	
currency	Object	mandatory
AUTHORIZATION
API Key
Key
apikey

Value
<value>

POST
Cancel Profile
https://api.peacom.co/eip/partner/esim/cancel
Cancel an inactive, unused eSIM profile.

This operation is available under the following conditions:

For product SKU BLC-01:

Only when the eSIM serial status after ordering is PENDING. .
For product SKU BLC-02:

When the eSIM serial status after ordering is PENDING..

Or when the eSIM serial status after ordering is REDEEMED, but the eSIM status is NOT_ACTIVE, meaning the eSIM was redeem but not installed on a device.

Note:
Once the user has installed the eSIM, cancellation is no longer possible.

Request Parameters
Name	Type	M/O	Description	Example
serial	string	optional	Serial number	GX28D0X493
iccid	string	optional	ICCID	894000000000058815
AUTHORIZATION
API Key
This request is using API Key from collectioneSIM Bluesim API
Body
raw (json)
json
{
    "iccid": "",
    "serial":"GX28D0X493"
}
Example Request
Cancel Profile Success
curl
curl --location 'https://api.peacom.co/eip/partner/esim/cancel' \
--data '{
    "iccid": "",
    "serial": "JNJD9QRMBA"
}'
Example Response
Body
Headers (0)
true
POST
Top Up
https://api.peacom.co/eip/partner/esim/topup
After querying the available top-up packages for a package or product, the top-up endpoint allows an existing installed eSIM to be loaded with a new plan. To top up the plan, you need its serial number for eSIM and the compatible top-up data plan SKU.

Request Parameters
Name	Type	M/O	Depcription	Type
serial	string	mandatory	Serial number	
sku	string	mandatory	SKU product	
requestId	string	mandatory	User generated unique transaction ID. Max 50 chars	
AUTHORIZATION
API Key
This request is using API Key from collectioneSIM Bluesim API
Body
raw (json)
json
{
    "requestId": "222222277",
    "serial": "Q1XQWWQJTH",
    "sku": "BLC-01-US-change-7days-1gb-topup"
}