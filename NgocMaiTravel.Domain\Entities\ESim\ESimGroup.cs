using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NgocMaiTravel.Domain.Entities.ESim
{
    /// <summary>
    /// ESim Group Entity (Countries/Regions)
    /// </summary>
    [Table("tblESimGroup")]
    public class ESimGroup
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(10)]
        public string GroupCode { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string GroupName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string GroupType { get; set; } = "COUNTRY"; // COUNTRY, REGION, GLOBAL

        [StringLength(1000)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        public int DisplayOrder { get; set; } = 0;

        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;

        [StringLength(255)]
        public string? CreatedBy { get; set; }

        [StringLength(255)]
        public string? UpdatedBy { get; set; }

        // Navigation properties
        public virtual ICollection<ESimGroupPackage> GroupPackages { get; set; } = new List<ESimGroupPackage>();

        // Helper property to get packages
        [NotMapped]
        public virtual IEnumerable<ESimPackage> Packages => GroupPackages.Where(gp => gp.IsActive).Select(gp => gp.Package);
    }

    /// <summary>
    /// ESim Group Types
    /// </summary>
    public static class ESimGroupTypes
    {
        public const string Country = "COUNTRY";
        public const string Region = "REGION";
        public const string Global = "GLOBAL";
    }
}
