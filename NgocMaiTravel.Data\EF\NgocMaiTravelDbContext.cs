﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using NgocMaiTravel.Data.Configurations;
using NgocMaiTravel.Data.Configurations.AirportMeta;
using NgocMaiTravel.Data.Configurations.Payment;
using NgocMaiTravel.Data.Configurations.ESim;
using NgocMaiTravel.Data.Entities;
using NgocMaiTravel.Data.Entities.AirportMeta;
using NgocMaiTravel.Data.Entities.Payment;
using NgocMaiTravel.Data.Entities.ESim;
using NgocMaiTravel.Data.Extensions;

namespace NgocMaiTravel.Data.EF
{
    //public class NgocMaiTravelDbContext : IdentityDbContext<AppUser, AppRole, Guid>
    public class NgocMaiTravelDbContext : IdentityDbContext<AppUser, AppRole, Guid,
    IdentityUserClaim<Guid>, IdentityUserRole<Guid>, IdentityUserLogin<Guid>, IdentityRoleClaim<Guid>,
    AppUserToken>
    {
        public NgocMaiTravelDbContext(DbContextOptions options) : base(options)
        {

        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Configure IdentityUserRole<TKey> as a keyless entity type
            modelBuilder.Entity<IdentityUserRole<Guid>>().HasNoKey();
            modelBuilder.Entity<IdentityUserLogin<Guid>>().HasNoKey();
            modelBuilder.Entity<IdentityUserClaim<Guid>>().HasNoKey();
            modelBuilder.Entity<IdentityUserToken<Guid>>().HasNoKey();
            modelBuilder.Entity<IdentityRoleClaim<Guid>>().HasNoKey();


            //Configure using Fluent API

            modelBuilder.ApplyConfiguration(new AppConfigConfiguration());


            modelBuilder.ApplyConfiguration(new AppUserConfiguration());
            //modelBuilder.ApplyConfiguration(new AppUserTokenConfiguration());
            modelBuilder.ApplyConfiguration(new AppRoleConfiguration());
            modelBuilder.ApplyConfiguration(new CategoryNewsConfiguration());
            modelBuilder.ApplyConfiguration(new CategoryToursConfiguration());
            modelBuilder.ApplyConfiguration(new NewsConfiguration());
            modelBuilder.ApplyConfiguration(new AddressConfiguration());
            modelBuilder.ApplyConfiguration(new OrderConfiguration());
            modelBuilder.ApplyConfiguration(new OrderDetailConfiguration());
            modelBuilder.ApplyConfiguration(new TourAddressConfiguration());
            modelBuilder.ApplyConfiguration(new TourImageConfiguration());
            modelBuilder.ApplyConfiguration(new TourPriceConfiguration());
            modelBuilder.ApplyConfiguration(new ToursConfiguration());
            modelBuilder.ApplyConfiguration(new TourToursConfiguration());
            modelBuilder.ApplyConfiguration(new CategoryHotelConfiguration());
            modelBuilder.ApplyConfiguration(new HotelConfiguration());
            modelBuilder.ApplyConfiguration(new HotelImageConfiguration());
            modelBuilder.ApplyConfiguration(new RoomConfiguration());
            modelBuilder.ApplyConfiguration(new HotelBookingConfiguration());
            modelBuilder.ApplyConfiguration(new PromotionConfiguration());
            modelBuilder.ApplyConfiguration(new ContactConfiguration());
            modelBuilder.ApplyConfiguration(new VisaRequestConfiguration());
            modelBuilder.ApplyConfiguration(new TourFeedbackConfiguration());
            modelBuilder.ApplyConfiguration(new TourFeedbackImageConfiguration());
            modelBuilder.ApplyConfiguration(new ProvinceConfiguration());
            modelBuilder.ApplyConfiguration(new CountryConfiguration());
            modelBuilder.ApplyConfiguration(new HotelRequestConfiguration());
            modelBuilder.ApplyConfiguration(new FlightRequestConfiguration());
            modelBuilder.ApplyConfiguration(new CategoryAirportConfiguration());
            modelBuilder.ApplyConfiguration(new AirportConfiguration());
            modelBuilder.ApplyConfiguration(new EmailContactConfiguration());
            modelBuilder.ApplyConfiguration(new VisaConfiguration());
            modelBuilder.ApplyConfiguration(new CategoryVisaConfiguration());
            modelBuilder.ApplyConfiguration(new CategoryVisa_VisaConfiguration());
            modelBuilder.ApplyConfiguration(new WorldRegionConfiguration());
            modelBuilder.ApplyConfiguration(new WorldSubregionConfiguration());
            modelBuilder.ApplyConfiguration(new WorldCountryConfiguration());
            modelBuilder.ApplyConfiguration(new WorldStateConfiguration());
            modelBuilder.ApplyConfiguration(new WorldCityConfiguration());
            modelBuilder.ApplyConfiguration(new ScreenConfiguration());
            modelBuilder.ApplyConfiguration(new TranslateConfiguration());
            modelBuilder.ApplyConfiguration(new AddressTranslationConfiguration());
            modelBuilder.ApplyConfiguration(new LanguageConfiguration());
            modelBuilder.ApplyConfiguration(new VisaTranslationConfiguration());
            modelBuilder.ApplyConfiguration(new ScreenRoleConfiguration());
            modelBuilder.ApplyConfiguration(new ScreenUserConfiguration());
            modelBuilder.ApplyConfiguration(new AppGroupUserConfiguration());
            modelBuilder.ApplyConfiguration(new AppLockUserHistoryConfiguration());
            modelBuilder.ApplyConfiguration(new tblAirportConfiguration());
            modelBuilder.ApplyConfiguration(new tblFareRulesConfiguration());
            modelBuilder.ApplyConfiguration(new tblFlightContinentConfiguration());
            modelBuilder.ApplyConfiguration(new tblFlightRegionConfiguration());
            modelBuilder.ApplyConfiguration(new tblFlightCountryConfiguration());
            modelBuilder.ApplyConfiguration(new tblFlightAirportConfiguration());
            modelBuilder.ApplyConfiguration(new tblFlightPlaneConfiguration());
            modelBuilder.ApplyConfiguration(new tblFeatureConfiguration());
            modelBuilder.ApplyConfiguration(new tblApiKeyLibConfiguration());
            modelBuilder.ApplyConfiguration(new tblLibHisConfiguration());
            modelBuilder.ApplyConfiguration(new ChatMessageConfiguration());
            modelBuilder.ApplyConfiguration(new tblMetaTicketIssueFeeConfiguration());
            modelBuilder.ApplyConfiguration(new tblUserActivityLogConfiguration());
            modelBuilder.ApplyConfiguration(new tblGDSConfiguration());
            modelBuilder.ApplyConfiguration(new tblUserGDSConfiguration());
            modelBuilder.ApplyConfiguration(new tblPaymentFeeConfigConfiguration());
            modelBuilder.ApplyConfiguration(new tblVoucherConfiguration());
            modelBuilder.ApplyConfiguration(new tblESimOrderConfiguration());
            modelBuilder.ApplyConfiguration(new tblESimLogConfiguration());

            #region ESim caching entities
            modelBuilder.ApplyConfiguration(new ESimGroupConfiguration());
            modelBuilder.ApplyConfiguration(new ESimPackageConfiguration());
            modelBuilder.ApplyConfiguration(new ESimGroupPackageConfiguration());
            modelBuilder.ApplyConfiguration(new ESimSyncLogConfiguration());
            #endregion

            #region meta airport
            modelBuilder.ApplyConfiguration(new tblMetaAirportTranslateConfiguration());
            modelBuilder.ApplyConfiguration(new tblMetaContinentConfiguration());
            modelBuilder.ApplyConfiguration(new tblMetaCountryConfiguration());
            modelBuilder.ApplyConfiguration(new tblMetaOurAirportConfiguration());
            modelBuilder.ApplyConfiguration(new tblMetaRegionConfiguration());
            modelBuilder.ApplyConfiguration(new tblMetaSubContinentConfiguration());
            #endregion


            modelBuilder.Entity<IdentityUserClaim<Guid>>().ToTable("AppUserClaims");
            modelBuilder.Entity<IdentityUserRole<Guid>>().ToTable("AppUserRoles").HasKey(x => new { x.UserId, x.RoleId });
            modelBuilder.Entity<IdentityUserLogin<Guid>>().ToTable("AppUserLogins").HasKey(x => x.UserId);

            modelBuilder.Entity<IdentityRoleClaim<Guid>>().ToTable("AppRoleClaims");

            modelBuilder.Entity<IdentityUserToken<Guid>>().ToTable("AppUserTokens").HasKey(x => new { x.UserId, x.LoginProvider, x.Name });
            modelBuilder.Entity<AppUserToken>().Property(x => x.Created).HasColumnType("datetime").IsRequired();
            modelBuilder.Entity<AppUserToken>().Property(x => x.Expires).HasColumnType("datetime").IsRequired();

            modelBuilder.Seed();
        }
        // AppUserClaim, AppUserRole, AppUserLogin, AppRoleClaim, AppUserToken



        public DbSet<AppConfig> AppConfigs { get; set; }
        //public DbSet<AppUserToken> AppUserTokens { get; set; }
        //public DbSet<AppUserToken> AppUserTokens { get; set; }
        public virtual DbSet<Address> Addresses { get; set; }
        public DbSet<CategoryNews> CategoryNews { get; set; }
        public DbSet<News> News { get; set; }

        public DbSet<CategoryTour> CategoryTours { get; set; }
        public DbSet<Tour> Tours { get; set; }
        public DbSet<TourTour> TourTours { get; set; }


        public virtual DbSet<Order> Orders { get; set; }

        public virtual DbSet<OrderDetail> OrderDetails { get; set; }

        public virtual DbSet<TourAddress> TourAddresses { get; set; }

        public virtual DbSet<TourImage> TourImages { get; set; }

        public virtual DbSet<TourPrice> TourPrices { get; set; }

        public virtual DbSet<CategoryHotel> CategoryHotels { get; set; }
        public virtual DbSet<Hotel> Hotels { get; set; }

        public virtual DbSet<HotelImage> HotelImages { get; set; }

        public virtual DbSet<Room> Rooms { get; set; }
        public virtual DbSet<HotelBooking> HotelBookings { get; set; }
        public virtual DbSet<Promotion> Promotions { get; set; }
        public virtual DbSet<Contact> Contacts { get; set; }
        public virtual DbSet<VisaRequest> VisaRequests { get; set; }
        public virtual DbSet<TourFeedback> TourFeedbacks { get; set; }
        public virtual DbSet<TourFeedbackImage> TourFeedbackImages { get; set; }
        public virtual DbSet<Province> Provinces { get; set; }
        public virtual DbSet<Country> Countries { get; set; }
        public virtual DbSet<HotelRequest> HotelRequests { get; set; }
        public virtual DbSet<FlightRequest> FlightRequests { get; set; }
        public virtual DbSet<CategoryAirport> CategoryAirports { get; set; }
        public virtual DbSet<Airport> Airports { get; set; }
        public virtual DbSet<EmailContact> EmailContacts { get; set; }
        public virtual DbSet<Visa> Visas { get; set; }
        public virtual DbSet<CategoryVisa> CategoryVisas { get; set; }
        public virtual DbSet<CategoryVisa_Visa> CategoryVisa_Visas { get; set; }
        public virtual DbSet<WorldRegion> WorldRegions { get; set; }
        public virtual DbSet<WorldSubregion> WorldSubregions { get; set; }
        public virtual DbSet<WorldCountry> WorldCountries { get; set; }
        public virtual DbSet<WorldState> WorldStates { get; set; }
        public virtual DbSet<WorldCity> WorldCities { get; set; }
        public virtual DbSet<Screen> Screens { get; set; }
        public virtual DbSet<ScreenRole> ScreenRoles { get; set; }
        public virtual DbSet<ScreenUser> ScreenUsers { get; set; }
        public DbSet<AppGroupUser> GroupUsers { get; set; }
        public DbSet<AppLockUserHistory> AppLockUserHistories { get; set; }
        public virtual DbSet<Translate> Translates { get; set; }
        public virtual DbSet<AddressTranslation> AddressTranslations { get; set; }
        public virtual DbSet<Language> Languages { get; set; }
        public virtual DbSet<VisaTranslation> VisaTranslations { get; set; }
        public virtual DbSet<tblAirport> tblAirports { get; set; }
        public virtual DbSet<tblFareRule> tblFareRules { get; set; }

        //flight location
        public virtual DbSet<tblFlightContinent> tblFlightContinents { get; set; }
        public virtual DbSet<tblFlightRegion> tblFlightRegions { get; set; }
        public virtual DbSet<tblFlightCountry> tblFlightCountries { get; set; }
        public virtual DbSet<tblFlightAirport> tblFlightAirports { get; set; }
        public virtual DbSet<tblFlightPlane> tblFlightPlanes { get; set; }
        public virtual DbSet<tblFeature> tblFeatures { get; set; }
        public virtual DbSet<tblApiKeyLib> tblApiKeyLibs { get; set; }
        public virtual DbSet<tblLibHis> tblLibHies { get; set; }

        public virtual DbSet<tblMetaAirportTranslate> tblMetaAirportTranslates { get; set; }
        public virtual DbSet<tblMetaContinent> tblMetaContinents { get; set; }
        public virtual DbSet<tblMetaCountry> tblMetaCountries { get; set; }
        public virtual DbSet<tblMetaOurAirport> tblMetaOurAirports { get; set; }
        public virtual DbSet<tblMetaRegion> tblMetaRegions { get; set; }
        public virtual DbSet<tblMetaSubContinent> tblMetaSubContinents { get; set; }
        public virtual DbSet<ChatMessage> ChatMessages { get; set; }
        public virtual DbSet<tblMetaTicketIssueFee> tblMetaTicketIssueFees { get; set; }
        public virtual DbSet<tblUserActivityLog> tblUserActivityLogs { get; set; }
        public virtual DbSet<tblGDS> tblGDSs { get; set; }
        public virtual DbSet<tblUserGDS> tblUserGDSs { get; set; }
        public virtual DbSet<tblPaymentFeeConfig> tblPaymentFeeConfigs { get; set; }
        public virtual DbSet<tblVoucher> tblVouchers { get; set; }
        public virtual DbSet<tblESimOrder> tblESimOrders { get; set; }
        public virtual DbSet<tblESimLog> tblESimLogs { get; set; }

        // New ESim caching entities
        public virtual DbSet<ESimGroup> ESimGroups { get; set; }
        public virtual DbSet<ESimPackage> ESimPackages { get; set; }
        public virtual DbSet<ESimGroupPackage> ESimGroupPackages { get; set; }
        public virtual DbSet<ESimSyncLog> ESimSyncLogs { get; set; }

    }
}
