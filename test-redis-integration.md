# Redis Integration Test Guide

## Prerequisites

1. **Redis Server Running**
   ```bash
   # Check if Redis is running
   redis-cli ping
   # Should return: PONG
   ```

2. **Application Configuration**
   ```json
   // appsettings.json
   {
     "Redis": {
       "Configuration": "localhost:6379",
       "InstanceName": "NgocMaiTravel"
     }
   }
   ```

## Test Steps

### 1. Start Application
```bash
cd NgocMaiTravel.BackendApi
dotnet run
```

### 2. Test Redis Connection
```bash
curl -X GET "https://localhost:7001/api/TestRedis/test-connection"
```

**Expected Response:**
```json
{
  "success": true,
  "firstCall": {
    "message": "Redis connection test successful",
    "timestamp": "2024-01-15T10:30:00Z",
    "testKey": "connection-test-*********"
  },
  "secondCall": {
    "message": "Redis connection test successful", 
    "timestamp": "2024-01-15T10:30:00Z",
    "testKey": "connection-test-*********"
  },
  "cacheWorking": true,
  "note": "If cache is working, both calls should return identical results"
}
```

### 3. Test Basic Deduplication
```bash
# First call - should take 2 seconds
curl -X GET "https://localhost:7001/api/TestRedis/test-deduplication?key=test1&delay=2000"

# Second call immediately - should return cached result instantly
curl -X GET "https://localhost:7001/api/TestRedis/test-deduplication?key=test1&delay=2000"
```

### 4. Test Concurrent Requests
```bash
curl -X GET "https://localhost:7001/api/TestRedis/test-concurrent?key=concurrent-test"
```

### 5. Check Cache Statistics
```bash
curl -X GET "https://localhost:7001/api/TestRedis/stats"
```

**Expected Response:**
```json
{
  "success": true,
  "stats": {
    "totalRequests": 15,
    "cacheHits": 8,
    "deduplications": 3,
    "cacheHitRate": 53.33,
    "deduplicationRate": 20.0,
    "activeSemaphores": 2,
    "redisStats": {
      "activeCacheEntries": 5,
      "storedStats": { ... }
    }
  }
}
```

### 6. Test ESim Service with Redis Caching
```bash
# First search - should hit ESimBlue API
curl -X GET "https://localhost:7001/api/ESim/search?locationCode=VN&type=data"

# Second search with same parameters - should return cached result
curl -X GET "https://localhost:7001/api/ESim/search?locationCode=VN&type=data"
```

### 7. Clear Cache Test
```bash
# Clear specific cache
curl -X DELETE "https://localhost:7001/api/TestRedis/clear/test1"

# Clear all cache
curl -X DELETE "https://localhost:7001/api/TestRedis/clear-all"
```

## Verification with Redis CLI

### Check Cache Keys
```bash
redis-cli
> KEYS esim:dedup:cache:*
> GET esim:dedup:cache:test1
> TTL esim:dedup:cache:test1
```

### Monitor Redis Operations
```bash
redis-cli MONITOR
# Then run API calls to see Redis operations in real-time
```

### Check Statistics
```bash
redis-cli
> GET esim:dedup:stats:current
```

## Expected Behaviors

### ✅ Success Indicators

1. **Cache Hits**: Second identical request returns instantly
2. **Redis Storage**: Keys visible in Redis CLI
3. **TTL Management**: Keys expire after configured time
4. **Statistics**: Accurate cache hit/miss ratios
5. **Fallback**: Service works even if Redis is down

### ❌ Failure Indicators

1. **No Cache**: Every request takes full processing time
2. **Redis Errors**: Connection timeout or serialization errors
3. **Memory Leaks**: Semaphore count keeps growing
4. **Data Loss**: Cache doesn't persist between requests

## Troubleshooting

### Redis Connection Issues
```bash
# Check Redis status
sudo systemctl status redis

# Check Redis logs
sudo journalctl -u redis

# Test connection
redis-cli ping
```

### Application Logs
```bash
# Check for Redis-related errors
grep -i redis logs/application.log
grep -i "deduplication" logs/application.log
```

### Performance Issues
```bash
# Monitor Redis memory usage
redis-cli INFO memory

# Check slow queries
redis-cli SLOWLOG GET 10
```

## Load Testing

### Concurrent Requests Test
```bash
# Use Apache Bench to test concurrent requests
ab -n 100 -c 10 "https://localhost:7001/api/TestRedis/test-deduplication?key=load-test&delay=1000"
```

### Expected Results:
- First 10 requests should execute (one per concurrent connection)
- Remaining 90 requests should get cached results
- Total time should be ~1 second (not 100 seconds)

## Cleanup

### Clear Test Data
```bash
# Clear all test cache entries
curl -X DELETE "https://localhost:7001/api/TestRedis/clear-all"

# Or manually in Redis
redis-cli
> FLUSHDB
```

## Success Criteria

✅ **Redis Integration Working** if:
1. Cache hits show in statistics
2. Identical requests return same results instantly
3. Keys are visible in Redis CLI
4. TTL expiration works correctly
5. Fallback works when Redis is unavailable
6. ESim service benefits from caching
7. No memory leaks in semaphores
8. Performance improves under load
