using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities.ESim;

namespace NgocMaiTravel.Application.Catalog.ESim.Repositories
{
    /// <summary>
    /// Repository implementation for ESim entities
    /// </summary>
    public class ESimRepository : IESimRepository
    {
        private readonly NgocMaiTravelDbContext _context;
        private readonly ILogger<ESimRepository> _logger;

        public ESimRepository(NgocMaiTravelDbContext context, ILogger<ESimRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<bool> HasDataAsync()
        {
            try
            {
                return await _context.ESimGroups.AnyAsync(g => g.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if ESim data exists");
                return false;
            }
        }

        public async Task<DateTime?> GetLastSyncDateAsync()
        {
            try
            {
                var lastSuccessfulSync = await _context.ESimSyncLogs
                    .Where(l => l.Status == "SUCCESS")
                    .OrderByDescending(l => l.StartTime)
                    .FirstOrDefaultAsync();

                return lastSuccessfulSync?.StartTime;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting last sync date");
                return null;
            }
        }

        public async Task<IEnumerable<ESimGroup>> GetAllGroupsAsync()
        {
            try
            {
                return await _context.ESimGroups
                    .Include(g => g.GroupPackages)
                    .ThenInclude(gp => gp.Package)
                    .Where(g => g.IsActive)
                    .OrderBy(g => g.DisplayOrder)
                    .ThenBy(g => g.GroupName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all ESim groups");
                return new List<ESimGroup>();
            }
        }

        public async Task<ESimGroup?> GetGroupByCodeAsync(string groupCode)
        {
            try
            {
                return await _context.ESimGroups
                    .Include(g => g.GroupPackages)
                    .ThenInclude(gp => gp.Package)
                    .FirstOrDefaultAsync(g => g.GroupCode == groupCode && g.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting ESim group by code: {GroupCode}", groupCode);
                return null;
            }
        }

        public async Task<IEnumerable<ESimPackage>> GetPackagesByGroupCodeAsync(string groupCode)
        {
            try
            {
                return await _context.ESimGroupPackages
                    .Include(gp => gp.Package)
                    .Include(gp => gp.Group)
                    .Where(gp => gp.Group.GroupCode == groupCode && 
                                gp.IsActive && 
                                gp.Package.IsActive && 
                                gp.Group.IsActive)
                    .OrderBy(gp => gp.DisplayOrder)
                    .ThenBy(gp => gp.Package.Price)
                    .Select(gp => gp.Package)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting packages by group code: {GroupCode}", groupCode);
                return new List<ESimPackage>();
            }
        }

        public async Task<ESimPackage?> GetPackageByIdAsync(string packageId)
        {
            try
            {
                return await _context.ESimPackages
                    .Include(p => p.GroupPackages)
                    .ThenInclude(gp => gp.Group)
                    .FirstOrDefaultAsync(p => p.PackageId == packageId && p.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting package by ID: {PackageId}", packageId);
                return null;
            }
        }

        public async Task<ESimGroup> CreateOrUpdateGroupAsync(ESimGroup group)
        {
            try
            {
                var existingGroup = await _context.ESimGroups
                    .FirstOrDefaultAsync(g => g.GroupCode == group.GroupCode);

                if (existingGroup != null)
                {
                    // Update existing group
                    existingGroup.GroupName = group.GroupName;
                    existingGroup.GroupType = group.GroupType;
                    existingGroup.Description = group.Description;
                    existingGroup.IsActive = group.IsActive;
                    existingGroup.DisplayOrder = group.DisplayOrder;
                    existingGroup.UpdatedDate = DateTime.UtcNow;
                    existingGroup.UpdatedBy = group.UpdatedBy;

                    _context.ESimGroups.Update(existingGroup);
                    await _context.SaveChangesAsync();
                    return existingGroup;
                }
                else
                {
                    // Create new group
                    group.CreatedDate = DateTime.UtcNow;
                    group.UpdatedDate = DateTime.UtcNow;
                    
                    _context.ESimGroups.Add(group);
                    await _context.SaveChangesAsync();
                    return group;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating/updating ESim group: {GroupCode}", group.GroupCode);
                throw;
            }
        }

        public async Task<ESimPackage> CreateOrUpdatePackageAsync(ESimPackage package)
        {
            try
            {
                var existingPackage = await _context.ESimPackages
                    .FirstOrDefaultAsync(p => p.PackageId == package.PackageId);

                if (existingPackage != null)
                {
                    // Update existing package
                    existingPackage.Sku = package.Sku;
                    existingPackage.PackageName = package.PackageName;
                    existingPackage.Description = package.Description;
                    existingPackage.DataAmount = package.DataAmount;
                    existingPackage.ValidityDays = package.ValidityDays;
                    existingPackage.Price = package.Price;
                    existingPackage.Currency = package.Currency;
                    existingPackage.OriginalPrice = package.OriginalPrice;
                    existingPackage.DiscountPercent = package.DiscountPercent;
                    existingPackage.PackageType = package.PackageType;
                    existingPackage.NetworkType = package.NetworkType;
                    existingPackage.IsUnlimited = package.IsUnlimited;
                    existingPackage.IsTopUpSupported = package.IsTopUpSupported;
                    existingPackage.IsActive = package.IsActive;
                    existingPackage.ExternalData = package.ExternalData;
                    existingPackage.UpdatedDate = DateTime.UtcNow;
                    existingPackage.LastSyncDate = DateTime.UtcNow;
                    existingPackage.UpdatedBy = package.UpdatedBy;

                    _context.ESimPackages.Update(existingPackage);
                    await _context.SaveChangesAsync();
                    return existingPackage;
                }
                else
                {
                    // Create new package
                    package.CreatedDate = DateTime.UtcNow;
                    package.UpdatedDate = DateTime.UtcNow;
                    package.LastSyncDate = DateTime.UtcNow;
                    
                    _context.ESimPackages.Add(package);
                    await _context.SaveChangesAsync();
                    return package;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating/updating ESim package: {PackageId}", package.PackageId);
                throw;
            }
        }

        public async Task<ESimGroupPackage> CreateGroupPackageMappingAsync(long groupId, long packageId, int displayOrder = 0)
        {
            try
            {
                var existingMapping = await _context.ESimGroupPackages
                    .FirstOrDefaultAsync(gp => gp.GroupId == groupId && gp.PackageId == packageId);

                if (existingMapping != null)
                {
                    // Update existing mapping
                    existingMapping.DisplayOrder = displayOrder;
                    existingMapping.IsActive = true;
                    existingMapping.UpdatedDate = DateTime.UtcNow;

                    _context.ESimGroupPackages.Update(existingMapping);
                    await _context.SaveChangesAsync();
                    return existingMapping;
                }
                else
                {
                    // Create new mapping
                    var mapping = new ESimGroupPackage
                    {
                        GroupId = groupId,
                        PackageId = packageId,
                        DisplayOrder = displayOrder,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        UpdatedDate = DateTime.UtcNow
                    };

                    _context.ESimGroupPackages.Add(mapping);
                    await _context.SaveChangesAsync();
                    return mapping;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating group-package mapping: GroupId={GroupId}, PackageId={PackageId}", groupId, packageId);
                throw;
            }
        }

        public async Task<int> GetPackageCountAsync()
        {
            try
            {
                return await _context.ESimPackages.CountAsync(p => p.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting package count");
                return 0;
            }
        }

        public async Task<int> GetGroupCountAsync()
        {
            try
            {
                return await _context.ESimGroups.CountAsync(g => g.IsActive);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting group count");
                return 0;
            }
        }

        public async Task<bool> DeleteGroupAsync(string groupCode)
        {
            try
            {
                var group = await _context.ESimGroups
                    .FirstOrDefaultAsync(g => g.GroupCode == groupCode);

                if (group != null)
                {
                    group.IsActive = false;
                    group.UpdatedDate = DateTime.UtcNow;
                    
                    _context.ESimGroups.Update(group);
                    await _context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting ESim group: {GroupCode}", groupCode);
                return false;
            }
        }

        public async Task<bool> DeletePackageAsync(string packageId)
        {
            try
            {
                var package = await _context.ESimPackages
                    .FirstOrDefaultAsync(p => p.PackageId == packageId);

                if (package != null)
                {
                    package.IsActive = false;
                    package.UpdatedDate = DateTime.UtcNow;
                    
                    _context.ESimPackages.Update(package);
                    await _context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting ESim package: {PackageId}", packageId);
                return false;
            }
        }
    }
}
