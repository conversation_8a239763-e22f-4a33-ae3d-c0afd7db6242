using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using NgocMaiTravel.ApiIntegration.Common.Logger;
using NgocMaiTravel.ApiIntegration.Esim;
using NgocMaiTravel.ApiIntegration.ESimBlueAPI;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Catalog.ESim
{
    public class ESimService : IESimService
    {
        private readonly IESimBlueAPIClient _eSimBlueClient;
        private readonly NgocMaiTravelDbContext _context;
        private readonly ILoggerService _loggerService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ESimService(
            IESimBlueAPIClient eSimBlueClient,
            NgocMaiTravelDbContext context,
            ILoggerService loggerService,
            IHttpContextAccessor httpContextAccessor)
        {
            _eSimBlueClient = eSimBlueClient ?? throw new ArgumentNullException(nameof(eSimBlueClient));
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _loggerService = loggerService ?? throw new ArgumentNullException(nameof(loggerService));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        }

        public async Task<ApiResult<BalanceRp>> GetBalanceAsync()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            try
            {
                var result = await _eSimBlueClient.GetBalanceAsync();
                if (result.IsSuccessed)
                {
                    _loggerService.LogResponse(httpContext, $"ESim balance retrieved successfully: {result.ResultObj.Balance}", Guid.NewGuid().ToString(), "", false);
                }
                else
                {
                    _loggerService.LogResponse(httpContext, $"ESim balance retrieval failed: {result.Message}", Guid.NewGuid().ToString(), "", false);
                }
                return result;
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(httpContext, $"ESim balance retrieval exception: {ex.Message}", Guid.NewGuid().ToString(), "", false);
                return new ApiErrorResult<BalanceRp>($"Get balance failed: {ex.Message}");
            }
        }

        public async Task<ApiResult<EsimPackagesRp>> SearchPlansAsync(EsimPackagesRq rq)
        {
            var idTrans = rq.TransID.ToString();
            try
            {
                _loggerService.LogRequest(_httpContextAccessor.HttpContext, $"ESim search request: {JsonConvert.SerializeObject(rq)}", idTrans, "", false);

                var result = await _eSimBlueClient.GetAllDataPackagesAsync(rq);

                if (result.IsSuccessed)
                {
                    _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"ESim search successful. Found {result.ResultObj.PackageList.Length} plans", idTrans, "", false);
                }
                else
                {
                    _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"ESim search failed: {result.Message}", idTrans, "", false);
                }

                return result;
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"ESim search exception: {ex.Message}", idTrans, "", false);
                return new ApiErrorResult<EsimPackagesRp>($"Search failed: {ex.Message}");
            }
        }

        public async Task<ApiResult<EsimProflieRp>> GetEsimProflieAsync(string serial)
        {
            var idTrans = Guid.NewGuid().ToString();
            try
            {
                _loggerService.LogRequest(_httpContextAccessor.HttpContext, $"ESim profile request for serial: {serial}", idTrans, "", false);
                var result = await _eSimBlueClient.GetEsimProflieAsync(serial);
                if (result.IsSuccessed)
                {
                    _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"ESim profile retrieved successfully for serial: {serial}", idTrans, "", false);
                }
                else
                {
                    _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"ESim profile retrieval failed for serial {serial}: {result.Message}", idTrans, "", false);
                }
                return result;
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"ESim profile retrieval exception for serial {serial}: {ex.Message}", idTrans, "", false);
                return new ApiErrorResult<EsimProflieRp>($"Get profile failed: {ex.Message}");
            }

        }


    }
}
