﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NgocMaiTravel.Application.Common.Logging;
using NgocMaiTravel.Application.Common.Services;
using NgocMaiTravel.Application.Catalog.ESim.Repositories;
using NgocMaiTravel.Application.Catalog.ESim.Services;
using NgocMaiTravel.ApiIntegration.ESimBlueAPI;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Catalog.ESim
{
    public class ESimService : IESimService
    {
        private readonly IESimBlueAPIClient _eSimBlueClient;
        private readonly NgocMaiTravelDbContext _context;
        private readonly IESimFileLogger _fileLogger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IRequestDeduplicationService _deduplicationService;
        private readonly ILogger<ESimService> _logger;
        private readonly IESimRepository _eSimRepository;
        private readonly IESimSyncService _eSimSyncService;

        public ESimService(
            IESimBlueAPIClient eSimBlueClient,
            NgocMaiTravelDbContext context,
            IESimFileLogger fileLogger,
            IHttpContextAccessor httpContextAccessor,
            IRequestDeduplicationService deduplicationService,
            ILogger<ESimService> logger,
            IESimRepository eSimRepository,
            IESimSyncService eSimSyncService)
        {
            _eSimBlueClient = eSimBlueClient ?? throw new ArgumentNullException(nameof(eSimBlueClient));
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _fileLogger = fileLogger ?? throw new ArgumentNullException(nameof(fileLogger));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
            _deduplicationService = deduplicationService ?? throw new ArgumentNullException(nameof(deduplicationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _eSimRepository = eSimRepository ?? throw new ArgumentNullException(nameof(eSimRepository));
            _eSimSyncService = eSimSyncService ?? throw new ArgumentNullException(nameof(eSimSyncService));
        }

        public async Task<ApiResult<BalanceRp>> GetBalanceAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");

            try
            {
                // Log request with unique logId
                var logId = Guid.NewGuid();
                await _fileLogger.LogApiCallAsync(
                    action: "get_balance",
                    endpoint: "/eip/partner/company/balance",
                    requestData: "{}",
                    status: "pending",
                    traceId: traceId,
                    logId: logId
                );

                var result = await _eSimBlueClient.GetBalanceAsync();
                stopwatch.Stop();

                if (result.IsSuccessed)
                {
                    // Log successful response
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: JsonConvert.SerializeObject(result.ResultObj),
                        status: "success",
                        duration: stopwatch.Elapsed
                    );
                }
                else
                {
                    // Log failed response
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: result.Message,
                        status: "error",
                        duration: stopwatch.Elapsed,
                        errorMessage: result.Message
                    );
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // Log exception with unique logId
                await _fileLogger.LogApiCallAsync(
                    action: "get_balance",
                    endpoint: "/eip/partner/company/balance",
                    status: "error",
                    duration: stopwatch.Elapsed,
                    errorMessage: ex.Message,
                    traceId: traceId,
                    logId: Guid.NewGuid()
                );

                return new ApiErrorResult<BalanceRp>($"Get balance failed: {ex.Message}");
            }
        }

        public async Task<ApiResult<List<CountryEsimGroup>>> SearchPlansAsync(EsimPackagesRq rq)
        {
            try
            {
                // Check if we have data in database
                var hasData = await _eSimRepository.HasDataAsync();

                if (!hasData)
                {
                    // No data in DB - fetch from API and queue sync to DB
                    _logger.LogInformation("No ESim data in database, fetching from API and queuing sync");

                    var apiResult = await FetchFromApiAndQueueSync(rq);
                    if (apiResult.IsSuccessed)
                    {
                        return apiResult;
                    }

                    // If API fails and no DB data, return error
                    return new ApiErrorResult<List<CountryEsimGroup>>("No ESim data available and API request failed");
                }
                else
                {
                    // Data exists in DB - return from DB and queue background sync
                    _logger.LogInformation("Returning ESim data from database and queuing background sync");

                    var dbResult = await GetFromDatabase(rq);

                    // If DB result is empty, fallback to API
                    if (!dbResult.IsSuccessed || !dbResult.ResultObj.Any())
                    {
                        _logger.LogInformation("Database result empty, falling back to API");
                        return await FetchFromApiAndQueueSync(rq);
                    }

                    // Queue background sync to update data
                    await QueueBackgroundSync(rq);

                    return dbResult;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SearchPlansAsync");
                return new ApiErrorResult<List<CountryEsimGroup>>($"Search failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Fetch data from API and queue sync to database
        /// </summary>
        private async Task<ApiResult<List<CountryEsimGroup>>> FetchFromApiAndQueueSync(EsimPackagesRq rq)
        {
            // Generate unique key for request deduplication
            var requestKey = ESimRequestKeyGenerator.GenerateSearchPlansKey(rq);

            // Use deduplication service to handle concurrent requests
            var result = await _deduplicationService.ExecuteAsync(
                key: requestKey,
                factory: () => ExecuteSearchPlansInternalAsync(rq),
                cacheDurationSeconds: 60 * 60 * 12
            );

            if (result.IsSuccessed)
            {
                // Queue sync to database
                await _eSimSyncService.QueueFullSyncAsync("SearchPlansAsync");

                // Extract custom ESim groups from the result
                var countryMap = await GetCustomESimGroup(result);
                if (countryMap != null)
                {
                    return new ApiSuccessResult<List<CountryEsimGroup>>(countryMap);
                }
                else
                {
                    return new ApiErrorResult<List<CountryEsimGroup>>("No ESim packages found.");
                }
            }
            else
            {
                return new ApiErrorResult<List<CountryEsimGroup>>(result.Message);
            }
        }

        /// <summary>
        /// Sync data from API to database (TODO: Implement when database entities are ready)
        /// </summary>
        private async Task SyncDataFromApiToDatabase()
        {
            // TODO: Implement when database entities are ready
            _logger.LogInformation("SyncDataFromApiToDatabase called but not implemented yet");
            await Task.CompletedTask;
        }



        /// <summary>
        /// Get data from database
        /// </summary>
        private async Task<ApiResult<List<CountryEsimGroup>>> GetFromDatabase(EsimPackagesRq rq)
        {
            try
            {
                // Get groups with packages from database
                var groups = await _eSimRepository.GetAllGroupsAsync();
                var countryMap = new List<CountryEsimGroup>();

                foreach (var group in groups.Where(g => g.IsActive))
                {
                    var packages = await _eSimRepository.GetPackagesByGroupCodeAsync(group.GroupCode);

                    if (packages.Any())
                    {
                        var esimItems = packages.Where(p => p.IsActive).Select(p => new EsimItem
                        {
                            Id = p.PackageId,
                            Sku = p.Sku,
                            PackageName = p.PackageName,
                            Description = p.Description ?? "",
                            DataAmount = p.DataAmount,
                            ValidityDays = p.ValidityDays,
                            Price = p.Price,
                            Currency = p.Currency,
                            OriginalPrice = p.OriginalPrice,
                            DiscountPercent = p.DiscountPercent,
                            PackageType = p.PackageType,
                            NetworkType = p.NetworkType ?? "",
                            IsUnlimited = p.IsUnlimited,
                            IsTopUpSupported = p.IsTopUpSupported,
                            OperatorList = new List<string>()
                        }).ToList();

                        if (esimItems.Any())
                        {
                            countryMap.Add(new CountryEsimGroup
                            {
                                CountryCode = group.GroupCode,
                                CountryName = group.GroupName,
                                Flag = $"/assets/img/countries/{group.GroupCode.ToLower()}.png",
                                Esims = esimItems
                            });
                        }
                    }
                }

                // Sort by country name
                countryMap = countryMap.OrderBy(c => c.CountryName).ToList();

                _logger.LogInformation("Retrieved {Count} country groups with ESim packages from database", countryMap.Count);
                return new ApiSuccessResult<List<CountryEsimGroup>>(countryMap);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting ESim data from database");
                return new ApiErrorResult<List<CountryEsimGroup>>($"Database error: {ex.Message}");
            }
        }

        /// <summary>
        /// Queue background sync to update database
        /// </summary>
        private async Task QueueBackgroundSync(EsimPackagesRq rq)
        {
            try
            {
                // Check if sync is needed (based on last sync time)
                var syncNeeded = await _eSimSyncService.IsSyncNeededAsync(TimeSpan.FromHours(6));

                if (syncNeeded)
                {
                    await _eSimSyncService.QueueIncrementalSyncAsync("SearchPlansAsync_Background");
                    _logger.LogInformation("Queued background sync for ESim data");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to queue background sync");
                // Don't fail the main request if background sync queueing fails
            }
        }

        private async Task<List<CountryEsimGroup>?> GetCustomESimGroup(ApiResult<EsimPackagesRp> esimPackagesRp)
        {
            if (!esimPackagesRp.IsSuccessed) return null;

            var countryMap = new List<CountryEsimGroup>();
            var packages = esimPackagesRp.ResultObj.PackageList;

            // Load full danh sách quốc gia để map code <-> name
            var allCountries = await _context.tblMetaCountries.AsNoTracking()
                .Where(x => x.Name != null && x.Code != null)
                .Select(x => new { Name = x.Name, Code = x.Code })
                .ToListAsync();

            var codeToNameDict = allCountries.ToDictionary(x => x.Code.ToUpper(), x => x.Name);
            var nameToCodeDict = allCountries.ToDictionary(x => x.Name.ToLower(), x => x.Code);

            foreach (var pkg in packages)
            {
                var coverages = pkg.ExtraData?.Operator?.Coverages.CoverageArray;
                if (coverages != null)
                {
                    foreach (var country in coverages)
                    {
                        var code = country.LocationCode;
                        var name = country.LocationName;

                        // Nếu mã là 2 ký tự thì kiểm tra name từ DB
                        if (code.Length == 2 && codeToNameDict.TryGetValue(code.ToUpper(), out var dbName))
                        {
                            name = dbName;
                        }

                        TryAddGroup(countryMap, code, name);
                        AddEsimToGroup(countryMap, code, pkg, country?.OperatorList?.Select(o => o.OperatorName).ToList());
                    }
                }

                var coverageString = pkg.ExtraData?.Operator?.Coverages.String;
                if (!string.IsNullOrEmpty(coverageString))
                {
                    var listCountries = coverageString.Split(',').Select(c => c.Trim()).Where(c => !string.IsNullOrEmpty(c)).ToList();

                    foreach (var countryName in listCountries)
                    {
                        var lowerName = countryName.ToLower();
                        string countryCode;
                        string finalName;

                        // 1. So khớp chính xác theo tên
                        if (nameToCodeDict.TryGetValue(lowerName, out var matchedCode))
                        {
                            countryCode = matchedCode;
                            finalName = codeToNameDict.TryGetValue(matchedCode.ToUpper(), out var dbNameExact) ? dbNameExact : countryName;
                        }
                        else
                        {
                            // 2. Không tìm thấy tên chính xác → thử contains
                            var matchedByContain = allCountries
                                .FirstOrDefault(c => c.Name != null && c.Name.ToLower().Contains(lowerName));

                            if (matchedByContain != null)
                            {
                                countryCode = matchedByContain.Code;
                                finalName = matchedByContain.Name;
                            }
                            else
                            {
                                // 3. Không tìm thấy gì → fallback
                                countryCode = countryName.ToLowerInvariant().Replace(" ", "-");
                                finalName = countryName;
                            }
                        }

                        TryAddGroup(countryMap, countryCode, finalName);
                        AddEsimToGroup(countryMap, countryCode, pkg, null);
                    }

                }
            }

            // Sau khi đã build xong countryMap, tính MinPrice cho từng group
            foreach (var group in countryMap)
            {
                if (group.Esims != null && group.Esims.Count > 0)
                {
                    group.MinPrice = group.Esims.Min(e => e.Price);
                }
                else
                {
                    group.MinPrice = 0;
                }
            }

            return countryMap;
        }


        private void TryAddGroup(List<CountryEsimGroup> countryMap, string code, string name)
        {
            if (!countryMap.Any(x => x.CountryCode == code))
            {
                countryMap.Add(new CountryEsimGroup
                {
                    CountryCode = code,
                    CountryName = name,
                    Flag = $"/assets/img/countries/{code}.png",
                    Esims = new List<EsimItem>()
                });
            }
        }

        private void AddEsimToGroup(List<CountryEsimGroup> countryMap, string code, PackageList pkg, List<string>? operatorList)
        {
            var group = countryMap.FirstOrDefault(x => x.CountryCode == code);
            if (group != null)
            {
                group.Esims.Add(new EsimItem
                {
                    Sku = pkg.Sku,
                    Model = pkg.Model,
                    Regional = pkg.Regional,
                    Name = pkg.Name,
                    Price = pkg.Price,
                    CurrencyCode = pkg.CurrencyCode.ToString(),
                    Speed = pkg.ExtraData?.Speed.ToString(),
                    Duration = pkg.ExtraData?.Duration,
                    Volume = !string.IsNullOrEmpty(pkg.ExtraData?.Volume?.String)
                                ? pkg.ExtraData?.Volume?.String
                                : $"{pkg.ExtraData?.Volume?.Integer}",
                    TypePackage = pkg.TypePackage,
                    DataType = pkg.ExtraData.DataType,
                    SmsStatus = pkg.ExtraData.SmsStatus,
                    ActiveType = pkg.ExtraData.ActiveType,
                    UnusedValidTime = pkg.ExtraData.UnusedValidTime,
                    SupportTopUpType = pkg.ExtraData.SupportTopUpType,
                    OperatorList = operatorList ?? new List<string>()
                });
            }
        }

        /// <summary>
        /// Internal method that actually executes the search plans request - NO LOGGING HERE
        /// </summary>
        private async Task<ApiResult<EsimPackagesRp>> ExecuteSearchPlansInternalAsync(EsimPackagesRq rq)
        {
            try
            {
                // Just call the API client - no logging here to avoid conflicts
                var result = await _eSimBlueClient.GetAllDataPackagesAsync(rq);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SearchPlans API call exception");
                return new ApiErrorResult<EsimPackagesRp>($"Search failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Log search request for individual request tracking
        /// </summary>
        private async Task LogSearchRequestAsync(EsimPackagesRq rq, string traceId, Guid logId)
        {
            try
            {
                var requestData = JsonConvert.SerializeObject(rq);
                await _fileLogger.LogApiCallAsync(
                    action: "search_plans",
                    endpoint: "/eip/partner/esim/packages",
                    requestData: requestData,
                    status: "pending",
                    traceId: traceId,
                    logId: logId
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log search request for traceId: {TraceId}", traceId);
            }
        }

        /// <summary>
        /// Log search response for individual request tracking
        /// </summary>
        private async Task LogSearchResponseAsync(ApiResult<EsimPackagesRp> result, string traceId, Guid logId)
        {
            try
            {
                if (result.IsSuccessed)
                {
                    var responseData = JsonConvert.SerializeObject(result.ResultObj);
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: responseData,
                        status: "success"
                    );
                }
                else
                {
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: result.Message,
                        status: "error",
                        errorMessage: result.Message
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log search response for traceId: {TraceId}", traceId);
            }
        }

        /// <summary>
        /// Log search error for individual request tracking
        /// </summary>
        private async Task LogSearchErrorAsync(Exception exception, EsimPackagesRq rq, string traceId, Guid logId)
        {
            try
            {
                await _fileLogger.UpdateLogAsync(
                    logId: logId,
                    status: "error",
                    errorMessage: exception.Message
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log search error for traceId: {TraceId}", traceId);
            }
        }

        public async Task<ApiResult<EsimProflieRp>> GetEsimProflieAsync(string serial)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");

            try
            {
                // Prepare request data
                var requestData = JsonConvert.SerializeObject(new { serial });

                // Log request with unique logId
                var logId = Guid.NewGuid();
                await _fileLogger.LogApiCallAsync(
                    action: "get_esim_profile",
                    endpoint: $"/eip/partner/esim/{serial}/query",
                    requestData: requestData,
                    status: "pending",
                    traceId: traceId,
                    logId: logId
                );

                var result = await _eSimBlueClient.GetEsimProflieAsync(serial);
                stopwatch.Stop();

                if (result.IsSuccessed)
                {
                    // Log successful response
                    var responseData = JsonConvert.SerializeObject(result.ResultObj);
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: responseData,
                        status: "success",
                        duration: stopwatch.Elapsed
                    );
                }
                else
                {
                    // Log failed response
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: result.Message,
                        status: "error",
                        duration: stopwatch.Elapsed,
                        errorMessage: result.Message
                    );
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // Log exception with unique logId
                await _fileLogger.LogApiCallAsync(
                    action: "get_esim_profile",
                    endpoint: $"/eip/partner/esim/{serial}/query",
                    requestData: JsonConvert.SerializeObject(new { serial }),
                    status: "error",
                    duration: stopwatch.Elapsed,
                    errorMessage: ex.Message,
                    traceId: traceId,
                    logId: Guid.NewGuid()
                );

                return new ApiErrorResult<EsimProflieRp>($"Get profile failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Enhanced search with customer context logging
        /// </summary>
        public async Task<ApiResult<EsimPackagesRp>> SearchPlansWithContextAsync(EsimPackagesRq rq, string? customerEmail = null, string? customerPhone = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId =  Guid.NewGuid().ToString("N");

            try
            {
                // Prepare request data
                var requestData = JsonConvert.SerializeObject(rq);

                // Log request with customer context and unique logId
                var logId = Guid.NewGuid();
                await _fileLogger.LogApiCallAsync(
                    action: "search_plans_with_context",
                    endpoint: "/eip/partner/esim/packages",
                    requestData: requestData,
                    status: "pending",
                    customerEmail: customerEmail,
                    customerPhone: customerPhone,
                    traceId: traceId,
                    logId: logId
                );

                var result = await _eSimBlueClient.GetAllDataPackagesAsync(rq);
                stopwatch.Stop();

                if (result.IsSuccessed)
                {
                    // Log successful response with package count
                    var responseData = JsonConvert.SerializeObject(new
                    {
                        packageCount = result.ResultObj?.PackageList?.Length ?? 0,
                        packages = result.ResultObj
                    });

                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: responseData,
                        status: "success",
                        duration: stopwatch.Elapsed
                    );
                }
                else
                {
                    // Log failed response
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: result.Message,
                        status: "error",
                        duration: stopwatch.Elapsed,
                        errorMessage: result.Message
                    );
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // Log exception with customer context and unique logId
                await _fileLogger.LogApiCallAsync(
                    action: "search_plans_with_context",
                    endpoint: "/eip/partner/esim/packages",
                    requestData: JsonConvert.SerializeObject(rq),
                    status: "error",
                    duration: stopwatch.Elapsed,
                    errorMessage: ex.Message,
                    customerEmail: customerEmail,
                    customerPhone: customerPhone,
                    traceId: traceId,
                    logId: Guid.NewGuid()
                );

                return new ApiErrorResult<EsimPackagesRp>($"Search failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Create order with comprehensive logging
        /// </summary>
        public async Task<ApiResult<string>> CreateOrderAsync(string planId, string customerEmail, string customerPhone, string? orderId = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");
            var requestId = orderId ?? Guid.NewGuid().ToString("N");

            try
            {
                // Create order request in ESimBlue API format
                var orderRequest = new EsimOrderRq
                {
                    RequestId = requestId,
                    PackageInfoList = new List<PackageInfo>
                    {
                        new PackageInfo
                        {
                            Sku = planId,
                            Quantity = 1
                        }
                    },
                    CustomerEmail = customerEmail,
                    CustomerPhone = customerPhone
                };

                var requestData = JsonConvert.SerializeObject(orderRequest);

                // Log order creation request with unique logId
                var logId = Guid.NewGuid();
                await _fileLogger.LogApiCallAsync(
                    action: "create_order",
                    endpoint: "/eip/partner/esim/orders",
                    requestData: requestData,
                    status: "pending",
                    orderId: requestId,
                    customerEmail: customerEmail,
                    customerPhone: customerPhone,
                    traceId: traceId,
                    logId: logId
                );

                // Call ESimBlue API to create order
                var apiResult = await _eSimBlueClient.CreateOrderAsync(orderRequest);
                stopwatch.Stop();

                if (apiResult.IsSuccessed && apiResult.ResultObj != null)
                {
                    var orderResponse = apiResult.ResultObj;
                    var responseData = JsonConvert.SerializeObject(orderResponse);

                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: responseData,
                        status: "success",
                        duration: stopwatch.Elapsed
                    );

                    // Return the order public ID from the response
                    return new ApiSuccessResult<string>(orderResponse.OrderPublicId);
                }
                else
                {
                    var errorData = JsonConvert.SerializeObject(new { error = apiResult.Message });
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: errorData,
                        status: "error",
                        duration: stopwatch.Elapsed
                    );

                    return new ApiErrorResult<string>($"Order creation failed: {apiResult.Message}");
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // Log exception with unique logId
                await _fileLogger.LogApiCallAsync(
                    action: "create_order",
                    endpoint: "/eip/partner/esim/order",
                    requestData: JsonConvert.SerializeObject(new { planId, customerEmail, customerPhone, orderId }),
                    status: "error",
                    duration: stopwatch.Elapsed,
                    errorMessage: ex.Message,
                    orderId: orderId,
                    customerEmail: customerEmail,
                    customerPhone: customerPhone,
                    traceId: traceId,
                    logId: Guid.NewGuid()
                );

                return new ApiErrorResult<string>($"Create order failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Get logs for analysis
        /// </summary>
        public async Task<ApiResult<List<ESimLogVM>>> GetRecentLogsAsync(int count = 50)
        {
            try
            {
                var logs = await _context.tblESimLogs
                    .OrderByDescending(x => x.Timestamp)
                    .Take(count)
                    .Select(x => new ESimLogVM
                    {
                        Id = x.Id,
                        OrderId = x.OrderId,
                        Action = x.Action,
                        CustomerEmail = x.CustomerEmail,
                        CustomerPhone = x.CustomerPhone,
                        Status = x.Status,
                        ErrorMessage = x.ErrorMessage,
                        Timestamp = x.Timestamp,
                        Duration = x.Duration,
                        ApiEndpoint = x.ApiEndpoint
                    })
                    .ToListAsync();

                return new ApiSuccessResult<List<ESimLogVM>>(logs);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<List<ESimLogVM>>($"Get logs failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Get performance statistics
        /// </summary>
        public async Task<ApiResult<object>> GetPerformanceStatsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var from = fromDate ?? DateTime.Today.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                var query = _context.tblESimLogs.Where(x => x.Timestamp >= from && x.Timestamp <= to);

                var stats = new
                {
                    TotalRequests = await query.CountAsync(),
                    SuccessfulRequests = await query.CountAsync(x => x.Status == "success"),
                    FailedRequests = await query.CountAsync(x => x.Status == "error"),
                    AverageResponseTime = await query.Where(x => x.Duration.HasValue)
                        .AverageAsync(x => x.Duration.Value.TotalMilliseconds),
                    TopActions = await query.GroupBy(x => x.Action)
                        .Select(g => new { Action = g.Key, Count = g.Count() })
                        .OrderByDescending(x => x.Count)
                        .Take(5)
                        .ToListAsync()
                };

                return new ApiSuccessResult<object>(stats);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<object>($"Get stats failed: {ex.Message}");
            }
        }

        // === AVAILABLE ESIM WORKFLOW METHODS ===

        // Note: GetOrderStatusAsync, CancelOrderAsync, ActivateEsimAsync, VerifyApiKeyAsync
        // are not available in current ESimBlue API client implementation



        public async Task<ApiResult<EsimUsageRp>> GetEsimUsageAsync(string serial)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");

            try
            {
                var logId = Guid.NewGuid();
                await _fileLogger.LogApiCallAsync(
                    action: "get_esim_usage",
                    endpoint: $"/eip/partner/esim/{serial}/usage",
                    requestData: JsonConvert.SerializeObject(new { serial }),
                    status: "pending",
                    traceId: traceId,
                    logId: logId
                );

                var result = await _eSimBlueClient.GetEsimUsageAsync(serial);
                stopwatch.Stop();

                await _fileLogger.LogApiCallAsync(
                    action: "get_esim_usage",
                    endpoint: $"/eip/partner/esim/{serial}/usage",
                    requestData: JsonConvert.SerializeObject(new { serial }),
                    responseData: JsonConvert.SerializeObject(result.ResultObj),
                    status: result.IsSuccessed ? "success" : "error",
                    traceId: traceId,
                    logId: logId
                );

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "GetEsimUsage API call exception for serial: {Serial}", serial);
                return new ApiErrorResult<EsimUsageRp>($"Get ESim usage failed: {ex.Message}");
            }
        }

        public async Task<ApiResult<EsimRedeemRp>> RedeemEsimAsync(string serial)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");

            try
            {
                var logId = Guid.NewGuid();
                await _fileLogger.LogApiCallAsync(
                    action: "redeem_esim",
                    endpoint: $"/eip/partner/esim/{serial}/redeem",
                    requestData: JsonConvert.SerializeObject(new { serial }),
                    status: "pending",
                    traceId: traceId,
                    logId: logId
                );

                var result = await _eSimBlueClient.RedeemEsimAsync(serial);
                stopwatch.Stop();

                await _fileLogger.LogApiCallAsync(
                    action: "redeem_esim",
                    endpoint: $"/eip/partner/esim/{serial}/redeem",
                    requestData: JsonConvert.SerializeObject(new { serial }),
                    responseData: JsonConvert.SerializeObject(result.ResultObj),
                    status: result.IsSuccessed ? "success" : "error",
                    traceId: traceId,
                    logId: logId
                );

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "RedeemEsim API call exception for serial: {Serial}", serial);
                return new ApiErrorResult<EsimRedeemRp>($"Redeem ESim failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Cancel ESim with comprehensive logging
        /// </summary>
        public async Task<ApiResult<bool>> CancelEsimAsync(string iccid, string serial)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");

            try
            {
                var logId = Guid.NewGuid();
                var requestData = JsonConvert.SerializeObject(new { iccid, serial });

                await _fileLogger.LogApiCallAsync(
                    action: "cancel_esim",
                    endpoint: "/eip/partner/esim/cancel",
                    requestData: requestData,
                    status: "pending",
                    traceId: traceId,
                    logId: logId
                );

                var result = await _eSimBlueClient.CancelEsimAsync(iccid, serial);
                stopwatch.Stop();

                await _fileLogger.UpdateLogAsync(
                    logId: logId,
                    responseData: JsonConvert.SerializeObject(result.ResultObj),
                    status: result.IsSuccessed ? "success" : "error",
                    duration: stopwatch.Elapsed
                );

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "CancelEsim API call exception for serial: {Serial}, iccid: {Iccid}", serial, iccid);
                return new ApiErrorResult<bool>($"Cancel ESim failed: {ex.Message}");
            }
        }

        /// <summary>
        /// TopUp ESim with comprehensive logging
        /// </summary>
        public async Task<ApiResult<EsimTopUpRp>> TopUpEsimAsync(string serial, string packageSku)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");

            try
            {
                var logId = Guid.NewGuid();
                var requestData = JsonConvert.SerializeObject(new { serial, sku = packageSku });

                await _fileLogger.LogApiCallAsync(
                    action: "topup_esim",
                    endpoint: "/eip/partner/esim/topup",
                    requestData: requestData,
                    status: "pending",
                    traceId: traceId,
                    logId: logId
                );

                var result = await _eSimBlueClient.TopUpEsimAsync(serial, packageSku);
                stopwatch.Stop();

                await _fileLogger.UpdateLogAsync(
                    logId: logId,
                    responseData: JsonConvert.SerializeObject(result.ResultObj),
                    status: result.IsSuccessed ? "success" : "error",
                    duration: stopwatch.Elapsed
                );

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "TopUpEsim API call exception for serial: {Serial}, packageSku: {PackageSku}", serial, packageSku);
                return new ApiErrorResult<EsimTopUpRp>($"TopUp ESim failed: {ex.Message}");
            }
        }


    }
}
