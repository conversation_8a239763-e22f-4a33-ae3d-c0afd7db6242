using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using NgocMaiTravel.ApiIntegration.Common.Logger;
using NgocMaiTravel.ApiIntegration.ESimBlueAPI;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Catalog.ESim
{
    public class ESimService : IESimService
    {
        private readonly IESimBlueAPIClient _eSimBlueClient;
        private readonly NgocMaiTravelDbContext _context;
        private readonly ILoggerService _loggerService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ESimService(
            IESimBlueAPIClient eSimBlueClient,
            NgocMaiTravelDbContext context,
            ILoggerService loggerService,
            IHttpContextAccessor httpContextAccessor)
        {
            _eSimBlueClient = eSimBlueClient ?? throw new ArgumentNullException(nameof(eSimBlueClient));
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _loggerService = loggerService ?? throw new ArgumentNullException(nameof(loggerService));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        }

        public async Task<ApiResult<PagedResult<ESimPlanVM>>> SearchPlansAsync(ESimSearchRequest request)
        {
            try
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"ESim search request: {System.Text.Json.JsonSerializer.Serialize(request)}", Guid.NewGuid().ToString(), "", false);
                
                var result = await _eSimBlueClient.SearchPlansAsync(request);
                
                if (result.IsSuccessed)
                {
                    _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"ESim search successful. Found {result.ResultObj.TotalRecords} plans", Guid.NewGuid().ToString(), "", false);
                }
                else
                {
                    _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"ESim search failed: {result.Message}", Guid.NewGuid().ToString(), "", false);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"ESim search exception: {ex.Message}", Guid.NewGuid().ToString(), "", false);
                return new ApiErrorResult<PagedResult<ESimPlanVM>>($"Search failed: {ex.Message}");
            }
        }

        public async Task<ApiResult<List<ESimCountryVM>>> GetCountriesAsync()
        {
            try
            {
                var result = await _eSimBlueClient.GetCountriesAsync();
                return result;
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Get countries exception: {ex.Message}", Guid.NewGuid().ToString(), "", false);
                return new ApiErrorResult<List<ESimCountryVM>>($"Get countries failed: {ex.Message}");
            }
        }

        public async Task<ApiResult<ESimPlanVM>> GetPlanDetailsAsync(string planId)
        {
            try
            {
                var result = await _eSimBlueClient.GetPlanDetailsAsync(planId);
                return result;
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Get plan details exception: {ex.Message}", Guid.NewGuid().ToString(), "", false);
                return new ApiErrorResult<ESimPlanVM>($"Get plan details failed: {ex.Message}");
            }
        }

        public async Task<ApiResult<ESimOrderVM>> CreateOrderAsync(ESimOrderRequest request)
        {
            try
            {
                // Get plan details first
                var planResult = await _eSimBlueClient.GetPlanDetailsAsync(request.PlanId);
                if (!planResult.IsSuccessed)
                {
                    return new ApiErrorResult<ESimOrderVM>($"Plan not found: {planResult.Message}");
                }

                var plan = planResult.ResultObj;

                // Create order in ESimBlue
                var orderResult = await _eSimBlueClient.CreateOrderAsync(request);
                if (!orderResult.IsSuccessed)
                {
                    return new ApiErrorResult<ESimOrderVM>($"Failed to create order: {orderResult.Message}");
                }

                var order = orderResult.ResultObj;

                // Save order to local database
                var dbOrder = new tblESimOrder
                {
                    Id = Guid.NewGuid(),
                    OrderId = order.OrderId,
                    PlanId = request.PlanId,
                    PlanName = plan.PlanName,
                    Country = plan.Country,
                    CountryCode = plan.CountryCode,
                    Region = plan.Region,
                    DataAmount = plan.DataAmount,
                    IsUnlimited = plan.IsUnlimited,
                    ValidityDays = plan.ValidityDays,
                    Price = plan.Price,
                    Currency = plan.Currency,
                    Provider = plan.Provider,
                    CustomerName = request.CustomerName,
                    CustomerEmail = request.CustomerEmail,
                    CustomerPhone = request.CustomerPhone,
                    Status = "pending",
                    CreatedAt = DateTime.Now,
                    PaymentMethod = request.PaymentMethod,
                    TotalAmount = plan.Price,
                    Notes = request.Notes,
                    OwnerID = GetCurrentOwnerID()
                };

                _context.tblESimOrders.Add(dbOrder);
                await _context.SaveChangesAsync();

                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"ESim order created: {order.OrderId}", order.OrderId, "", false);

                return new ApiSuccessResult<ESimOrderVM>(order);
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Create order exception: {ex.Message}", Guid.NewGuid().ToString(), "", false);
                return new ApiErrorResult<ESimOrderVM>($"Create order failed: {ex.Message}");
            }
        }

        public async Task<ApiResult<ESimOrderVM>> GetOrderAsync(string orderId)
        {
            try
            {
                var dbOrder = await _context.tblESimOrders.FirstOrDefaultAsync(x => x.OrderId == orderId);
                if (dbOrder == null)
                {
                    return new ApiErrorResult<ESimOrderVM>("Order not found");
                }

                var orderVM = MapToOrderVM(dbOrder);
                return new ApiSuccessResult<ESimOrderVM>(orderVM);
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Get order exception: {ex.Message}", orderId, "", false);
                return new ApiErrorResult<ESimOrderVM>($"Get order failed: {ex.Message}");
            }
        }

        public async Task<ApiResult<ESimOrderVM>> GetOrderByIdAsync(Guid id)
        {
            try
            {
                var dbOrder = await _context.tblESimOrders.FirstOrDefaultAsync(x => x.Id == id);
                if (dbOrder == null)
                {
                    return new ApiErrorResult<ESimOrderVM>("Order not found");
                }

                var orderVM = MapToOrderVM(dbOrder);
                return new ApiSuccessResult<ESimOrderVM>(orderVM);
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Get order by ID exception: {ex.Message}", id.ToString(), "", false);
                return new ApiErrorResult<ESimOrderVM>($"Get order failed: {ex.Message}");
            }
        }

        // Placeholder implementations for remaining methods
        public async Task<ApiResult<bool>> CancelOrderAsync(string orderId)
        {
            await Task.Delay(1);
            return new ApiErrorResult<bool>("Not implemented yet");
        }

        public async Task<ApiResult<PagedResult<ESimOrderVM>>> GetOrdersAsync(ESimLogSearchRequest request)
        {
            await Task.Delay(1);
            return new ApiErrorResult<PagedResult<ESimOrderVM>>("Not implemented yet");
        }

        public async Task<ApiResult<ESimActivationVM>> ActivateESimAsync(ESimActivationRequest request)
        {
            await Task.Delay(1);
            return new ApiErrorResult<ESimActivationVM>("Not implemented yet");
        }

        public async Task<ApiResult<ESimActivationVM>> GetActivationStatusAsync(string orderId)
        {
            await Task.Delay(1);
            return new ApiErrorResult<ESimActivationVM>("Not implemented yet");
        }

        public async Task<ApiResult<string>> CreatePaymentUrlAsync(string orderId, string paymentMethod)
        {
            await Task.Delay(1);
            return new ApiErrorResult<string>("Not implemented yet");
        }

        public async Task<ApiResult<bool>> ConfirmPaymentAsync(string orderId, string transactionId)
        {
            await Task.Delay(1);
            return new ApiErrorResult<bool>("Not implemented yet");
        }

        public async Task<ApiResult<bool>> HandlePaymentCallbackAsync(string orderId, string status, string? transactionId = null)
        {
            await Task.Delay(1);
            return new ApiErrorResult<bool>("Not implemented yet");
        }

        public async Task<ApiResult<PagedResult<ESimLogVM>>> GetLogsAsync(ESimLogSearchRequest request)
        {
            await Task.Delay(1);
            return new ApiErrorResult<PagedResult<ESimLogVM>>("Not implemented yet");
        }

        public async Task<ApiResult<ESimStatsVM>> GetStatsAsync(DateTime fromDate, DateTime toDate)
        {
            await Task.Delay(1);
            return new ApiErrorResult<ESimStatsVM>("Not implemented yet");
        }

        public async Task<ApiResult<bool>> UpdateOrderStatusAsync(string orderId, string status)
        {
            await Task.Delay(1);
            return new ApiErrorResult<bool>("Not implemented yet");
        }

        public async Task<ApiResult<bool>> SyncOrderStatusAsync(string orderId)
        {
            await Task.Delay(1);
            return new ApiErrorResult<bool>("Not implemented yet");
        }

        public async Task<ApiResult<List<ESimOrderVM>>> GetExpiredOrdersAsync()
        {
            await Task.Delay(1);
            return new ApiErrorResult<List<ESimOrderVM>>("Not implemented yet");
        }

        // Helper methods
        private Guid? GetCurrentOwnerID()
        {
            try
            {
                var apiKey = _httpContextAccessor.HttpContext?.Request?.Headers["X-Api-Key"].ToString();
                if (!string.IsNullOrEmpty(apiKey))
                {
                    var ownerID = _context.tblApiKeyLibs.Where(t => t.XApiKey == apiKey).Select(t => t.Id).FirstOrDefault();
                    return ownerID != Guid.Empty ? ownerID : null;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        private ESimOrderVM MapToOrderVM(tblESimOrder dbOrder)
        {
            return new ESimOrderVM
            {
                OrderId = dbOrder.OrderId,
                PlanId = dbOrder.PlanId,
                PlanName = dbOrder.PlanName,
                CustomerName = dbOrder.CustomerName,
                CustomerEmail = dbOrder.CustomerEmail,
                CustomerPhone = dbOrder.CustomerPhone,
                TotalAmount = dbOrder.TotalAmount,
                Currency = dbOrder.Currency,
                Status = dbOrder.Status,
                CreatedAt = dbOrder.CreatedAt,
                PaidAt = dbOrder.PaidAt,
                ActivatedAt = dbOrder.ActivatedAt,
                ExpiresAt = dbOrder.ExpiresAt,
                QrCode = dbOrder.QrCode,
                ActivationCode = dbOrder.ActivationCode,
                PaymentUrl = dbOrder.PaymentUrl,
                Notes = dbOrder.Notes,
                PlanDetails = new ESimPlanVM
                {
                    PlanId = dbOrder.PlanId,
                    PlanName = dbOrder.PlanName,
                    Country = dbOrder.Country,
                    CountryCode = dbOrder.CountryCode,
                    Region = dbOrder.Region,
                    DataAmount = dbOrder.DataAmount,
                    IsUnlimited = dbOrder.IsUnlimited,
                    ValidityDays = dbOrder.ValidityDays,
                    Price = dbOrder.Price,
                    Currency = dbOrder.Currency,
                    Provider = dbOrder.Provider
                }
            };
        }
    }
}
