using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NgocMaiTravel.Application.Common.Logging;
using NgocMaiTravel.Application.Common.Services;
using NgocMaiTravel.ApiIntegration.Esim;
using NgocMaiTravel.ApiIntegration.ESimBlueAPI;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Catalog.ESim
{
    public class ESimService : IESimService
    {
        private readonly IESimBlueAPIClient _eSimBlueClient;
        private readonly NgocMaiTravelDbContext _context;
        private readonly IESimFileLogger _fileLogger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IRequestDeduplicationService _deduplicationService;
        private readonly ILogger<ESimService> _logger;

        public ESimService(
            IESimBlueAPIClient eSimBlueClient,
            NgocMaiTravelDbContext context,
            IESimFileLogger fileLogger,
            IHttpContextAccessor httpContextAccessor,
            IRequestDeduplicationService deduplicationService,
            ILogger<ESimService> logger)
        {
            _eSimBlueClient = eSimBlueClient ?? throw new ArgumentNullException(nameof(eSimBlueClient));
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _fileLogger = fileLogger ?? throw new ArgumentNullException(nameof(fileLogger));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
            _deduplicationService = deduplicationService ?? throw new ArgumentNullException(nameof(deduplicationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ApiResult<BalanceRp>> GetBalanceAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");

            try
            {
                // Log request
                var logId = await _fileLogger.LogApiCallAsync(
                    action: "get_balance",
                    endpoint: "/eip/partner/company/balance",
                    requestData: "{}",
                    status: "pending",
                    traceId: traceId
                );

                var result = await _eSimBlueClient.GetBalanceAsync();
                stopwatch.Stop();

                if (result.IsSuccessed)
                {
                    // Log successful response
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: JsonConvert.SerializeObject(result.ResultObj),
                        status: "success",
                        duration: stopwatch.Elapsed
                    );
                }
                else
                {
                    // Log failed response
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: result.Message,
                        status: "error",
                        duration: stopwatch.Elapsed,
                        errorMessage: result.Message
                    );
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // Log exception
                await _fileLogger.LogApiCallAsync(
                    action: "get_balance",
                    endpoint: "/eip/partner/company/balance",
                    status: "error",
                    duration: stopwatch.Elapsed,
                    errorMessage: ex.Message,
                    traceId: traceId
                );

                return new ApiErrorResult<BalanceRp>($"Get balance failed: {ex.Message}");
            }
        }

        public async Task<ApiResult<EsimPackagesRp>> SearchPlansAsync(EsimPackagesRq rq)
        {
            // Generate unique key for request deduplication
            var requestKey = ESimRequestKeyGenerator.GenerateSearchPlansKey(rq);

            _logger.LogDebug("SearchPlansAsync called with key: {RequestKey}", requestKey);

            // Use deduplication service to handle concurrent requests
            return await _deduplicationService.ExecuteAsync(
                key: requestKey,
                factory: () => ExecuteSearchPlansInternalAsync(rq),
                cacheDurationSeconds: 30 // Cache for 30 seconds
            );
        }

        /// <summary>
        /// Internal method that actually executes the search plans request
        /// </summary>
        private async Task<ApiResult<EsimPackagesRp>> ExecuteSearchPlansInternalAsync(EsimPackagesRq rq)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId =  Guid.NewGuid().ToString("N");

            _logger.LogInformation("Executing SearchPlans request for traceId: {TraceId}", traceId);

            try
            {
                // Prepare request data
                var requestData = JsonConvert.SerializeObject(rq);

                // Log request
                var logId = await _fileLogger.LogApiCallAsync(
                    action: "search_plans",
                    endpoint: "/eip/partner/esim/packages",
                    requestData: requestData,
                    status: "pending",
                    traceId: traceId
                );

                var result = await _eSimBlueClient.GetAllDataPackagesAsync(rq);
                stopwatch.Stop();

                if (result.IsSuccessed)
                {
                    // Log successful response
                    var responseData = JsonConvert.SerializeObject(result.ResultObj);
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: responseData,
                        status: "success",
                        duration: stopwatch.Elapsed
                    );

                    _logger.LogInformation("SearchPlans completed successfully in {Duration}ms for traceId: {TraceId}",
                        stopwatch.ElapsedMilliseconds, traceId);
                }
                else
                {
                    // Log failed response
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: result.Message,
                        status: "error",
                        duration: stopwatch.Elapsed,
                        errorMessage: result.Message
                    );

                    _logger.LogWarning("SearchPlans failed for traceId: {TraceId}, Error: {Error}",
                        traceId, result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                _logger.LogError(ex, "SearchPlans exception for traceId: {TraceId}", traceId);

                // Log exception
                await _fileLogger.LogApiCallAsync(
                    action: "search_plans",
                    endpoint: "/eip/partner/esim/packages",
                    requestData: JsonConvert.SerializeObject(rq),
                    status: "error",
                    duration: stopwatch.Elapsed,
                    errorMessage: ex.Message,
                    traceId: traceId
                );

                return new ApiErrorResult<EsimPackagesRp>($"Search failed: {ex.Message}");
            }
        }

        public async Task<ApiResult<EsimProflieRp>> GetEsimProflieAsync(string serial)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");

            try
            {
                // Prepare request data
                var requestData = JsonConvert.SerializeObject(new { serial });

                // Log request
                var logId = await _fileLogger.LogApiCallAsync(
                    action: "get_esim_profile",
                    endpoint: $"/eip/partner/esim/{serial}/query",
                    requestData: requestData,
                    status: "pending",
                    traceId: traceId
                );

                var result = await _eSimBlueClient.GetEsimProflieAsync(serial);
                stopwatch.Stop();

                if (result.IsSuccessed)
                {
                    // Log successful response
                    var responseData = JsonConvert.SerializeObject(result.ResultObj);
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: responseData,
                        status: "success",
                        duration: stopwatch.Elapsed
                    );
                }
                else
                {
                    // Log failed response
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: result.Message,
                        status: "error",
                        duration: stopwatch.Elapsed,
                        errorMessage: result.Message
                    );
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // Log exception
                await _fileLogger.LogApiCallAsync(
                    action: "get_esim_profile",
                    endpoint: $"/eip/partner/esim/{serial}/query",
                    requestData: JsonConvert.SerializeObject(new { serial }),
                    status: "error",
                    duration: stopwatch.Elapsed,
                    errorMessage: ex.Message,
                    traceId: traceId
                );

                return new ApiErrorResult<EsimProflieRp>($"Get profile failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Enhanced search with customer context logging
        /// </summary>
        public async Task<ApiResult<EsimPackagesRp>> SearchPlansWithContextAsync(EsimPackagesRq rq, string? customerEmail = null, string? customerPhone = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId =  Guid.NewGuid().ToString("N");

            try
            {
                // Prepare request data
                var requestData = JsonConvert.SerializeObject(rq);

                // Log request with customer context
                var logId = await _fileLogger.LogApiCallAsync(
                    action: "search_plans_with_context",
                    endpoint: "/eip/partner/esim/packages",
                    requestData: requestData,
                    status: "pending",
                    customerEmail: customerEmail,
                    customerPhone: customerPhone,
                    traceId: traceId
                );

                var result = await _eSimBlueClient.GetAllDataPackagesAsync(rq);
                stopwatch.Stop();

                if (result.IsSuccessed)
                {
                    // Log successful response with package count
                    var responseData = JsonConvert.SerializeObject(new
                    {
                        packageCount = result.ResultObj?.PackageList?.Length ?? 0,
                        packages = result.ResultObj
                    });

                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: responseData,
                        status: "success",
                        duration: stopwatch.Elapsed
                    );
                }
                else
                {
                    // Log failed response
                    await _fileLogger.UpdateLogAsync(
                        logId: logId,
                        responseData: result.Message,
                        status: "error",
                        duration: stopwatch.Elapsed,
                        errorMessage: result.Message
                    );
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // Log exception with customer context
                await _fileLogger.LogApiCallAsync(
                    action: "search_plans_with_context",
                    endpoint: "/eip/partner/esim/packages",
                    requestData: JsonConvert.SerializeObject(rq),
                    status: "error",
                    duration: stopwatch.Elapsed,
                    errorMessage: ex.Message,
                    customerEmail: customerEmail,
                    customerPhone: customerPhone,
                    traceId: traceId
                );

                return new ApiErrorResult<EsimPackagesRp>($"Search failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Create order with comprehensive logging
        /// </summary>
        public async Task<ApiResult<string>> CreateOrderAsync(string planId, string customerEmail, string customerPhone, string? orderId = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var traceId = Guid.NewGuid().ToString("N");
            orderId ??= Guid.NewGuid().ToString("N");

            try
            {
                // Prepare request data
                var requestData = JsonConvert.SerializeObject(new
                {
                    planId = planId,
                    customerEmail = customerEmail,
                    customerPhone = customerPhone,
                    orderId = orderId
                });

                // Log order creation request
                var logId = await _fileLogger.LogApiCallAsync(
                    action: "create_order",
                    endpoint: "/eip/partner/esim/order",
                    requestData: requestData,
                    status: "pending",
                    orderId: orderId,
                    customerEmail: customerEmail,
                    customerPhone: customerPhone,
                    traceId: traceId
                );

                // Simulate order creation (replace with actual API call)
                await Task.Delay(100); // Simulate API call
                stopwatch.Stop();

                // For now, return success (implement actual order creation later)
                var responseData = JsonConvert.SerializeObject(new
                {
                    orderId = orderId,
                    status = "created",
                    timestamp = DateTime.Now
                });

                await _fileLogger.UpdateLogAsync(
                    logId: logId,
                    responseData: responseData,
                    status: "success",
                    duration: stopwatch.Elapsed
                );

                return new ApiSuccessResult<string>(orderId);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // Log exception
                await _fileLogger.LogApiCallAsync(
                    action: "create_order",
                    endpoint: "/eip/partner/esim/order",
                    requestData: JsonConvert.SerializeObject(new { planId, customerEmail, customerPhone, orderId }),
                    status: "error",
                    duration: stopwatch.Elapsed,
                    errorMessage: ex.Message,
                    orderId: orderId,
                    customerEmail: customerEmail,
                    customerPhone: customerPhone,
                    traceId: traceId
                );

                return new ApiErrorResult<string>($"Create order failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Get logs for analysis
        /// </summary>
        public async Task<ApiResult<List<ESimLogVM>>> GetRecentLogsAsync(int count = 50)
        {
            try
            {
                var logs = await _context.tblESimLogs
                    .OrderByDescending(x => x.Timestamp)
                    .Take(count)
                    .Select(x => new ESimLogVM
                    {
                        Id = x.Id,
                        OrderId = x.OrderId,
                        Action = x.Action,
                        CustomerEmail = x.CustomerEmail,
                        CustomerPhone = x.CustomerPhone,
                        Status = x.Status,
                        ErrorMessage = x.ErrorMessage,
                        Timestamp = x.Timestamp,
                        Duration = x.Duration,
                        ApiEndpoint = x.ApiEndpoint
                    })
                    .ToListAsync();

                return new ApiSuccessResult<List<ESimLogVM>>(logs);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<List<ESimLogVM>>($"Get logs failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Get performance statistics
        /// </summary>
        public async Task<ApiResult<object>> GetPerformanceStatsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var from = fromDate ?? DateTime.Today.AddDays(-7);
                var to = toDate ?? DateTime.Now;

                var query = _context.tblESimLogs.Where(x => x.Timestamp >= from && x.Timestamp <= to);

                var stats = new
                {
                    TotalRequests = await query.CountAsync(),
                    SuccessfulRequests = await query.CountAsync(x => x.Status == "success"),
                    FailedRequests = await query.CountAsync(x => x.Status == "error"),
                    AverageResponseTime = await query.Where(x => x.Duration.HasValue)
                        .AverageAsync(x => x.Duration.Value.TotalMilliseconds),
                    TopActions = await query.GroupBy(x => x.Action)
                        .Select(g => new { Action = g.Key, Count = g.Count() })
                        .OrderByDescending(x => x.Count)
                        .Take(5)
                        .ToListAsync()
                };

                return new ApiSuccessResult<object>(stats);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<object>($"Get stats failed: {ex.Message}");
            }
        }


    }
}
