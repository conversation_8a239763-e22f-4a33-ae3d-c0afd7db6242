﻿using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace NgocMaiTravel.ViewModels.Catalog.ESim
{
    public class CountryEsimGroup
    {
        public string CountryCode { get; set; }
        public string CountryName { get; set; }
        public string Flag { get; set; }
        public List<EsimItem> Esims { get; set; }
        public decimal MinPrice { get; set; }

    }

    public class EsimItem
    {
        public string? Sku { get; set; }
        public string? Model { get; set; }
        public string? Regional { get; set; }
        public string? Name { get; set; }
        public decimal Price { get; set; }
        public string? CurrencyCode { get; set; }
        public string? Speed { get; set; }
        public long? Duration { get; set; }
        public string Volume { get; set; }
        /// <summary>
        /// 1 = ESIM; 2 = TOP UP; 3 = eSIM plan
        /// </summary>
        public long TypePackage { get; set; }
        /// <summary>
        /// //1 fixed amount; 2 reset daily unlimited FUP1 
        ///     -- dataType = 1: <PERSON><PERSON><PERSON> dữ liệu cố định, ví dụ 10GB cho 30 ngày.       
        ///     -- dataType = 2: Gói FUP theo ngày – ví dụ: 1GB mỗi ngày trong 30 ngày.
        /// </summary>
        public long DataType { get; set; }
        /// <summary>
        /// 0.SMS not supported; 1. API SMS delivery only
        /// </summary>
        public long SmsStatus { get; set; }
        /// <summary>
        /// 1: First installation; 2: First network connection.
        /// </summary>
        public long ActiveType { get; set; }
        /// <summary>
        /// "unusedValidTime": 7
        /// Nghĩa là sau khi bạn mua eSIM, bạn có tối đa 7 ngày để bắt đầu sử dụng(hoặc kích hoạt). 
        /// Nếu sau 7 ngày bạn chưa dùng thì gói sẽ bị vô hiệu hóa.
        /// </summary>
        public long? UnusedValidTime { get; set; }
        /// <summary>
        /// Top up support 1 = no; 2 = yes
        /// </summary>
        public long SupportTopUpType { get; set; }
        public List<string> OperatorList { get; set; } = new List<string>();
    }
}
