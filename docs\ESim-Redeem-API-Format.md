# ESim Redeem API Format - ESimBlue Integration

## Overview

Updated ESim redeem API models to match the actual ESimBlue API response format based on the provided success/error response examples.

## API Format

### **Redeem Endpoint**

**Endpoint:** `POST /eip/partner/esim/{serial}/redeem`

**Request:** Empty body

### **Success Response Format**
```json
{
    "orderPublicId": "bb559e4f-6b70-498f-939a-9da767049ca6",
    "serial": "GX28D0X493",
    "esim": {
        "packageId": 25,
        "sku": "BLC-01-JP-moshi-moshi-7days-1gb",
        "packageName": "1 GB - 7 Days",
        "model": "moshi-moshi-7days-1gb",
        "slug": "japan",
        "durationUnit": "DAY",
        "location": "JP",
        "activeType": 1,
        "supportTopUpType": 1,
        "imsi": null,
        "iccid": "894000000000058815",
        "ac": "LPA:1$lpa.airalo.com$TEST",
        "qrCodeUrl": "https://sandbox.airalo.com/qr?expires=1826683712&id=262303&signature=cbfd8900ad3957b8ec590b1d14149fd0cf97b6c394211e8bfe8b3509f9bb1652",
        "shortUrl": "https://esimsetup.apple.com/esim_qrcode_provisioning?carddata=LPA:1$lpa.airalo.com$TEST",
        "apn": null
    }
}
```

### **Error Response Format**
```json
[
    {
        "name": "SERIAL",
        "code": "RESOURCE_NOT_FOUND",
        "message": "Serial not found"
    }
]
```

## Model Updates

### **EsimRedeemRp (Success Response)**
```csharp
public class EsimRedeemRp
{
    public string OrderPublicId { get; set; } = string.Empty;
    public string Serial { get; set; } = string.Empty;
    public EsimDetails Esim { get; set; } = new();
}

public class EsimDetails
{
    public int PackageId { get; set; }
    public string Sku { get; set; } = string.Empty;
    public string PackageName { get; set; } = string.Empty;
    public string Model { get; set; } = string.Empty;
    public string Slug { get; set; } = string.Empty;
    public string DurationUnit { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public int ActiveType { get; set; }
    public int SupportTopUpType { get; set; }
    public string? Imsi { get; set; }
    public string Iccid { get; set; } = string.Empty;
    public string Ac { get; set; } = string.Empty; // LPA code
    public string QrCodeUrl { get; set; } = string.Empty;
    public string ShortUrl { get; set; } = string.Empty;
    public string? Apn { get; set; }
}
```

### **EsimErrorResponse (Error Response)**
```csharp
public class EsimErrorResponse
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
}
```

### **EsimApiResponse (Wrapper)**
```csharp
public class EsimApiResponse<T>
{
    public bool IsSuccess { get; set; }
    public T? Data { get; set; }
    public List<EsimErrorResponse> Errors { get; set; } = [];
    public string ErrorMessage => Errors.Count > 0 ? 
        string.Join("; ", Errors.Select(e => e.Message)) : string.Empty;
}
```

## Implementation

### **Key Insight: API Always Returns HTTP 200**
The ESimBlue API **always returns HTTP 200 OK**, but the response body format indicates success or failure:
- **Success**: JSON Object `{ "orderPublicId": "...", "serial": "...", "esim": {...} }`
- **Failure**: JSON Array `[{ "name": "...", "code": "...", "message": "..." }]`

### **ESimBlueAPIClient.RedeemEsimAsync (Fixed)**
```csharp
public async Task<ApiResult<EsimRedeemRp>> RedeemEsimAsync(string serial)
{
    var endpoint = $"/eip/partner/esim/{serial}/redeem";

    try
    {
        var request = new HttpRequestMessage(HttpMethod.Post, endpoint);
        var response = await _httpClient.SendAsync(request);
        var responseContent = await response.Content.ReadAsStringAsync();

        // API always returns 200 OK, but response format differs
        if (response.IsSuccessStatusCode)
        {
            // Check if response starts with '[' (error array) or '{' (success object)
            var trimmedContent = responseContent.Trim();

            if (trimmedContent.StartsWith('['))
            {
                // Response is an error array
                try
                {
                    var errorArray = JsonSerializer.Deserialize<List<EsimErrorResponse>>(responseContent);
                    if (errorArray != null && errorArray.Count > 0)
                    {
                        var errorMessage = string.Join("; ", errorArray.Select(e => $"{e.Code}: {e.Message}"));
                        return new ApiErrorResult<EsimRedeemRp>($"Redeem failed: {errorMessage}");
                    }
                }
                catch (JsonException ex)
                {
                    return new ApiErrorResult<EsimRedeemRp>($"Failed to parse error response: {ex.Message}");
                }
            }
            else if (trimmedContent.StartsWith('{'))
            {
                // Response is a success object
                try
                {
                    var successResult = JsonSerializer.Deserialize<EsimRedeemRp>(responseContent);
                    if (successResult != null && !string.IsNullOrEmpty(successResult.OrderPublicId))
                    {
                        return new ApiSuccessResult<EsimRedeemRp>(successResult);
                    }
                }
                catch (JsonException ex)
                {
                    return new ApiErrorResult<EsimRedeemRp>($"Failed to parse success response: {ex.Message}");
                }
            }
        }

        // If we reach here, something unexpected happened
        return new ApiErrorResult<EsimRedeemRp>($"Unexpected response format: {response.StatusCode} - {responseContent}");
    }
    catch (HttpRequestException ex)
    {
        return new ApiErrorResult<EsimRedeemRp>($"HTTP request failed: {ex.Message}");
    }
    catch (Exception ex)
    {
        return new ApiErrorResult<EsimRedeemRp>($"Redeem request failed: {ex.Message}");
    }
}
```

### **Response Format Detection Logic**
```csharp
var trimmedContent = responseContent.Trim();

if (trimmedContent.StartsWith('['))
{
    // Error response: [{"name": "SERIAL", "code": "RESOURCE_NOT_FOUND", "message": "Serial not found"}]
    // Parse as error array
}
else if (trimmedContent.StartsWith('{'))
{
    // Success response: {"orderPublicId": "...", "serial": "...", "esim": {...}}
    // Parse as success object
}
```

## Key Information from Response

### **Success Response Data**
- **OrderPublicId**: Unique order identifier
- **Serial**: ESim serial number
- **ICCID**: Integrated Circuit Card Identifier
- **LPA Code (ac)**: Local Profile Assistant code for activation
- **QR Code URL**: Direct link to QR code for easy scanning
- **Short URL**: Apple ESim setup URL
- **Package Details**: SKU, name, location, duration, etc.

### **Error Response Data**
- **Name**: Field that caused the error (e.g., "SERIAL")
- **Code**: Error code (e.g., "RESOURCE_NOT_FOUND")
- **Message**: Human-readable error message

## Usage Examples

### **Frontend Integration**
```javascript
// Redeem ESim
async function redeemESim(serial) {
    try {
        const response = await fetch(`/api/ESim/redeem/${serial}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        
        if (result.isSuccessed && result.resultObj) {
            const esimData = result.resultObj;
            
            // Display QR code and activation info
            displayQRCode(esimData.esim.qrCodeUrl);
            displayLPACode(esimData.esim.ac);
            displayActivationInstructions(esimData.esim.shortUrl);
            
            return esimData;
        } else {
            throw new Error(result.message || 'Redeem failed');
        }
    } catch (error) {
        console.error('Redeem error:', error);
        throw error;
    }
}
```

### **Backend Service Usage**
```csharp
// In ESimService
public async Task<ApiResult<EsimRedeemRp>> RedeemEsimAsync(string serial)
{
    var stopwatch = Stopwatch.StartNew();
    var traceId = Guid.NewGuid().ToString("N");

    try
    {
        var logId = Guid.NewGuid();
        await _fileLogger.LogApiCallAsync(
            action: "redeem_esim",
            endpoint: $"/eip/partner/esim/{serial}/redeem",
            requestData: JsonConvert.SerializeObject(new { serial }),
            status: "pending",
            traceId: traceId,
            logId: logId
        );

        var result = await _eSimBlueClient.RedeemEsimAsync(serial);
        stopwatch.Stop();

        await _fileLogger.LogApiCallAsync(
            action: "redeem_esim",
            endpoint: $"/eip/partner/esim/{serial}/redeem",
            requestData: JsonConvert.SerializeObject(new { serial }),
            responseData: JsonConvert.SerializeObject(result.ResultObj),
            status: result.IsSuccessed ? "success" : "error",
            traceId: traceId,
            logId: logId
        );

        return result;
    }
    catch (Exception ex)
    {
        stopwatch.Stop();
        _logger.LogError(ex, "RedeemEsim API call exception for serial: {Serial}", serial);
        return new ApiErrorResult<EsimRedeemRp>($"Redeem ESim failed: {ex.Message}");
    }
}
```

## Testing

### **Test Response Format Detection**
```bash
# Test success response detection
curl -X GET "https://localhost:7001/api/TestRedis/test-response-detection?responseType=success"

# Test error response detection
curl -X GET "https://localhost:7001/api/TestRedis/test-response-detection?responseType=error"
```

### **Test Redeem API Format**
```bash
# Test the redeem API format
curl -X POST "https://localhost:7001/api/TestRedis/test-esim-redeem?serial=GX28D0X493"
```

### **Test Actual Redeem**
```bash
# Test actual redeem (requires valid serial)
curl -X POST "https://localhost:7001/api/ESim/redeem/VALID_SERIAL_HERE"
```

### **Expected Test Results**

#### **Success Response Detection:**
```json
{
  "success": true,
  "responseType": "success",
  "detectedFormat": "success_object",
  "expectedIsSuccess": true,
  "detectionLogic": {
    "startsWithBracket": false,
    "startsWithBrace": true,
    "firstChar": "{"
  }
}
```

#### **Error Response Detection:**
```json
{
  "success": true,
  "responseType": "error",
  "detectedFormat": "error_array",
  "expectedIsSuccess": false,
  "detectionLogic": {
    "startsWithBracket": true,
    "startsWithBrace": false,
    "firstChar": "["
  }
}
```

## Error Handling

### **Common Error Codes**
- `RESOURCE_NOT_FOUND`: Serial number not found
- `INVALID_SERIAL`: Serial format is invalid
- `ALREADY_REDEEMED`: Serial already redeemed
- `EXPIRED_SERIAL`: Serial has expired

### **Error Response Processing**
```csharp
if (!result.IsSuccessed)
{
    // Parse error details from response
    var errorDetails = ParseErrorResponse(result.Message);
    
    switch (errorDetails.Code)
    {
        case "RESOURCE_NOT_FOUND":
            return BadRequest("Serial number not found");
        case "ALREADY_REDEEMED":
            return BadRequest("This ESim has already been redeemed");
        case "EXPIRED_SERIAL":
            return BadRequest("This ESim serial has expired");
        default:
            return BadRequest($"Redeem failed: {errorDetails.Message}");
    }
}
```

## Benefits

1. **Accurate API Integration**: Matches actual ESimBlue response format
2. **Comprehensive Error Handling**: Proper error parsing and reporting
3. **Rich Response Data**: Complete ESim details for activation
4. **QR Code Support**: Direct URLs for QR code display
5. **Apple Integration**: Short URLs for Apple ESim setup

This update ensures proper handling of both success and error responses from the ESimBlue redeem API.
