# ESim Redeem Logic Test Guide

## Problem Statement

The ESimBlue API **always returns HTTP 200 OK**, but uses different response body formats to indicate success or failure:

- **Success**: JSON Object starting with `{`
- **Failure**: JSON Array starting with `[`

## Fixed Implementation

### **Key Changes**
1. **Don't rely on HTTP status codes** - API always returns 200
2. **Check response format** - Object vs Array
3. **Parse accordingly** - Different models for different formats

### **Detection Logic**
```csharp
var trimmedContent = responseContent.Trim();

if (trimmedContent.StartsWith('['))
{
    // Error response: Parse as EsimErrorResponse[]
    var errorArray = JsonSerializer.Deserialize<List<EsimErrorResponse>>(responseContent);
    return new ApiErrorResult<EsimRedeemRp>($"Redeem failed: {errorMessage}");
}
else if (trimmedContent.StartsWith('{'))
{
    // Success response: Parse as EsimRedeemRp
    var successResult = JsonSerializer.Deserialize<EsimRedeemRp>(responseContent);
    return new ApiSuccessResult<EsimRedeemRp>(successResult);
}
```

## Test Cases

### **Test 1: Response Format Detection**

#### **Success Response Test**
```bash
curl -X GET "https://localhost:7001/api/TestRedis/test-response-detection?responseType=success"
```

**Expected Result:**
```json
{
  "success": true,
  "detectedFormat": "success_object",
  "detectionLogic": {
    "startsWithBracket": false,
    "startsWithBrace": true,
    "firstChar": "{"
  }
}
```

#### **Error Response Test**
```bash
curl -X GET "https://localhost:7001/api/TestRedis/test-response-detection?responseType=error"
```

**Expected Result:**
```json
{
  "success": true,
  "detectedFormat": "error_array",
  "detectionLogic": {
    "startsWithBracket": true,
    "startsWithBrace": false,
    "firstChar": "["
  }
}
```

### **Test 2: Actual API Integration**

#### **Test with Valid Serial (Success)**
```bash
curl -X POST "https://localhost:7001/api/ESim/redeem/VALID_SERIAL_HERE" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "isSuccessed": true,
  "message": "Redeem successful",
  "resultObj": {
    "orderPublicId": "bb559e4f-6b70-498f-939a-9da767049ca6",
    "serial": "GX28D0X493",
    "esim": {
      "packageId": 25,
      "sku": "BLC-01-JP-moshi-moshi-7days-1gb",
      "packageName": "1 GB - 7 Days",
      "iccid": "894000000000058815",
      "ac": "LPA:1$lpa.airalo.com$TEST",
      "qrCodeUrl": "https://sandbox.airalo.com/qr?expires=1826683712&id=262303&signature=...",
      "shortUrl": "https://esimsetup.apple.com/esim_qrcode_provisioning?carddata=LPA:1$lpa.airalo.com$TEST"
    }
  }
}
```

#### **Test with Invalid Serial (Error)**
```bash
curl -X POST "https://localhost:7001/api/ESim/redeem/INVALID_SERIAL" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "isSuccessed": false,
  "message": "Redeem failed: RESOURCE_NOT_FOUND: Serial not found",
  "resultObj": null
}
```

### **Test 3: Edge Cases**

#### **Empty Response**
- **Input**: Empty string `""`
- **Expected**: Error with "Unexpected response format"

#### **Invalid JSON**
- **Input**: `"invalid json {"`
- **Expected**: Error with "Failed to parse response"

#### **Unexpected Format**
- **Input**: `"plain text response"`
- **Expected**: Error with "Unexpected response format"

## Verification Steps

### **1. Start Application**
```bash
cd NgocMaiTravel.BackendApi
dotnet run
```

### **2. Test Response Detection Logic**
```bash
# Test success detection
curl "https://localhost:7001/api/TestRedis/test-response-detection?responseType=success"

# Test error detection
curl "https://localhost:7001/api/TestRedis/test-response-detection?responseType=error"
```

### **3. Verify Detection Results**
- ✅ Success response should detect `success_object`
- ✅ Error response should detect `error_array`
- ✅ First character detection should be correct

### **4. Test Actual Redeem API**
```bash
# Test redeem format
curl -X POST "https://localhost:7001/api/TestRedis/test-esim-redeem?serial=GX28D0X493"

# Test actual redeem (if you have valid serial)
curl -X POST "https://localhost:7001/api/ESim/redeem/YOUR_VALID_SERIAL"
```

## Success Criteria

### **✅ Logic Working Correctly If:**

1. **Response Detection**:
   - Success responses (starting with `{`) are detected as `success_object`
   - Error responses (starting with `[`) are detected as `error_array`

2. **API Integration**:
   - Valid serials return success with complete ESim data
   - Invalid serials return error with proper error message
   - No HTTP status code dependency

3. **Error Handling**:
   - JSON parsing errors are caught and handled
   - Unexpected formats return meaningful errors
   - Network errors are properly propagated

4. **Data Completeness**:
   - Success responses include all required fields
   - QR code URLs and LPA codes are present
   - Error responses include error codes and messages

## Common Issues & Solutions

### **Issue 1: Still Getting Parse Errors**
**Cause**: Response format not detected correctly
**Solution**: Check trimming and character detection logic

### **Issue 2: Success Treated as Error**
**Cause**: Response starts with whitespace
**Solution**: Ensure `Trim()` is called before character check

### **Issue 3: Error Details Missing**
**Cause**: Error array not parsed correctly
**Solution**: Verify EsimErrorResponse model matches API format

### **Issue 4: HTTP Status Dependency**
**Cause**: Still checking `IsSuccessStatusCode`
**Solution**: Remember API always returns 200, check content format instead

## Monitoring

### **Log What to Watch**
- Response content format (starts with `[` or `{`)
- Parsing success/failure for both formats
- Error codes and messages from failed redeems
- Complete ESim data from successful redeems

### **Metrics to Track**
- Success rate of redeem operations
- Most common error codes
- Response parsing success rate
- API response times

This comprehensive test ensures the redeem logic correctly handles the ESimBlue API's unique response pattern.
