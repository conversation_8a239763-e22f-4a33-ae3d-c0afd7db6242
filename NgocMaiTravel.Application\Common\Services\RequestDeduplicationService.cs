using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Common.Services
{
    /// <summary>
    /// Implementation of request deduplication service using Redis
    /// </summary>
    public class RequestDeduplicationService : IRequestDeduplicationService
    {
        private readonly ILogger<RequestDeduplicationService> _logger;
        private readonly IDatabase _redisDatabase;
        private readonly IConnectionMultiplexer _redis;

        // Semaphores for controlling concurrent execution (still in-memory for performance)
        private readonly ConcurrentDictionary<string, SemaphoreSlim> _semaphores = new();

        // Statistics (in-memory for performance)
        private long _totalRequests = 0;
        private long _cacheHits = 0;
        private long _deduplications = 0;

        // Redis key prefixes
        private const string CACHE_KEY_PREFIX = "esim:dedup:cache:";
        private const string STATS_KEY_PREFIX = "esim:dedup:stats:";

        public RequestDeduplicationService(
            ILogger<RequestDeduplicationService> logger,
            IConnectionMultiplexer redis)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _redis = redis ?? throw new ArgumentNullException(nameof(redis));
            _redisDatabase = _redis.GetDatabase();
        }

        public async Task<T> ExecuteAsync<T>(string key, Func<Task<T>> factory, int cacheDurationSeconds = 30)
        {
            ArgumentNullException.ThrowIfNull(factory);

            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Key cannot be null or empty", nameof(key));

            Interlocked.Increment(ref _totalRequests);

            var cacheKey = CACHE_KEY_PREFIX + key;

            try
            {
                // Check if we have a valid cached result in Redis
                var cachedValue = await _redisDatabase.StringGetAsync(cacheKey);
                if (cachedValue.HasValue)
                {
                    Interlocked.Increment(ref _cacheHits);
                    _logger.LogDebug("Redis cache hit for key: {Key}", key);

                    var cachedResult = JsonConvert.DeserializeObject<T>(cachedValue!);
                    return cachedResult!;
                }

                // Get or create semaphore for this key (in-memory for performance)
                var semaphore = _semaphores.GetOrAdd(key, _ => new SemaphoreSlim(1, 1));

                try
                {
                    // Wait for semaphore (only first request will pass immediately)
                    await semaphore.WaitAsync();

                    // Double-check Redis cache after acquiring semaphore
                    cachedValue = await _redisDatabase.StringGetAsync(cacheKey);
                    if (cachedValue.HasValue)
                    {
                        Interlocked.Increment(ref _cacheHits);
                        Interlocked.Increment(ref _deduplications);
                        _logger.LogDebug("Redis cache hit after semaphore wait for key: {Key} (deduplication)", key);

                        var cachedResult = JsonConvert.DeserializeObject<T>(cachedValue!);
                        return cachedResult!;
                    }

                    // Execute the factory function
                    _logger.LogDebug("Executing factory function for key: {Key}", key);
                    var result = await factory();

                    // Cache the result in Redis with expiration
                    var serializedResult = JsonConvert.SerializeObject(result);
                    var expiry = TimeSpan.FromSeconds(cacheDurationSeconds);

                    await _redisDatabase.StringSetAsync(cacheKey, serializedResult, expiry);

                    _logger.LogDebug("Cached result in Redis for key: {Key}, expires in: {ExpirySeconds}s", key, cacheDurationSeconds);

                    // Update stats in Redis (fire and forget)
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await UpdateStatsInRedisAsync();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to update stats in Redis");
                        }
                    });

                    return result;
                }
                finally
                {
                    semaphore.Release();
                }
            }
            catch (RedisException ex)
            {
                _logger.LogWarning(ex, "Redis error for key: {Key}, falling back to direct execution", key);
                // Fallback to direct execution if Redis fails
                return await factory();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing factory function for key: {Key}", key);
                throw;
            }
        }

        public void ClearCache(string key)
        {
            if (string.IsNullOrEmpty(key))
                return;

            var cacheKey = CACHE_KEY_PREFIX + key;

            try
            {
                _redisDatabase.KeyDelete(cacheKey);
                _logger.LogDebug("Cleared Redis cache for key: {Key}", key);
            }
            catch (RedisException ex)
            {
                _logger.LogWarning(ex, "Failed to clear Redis cache for key: {Key}", key);
            }
        }

        public void ClearAllCache()
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                var keys = server.Keys(pattern: CACHE_KEY_PREFIX + "*");
                var keyArray = keys.ToArray();

                if (keyArray.Length > 0)
                {
                    _redisDatabase.KeyDelete(keyArray);
                    _logger.LogInformation("Cleared {Count} Redis cache entries", keyArray.Length);
                }
                else
                {
                    _logger.LogInformation("No Redis cache entries to clear");
                }
            }
            catch (RedisException ex)
            {
                _logger.LogWarning(ex, "Failed to clear all Redis cache entries");
            }
        }

        public object GetCacheStats()
        {
            try
            {
                // Get stats from Redis
                var redisStats = GetStatsFromRedisAsync().GetAwaiter().GetResult();

                return new
                {
                    TotalRequests = _totalRequests,
                    CacheHits = _cacheHits,
                    Deduplications = _deduplications,
                    CacheHitRate = _totalRequests > 0 ? (double)_cacheHits / _totalRequests * 100 : 0,
                    DeduplicationRate = _totalRequests > 0 ? (double)_deduplications / _totalRequests * 100 : 0,
                    ActiveSemaphores = _semaphores.Count,
                    RedisStats = redisStats
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get complete cache stats");

                return new
                {
                    TotalRequests = _totalRequests,
                    CacheHits = _cacheHits,
                    Deduplications = _deduplications,
                    CacheHitRate = _totalRequests > 0 ? (double)_cacheHits / _totalRequests * 100 : 0,
                    DeduplicationRate = _totalRequests > 0 ? (double)_deduplications / _totalRequests * 100 : 0,
                    ActiveSemaphores = _semaphores.Count,
                    RedisStats = "Unavailable"
                };
            }
        }

        /// <summary>
        /// Update statistics in Redis (async)
        /// </summary>
        private async Task UpdateStatsInRedisAsync()
        {
            try
            {
                var statsKey = STATS_KEY_PREFIX + "current";
                var stats = new
                {
                    TotalRequests = _totalRequests,
                    CacheHits = _cacheHits,
                    Deduplications = _deduplications,
                    LastUpdated = DateTime.UtcNow
                };

                var serializedStats = JsonConvert.SerializeObject(stats);
                await _redisDatabase.StringSetAsync(statsKey, serializedStats, TimeSpan.FromHours(24));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to update stats in Redis");
            }
        }

        /// <summary>
        /// Get statistics from Redis (async)
        /// </summary>
        private async Task<object> GetStatsFromRedisAsync()
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                var cacheKeys = server.Keys(pattern: CACHE_KEY_PREFIX + "*");
                var activeCacheEntries = cacheKeys.Count();

                var statsKey = STATS_KEY_PREFIX + "current";
                var statsValue = await _redisDatabase.StringGetAsync(statsKey);

                if (statsValue.HasValue)
                {
                    var redisStats = JsonConvert.DeserializeObject(statsValue!);
                    return new
                    {
                        ActiveCacheEntries = activeCacheEntries,
                        StoredStats = redisStats
                    };
                }

                return new
                {
                    ActiveCacheEntries = activeCacheEntries,
                    StoredStats = "No stats available"
                };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get stats from Redis");
                return new { Error = ex.Message };
            }
        }

        /// <summary>
        /// Cleanup expired semaphores periodically (optional maintenance)
        /// </summary>
        public void CleanupExpiredSemaphores()
        {
            var expiredKeys = new List<string>();

            foreach (var kvp in _semaphores)
            {
                var semaphore = kvp.Value;
                if (semaphore.CurrentCount == 1) // No one waiting
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            foreach (var key in expiredKeys)
            {
                if (_semaphores.TryRemove(key, out var semaphore))
                {
                    semaphore.Dispose();
                }
            }

            if (expiredKeys.Count > 0)
            {
                _logger.LogDebug("Cleaned up {Count} expired semaphores", expiredKeys.Count);
            }
        }
    }
}
