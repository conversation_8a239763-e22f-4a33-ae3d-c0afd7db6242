using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Common.Services
{
    /// <summary>
    /// Implementation of request deduplication service
    /// </summary>
    public class RequestDeduplicationService : IRequestDeduplicationService
    {
        private readonly ILogger<RequestDeduplicationService> _logger;
        
        // Cache for storing results
        private readonly ConcurrentDictionary<string, CacheEntry> _cache = new();
        
        // Semaphores for controlling concurrent execution
        private readonly ConcurrentDictionary<string, SemaphoreSlim> _semaphores = new();
        
        // Statistics
        private long _totalRequests = 0;
        private long _cacheHits = 0;
        private long _deduplications = 0;

        public RequestDeduplicationService(ILogger<RequestDeduplicationService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<T> ExecuteAsync<T>(string key, Func<Task<T>> factory, int cacheDurationSeconds = 30)
        {
            if (string.IsNullOrEmpty(key))
                throw new ArgumentException("Key cannot be null or empty", nameof(key));

            if (factory == null)
                throw new ArgumentNullException(nameof(factory));

            Interlocked.Increment(ref _totalRequests);

            // Check if we have a valid cached result
            if (_cache.TryGetValue(key, out var cachedEntry) && !cachedEntry.IsExpired)
            {
                Interlocked.Increment(ref _cacheHits);
                _logger.LogDebug("Cache hit for key: {Key}", key);
                return (T)cachedEntry.Value;
            }

            // Get or create semaphore for this key
            var semaphore = _semaphores.GetOrAdd(key, _ => new SemaphoreSlim(1, 1));

            try
            {
                // Wait for semaphore (only first request will pass immediately)
                await semaphore.WaitAsync();

                // Double-check cache after acquiring semaphore
                if (_cache.TryGetValue(key, out cachedEntry) && !cachedEntry.IsExpired)
                {
                    Interlocked.Increment(ref _cacheHits);
                    Interlocked.Increment(ref _deduplications);
                    _logger.LogDebug("Cache hit after semaphore wait for key: {Key} (deduplication)", key);
                    return (T)cachedEntry.Value;
                }

                // Execute the factory function
                _logger.LogDebug("Executing factory function for key: {Key}", key);
                var result = await factory();

                // Cache the result
                var expiryTime = DateTime.UtcNow.AddSeconds(cacheDurationSeconds);
                _cache.AddOrUpdate(key, 
                    new CacheEntry(result, expiryTime),
                    (_, _) => new CacheEntry(result, expiryTime));

                _logger.LogDebug("Cached result for key: {Key}, expires at: {ExpiryTime}", key, expiryTime);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing factory function for key: {Key}", key);
                throw;
            }
            finally
            {
                semaphore.Release();
            }
        }

        public void ClearCache(string key)
        {
            if (string.IsNullOrEmpty(key))
                return;

            _cache.TryRemove(key, out _);
            _logger.LogDebug("Cleared cache for key: {Key}", key);
        }

        public void ClearAllCache()
        {
            var count = _cache.Count;
            _cache.Clear();
            _logger.LogInformation("Cleared all cache entries. Count: {Count}", count);
        }

        public object GetCacheStats()
        {
            // Clean up expired entries
            CleanupExpiredEntries();

            return new
            {
                TotalRequests = _totalRequests,
                CacheHits = _cacheHits,
                Deduplications = _deduplications,
                CacheHitRate = _totalRequests > 0 ? (double)_cacheHits / _totalRequests * 100 : 0,
                DeduplicationRate = _totalRequests > 0 ? (double)_deduplications / _totalRequests * 100 : 0,
                ActiveCacheEntries = _cache.Count,
                ActiveSemaphores = _semaphores.Count
            };
        }

        private void CleanupExpiredEntries()
        {
            var expiredKeys = new List<string>();
            
            foreach (var kvp in _cache)
            {
                if (kvp.Value.IsExpired)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            foreach (var key in expiredKeys)
            {
                _cache.TryRemove(key, out _);
                
                // Also remove semaphore if no longer needed
                if (_semaphores.TryGetValue(key, out var semaphore))
                {
                    if (semaphore.CurrentCount == 1) // No one waiting
                    {
                        _semaphores.TryRemove(key, out _);
                        semaphore.Dispose();
                    }
                }
            }

            if (expiredKeys.Count > 0)
            {
                _logger.LogDebug("Cleaned up {Count} expired cache entries", expiredKeys.Count);
            }
        }

        private class CacheEntry
        {
            public object Value { get; }
            public DateTime ExpiryTime { get; }
            public bool IsExpired => DateTime.UtcNow > ExpiryTime;

            public CacheEntry(object value, DateTime expiryTime)
            {
                Value = value;
                ExpiryTime = expiryTime;
            }
        }
    }
}
