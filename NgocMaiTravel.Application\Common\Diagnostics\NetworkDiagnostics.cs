using System;
using System.Diagnostics;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Common.Diagnostics
{
    /// <summary>
    /// Network diagnostics utilities for troubleshooting connection issues
    /// </summary>
    public static class NetworkDiagnostics
    {
        /// <summary>
        /// Test connectivity to a specific host and port
        /// </summary>
        public static async Task<bool> TestConnectivityAsync(string host, int port, int timeoutMs = 5000)
        {
            try
            {
                using var client = new TcpClient();
                var connectTask = client.ConnectAsync(host, port);
                var timeoutTask = Task.Delay(timeoutMs);
                
                var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                
                if (completedTask == timeoutTask)
                {
                    Debug.WriteLine($"Connection to {host}:{port} timed out after {timeoutMs}ms");
                    return false;
                }
                
                if (connectTask.IsFaulted)
                {
                    Debug.WriteLine($"Connection to {host}:{port} failed: {connectTask.Exception?.GetBaseException().Message}");
                    return false;
                }
                
                Debug.WriteLine($"Successfully connected to {host}:{port}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error testing connectivity to {host}:{port}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Ping a host to test basic connectivity
        /// </summary>
        public static async Task<bool> PingHostAsync(string host, int timeoutMs = 5000)
        {
            try
            {
                using var ping = new Ping();
                var reply = await ping.SendPingAsync(host, timeoutMs);
                
                if (reply.Status == IPStatus.Success)
                {
                    Debug.WriteLine($"Ping to {host} successful: {reply.RoundtripTime}ms");
                    return true;
                }
                else
                {
                    Debug.WriteLine($"Ping to {host} failed: {reply.Status}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error pinging {host}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get network adapter information
        /// </summary>
        public static void LogNetworkAdapterInfo()
        {
            try
            {
                var adapters = NetworkInterface.GetAllNetworkInterfaces();
                Debug.WriteLine("=== Network Adapter Information ===");
                
                foreach (var adapter in adapters)
                {
                    if (adapter.OperationalStatus == OperationalStatus.Up)
                    {
                        Debug.WriteLine($"Adapter: {adapter.Name}");
                        Debug.WriteLine($"  Type: {adapter.NetworkInterfaceType}");
                        Debug.WriteLine($"  Status: {adapter.OperationalStatus}");
                        Debug.WriteLine($"  Speed: {adapter.Speed / 1_000_000} Mbps");
                        
                        var ipProps = adapter.GetIPProperties();
                        foreach (var ip in ipProps.UnicastAddresses)
                        {
                            Debug.WriteLine($"  IP: {ip.Address}");
                        }
                        Debug.WriteLine("");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting network adapter info: {ex.Message}");
            }
        }

        /// <summary>
        /// Test DNS resolution
        /// </summary>
        public static async Task<bool> TestDnsResolutionAsync(string hostname)
        {
            try
            {
                var addresses = await global::System.Net.Dns.GetHostAddressesAsync(hostname);
                Debug.WriteLine($"DNS resolution for {hostname}:");
                foreach (var addr in addresses)
                {
                    Debug.WriteLine($"  {addr}");
                }
                return addresses.Length > 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"DNS resolution failed for {hostname}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Comprehensive network diagnostics for API connectivity issues
        /// </summary>
        public static async Task<string> DiagnoseApiConnectivityAsync(string apiUrl)
        {
            var results = new global::System.Text.StringBuilder();
            results.AppendLine($"=== Network Diagnostics for {apiUrl} ===");
            results.AppendLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            results.AppendLine();

            try
            {
                var uri = new Uri(apiUrl);
                var host = uri.Host;
                var port = uri.Port;

                // Test DNS resolution
                results.AppendLine("1. DNS Resolution Test:");
                var dnsSuccess = await TestDnsResolutionAsync(host);
                results.AppendLine($"   Result: {(dnsSuccess ? "SUCCESS" : "FAILED")}");
                results.AppendLine();

                // Test ping
                results.AppendLine("2. Ping Test:");
                var pingSuccess = await PingHostAsync(host);
                results.AppendLine($"   Result: {(pingSuccess ? "SUCCESS" : "FAILED")}");
                results.AppendLine();

                // Test TCP connectivity
                results.AppendLine("3. TCP Connectivity Test:");
                var tcpSuccess = await TestConnectivityAsync(host, port);
                results.AppendLine($"   Result: {(tcpSuccess ? "SUCCESS" : "FAILED")}");
                results.AppendLine();

                // Network adapter info
                results.AppendLine("4. Network Adapter Information:");
                LogNetworkAdapterInfo();
                results.AppendLine();

                // Overall assessment
                results.AppendLine("5. Overall Assessment:");
                if (dnsSuccess && pingSuccess && tcpSuccess)
                {
                    results.AppendLine("   Network connectivity appears to be working correctly.");
                    results.AppendLine("   The issue may be related to:");
                    results.AppendLine("   - Application-level timeouts");
                    results.AppendLine("   - Server-side rate limiting");
                    results.AppendLine("   - Authentication issues");
                    results.AppendLine("   - Firewall/proxy blocking HTTP requests");
                }
                else
                {
                    results.AppendLine("   Network connectivity issues detected:");
                    if (!dnsSuccess) results.AppendLine("   - DNS resolution failed");
                    if (!pingSuccess) results.AppendLine("   - Host unreachable");
                    if (!tcpSuccess) results.AppendLine("   - TCP connection failed");
                }
            }
            catch (Exception ex)
            {
                results.AppendLine($"Error during diagnostics: {ex.Message}");
            }

            return results.ToString();
        }
    }
}
