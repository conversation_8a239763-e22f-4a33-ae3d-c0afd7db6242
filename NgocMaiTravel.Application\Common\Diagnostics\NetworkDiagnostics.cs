using System;
using System.Diagnostics;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Common.Diagnostics
{
    /// <summary>
    /// Network diagnostics utilities for troubleshooting connection issues
    /// </summary>
    public static class NetworkDiagnostics
    {
        /// <summary>
        /// Test connectivity to a specific host and port with detailed diagnostics
        /// </summary>
        public static async Task<(bool Success, string Details)> TestConnectivityDetailedAsync(string host, int port, int timeoutMs = 5000)
        {
            var details = new global::System.Text.StringBuilder();
            details.AppendLine($"Testing TCP connection to {host}:{port}");

            try
            {
                using var client = new TcpClient();

                // Set socket options for better diagnostics
                client.ReceiveTimeout = timeoutMs;
                client.SendTimeout = timeoutMs;

                var connectTask = client.ConnectAsync(host, port);
                var timeoutTask = Task.Delay(timeoutMs);

                var startTime = DateTime.Now;
                var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                var elapsed = DateTime.Now - startTime;

                if (completedTask == timeoutTask)
                {
                    details.AppendLine($"❌ Connection timed out after {elapsed.TotalMilliseconds:F0}ms");
                    details.AppendLine($"   Timeout threshold: {timeoutMs}ms");
                    details.AppendLine($"   Possible causes:");
                    details.AppendLine($"   - Firewall blocking port {port}");
                    details.AppendLine($"   - Server not listening on port {port}");
                    details.AppendLine($"   - Network routing issues");
                    return (false, details.ToString());
                }

                if (connectTask.IsFaulted)
                {
                    var exception = connectTask.Exception?.GetBaseException();
                    details.AppendLine($"❌ Connection failed after {elapsed.TotalMilliseconds:F0}ms");
                    details.AppendLine($"   Error: {exception?.Message}");
                    details.AppendLine($"   Exception Type: {exception?.GetType().Name}");

                    if (exception is SocketException socketEx)
                    {
                        details.AppendLine($"   Socket Error Code: {socketEx.SocketErrorCode}");
                        details.AppendLine($"   Error Code: {socketEx.ErrorCode}");
                    }

                    return (false, details.ToString());
                }

                // Connection successful
                details.AppendLine($"✅ Connection successful in {elapsed.TotalMilliseconds:F0}ms");
                details.AppendLine($"   Local endpoint: {client.Client.LocalEndPoint}");
                details.AppendLine($"   Remote endpoint: {client.Client.RemoteEndPoint}");
                details.AppendLine($"   Socket connected: {client.Connected}");

                return (true, details.ToString());
            }
            catch (Exception ex)
            {
                details.AppendLine($"❌ Unexpected error: {ex.Message}");
                details.AppendLine($"   Exception Type: {ex.GetType().Name}");
                return (false, details.ToString());
            }
        }

        /// <summary>
        /// Test connectivity to a specific host and port (legacy method)
        /// </summary>
        public static async Task<bool> TestConnectivityAsync(string host, int port, int timeoutMs = 5000)
        {
            var (success, _) = await TestConnectivityDetailedAsync(host, port, timeoutMs);
            return success;
        }

        /// <summary>
        /// Ping a host to test basic connectivity
        /// </summary>
        public static async Task<bool> PingHostAsync(string host, int timeoutMs = 5000)
        {
            try
            {
                using var ping = new Ping();
                var reply = await ping.SendPingAsync(host, timeoutMs);
                
                if (reply.Status == IPStatus.Success)
                {
                    Debug.WriteLine($"Ping to {host} successful: {reply.RoundtripTime}ms");
                    return true;
                }
                else
                {
                    Debug.WriteLine($"Ping to {host} failed: {reply.Status}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error pinging {host}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get network adapter information
        /// </summary>
        public static void LogNetworkAdapterInfo()
        {
            try
            {
                var adapters = NetworkInterface.GetAllNetworkInterfaces();
                Debug.WriteLine("=== Network Adapter Information ===");
                
                foreach (var adapter in adapters)
                {
                    if (adapter.OperationalStatus == OperationalStatus.Up)
                    {
                        Debug.WriteLine($"Adapter: {adapter.Name}");
                        Debug.WriteLine($"  Type: {adapter.NetworkInterfaceType}");
                        Debug.WriteLine($"  Status: {adapter.OperationalStatus}");
                        Debug.WriteLine($"  Speed: {adapter.Speed / 1_000_000} Mbps");
                        
                        var ipProps = adapter.GetIPProperties();
                        foreach (var ip in ipProps.UnicastAddresses)
                        {
                            Debug.WriteLine($"  IP: {ip.Address}");
                        }
                        Debug.WriteLine("");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting network adapter info: {ex.Message}");
            }
        }

        /// <summary>
        /// Test DNS resolution
        /// </summary>
        public static async Task<bool> TestDnsResolutionAsync(string hostname)
        {
            try
            {
                var addresses = await global::System.Net.Dns.GetHostAddressesAsync(hostname);
                Debug.WriteLine($"DNS resolution for {hostname}:");
                foreach (var addr in addresses)
                {
                    Debug.WriteLine($"  {addr}");
                }
                return addresses.Length > 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"DNS resolution failed for {hostname}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Comprehensive network diagnostics for API connectivity issues
        /// </summary>
        public static async Task<string> DiagnoseApiConnectivityAsync(string apiUrl)
        {
            var results = new global::System.Text.StringBuilder();
            results.AppendLine($"=== Network Diagnostics for {apiUrl} ===");
            results.AppendLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            results.AppendLine();

            try
            {
                var uri = new Uri(apiUrl);
                var host = uri.Host;
                var port = uri.Port;

                // Test DNS resolution
                results.AppendLine("1. DNS Resolution Test:");
                var dnsSuccess = await TestDnsResolutionAsync(host);
                results.AppendLine($"   Result: {(dnsSuccess ? "SUCCESS" : "FAILED")}");
                results.AppendLine();

                // Test ping
                results.AppendLine("2. Ping Test:");
                var pingSuccess = await PingHostAsync(host);
                results.AppendLine($"   Result: {(pingSuccess ? "SUCCESS" : "FAILED")}");
                results.AppendLine();

                // Test TCP connectivity with detailed diagnostics
                results.AppendLine("3. TCP Connectivity Test:");
                var (tcpSuccess, tcpDetails) = await TestConnectivityDetailedAsync(host, port, 10000);
                results.AppendLine($"   Result: {(tcpSuccess ? "SUCCESS" : "FAILED")}");
                results.AppendLine($"   Details:");
                foreach (var line in tcpDetails.Split('\n'))
                {
                    if (!string.IsNullOrWhiteSpace(line))
                        results.AppendLine($"     {line.Trim()}");
                }
                results.AppendLine();

                // Test alternative ports
                results.AppendLine("4. Alternative Port Tests:");
                var commonPorts = new[] { 80, 443, 8080, 8443 };
                foreach (var testPort in commonPorts)
                {
                    if (testPort != port)
                    {
                        var (portSuccess, _) = await TestConnectivityDetailedAsync(host, testPort, 5000);
                        results.AppendLine($"   Port {testPort}: {(portSuccess ? "OPEN" : "CLOSED/FILTERED")}");
                    }
                }
                results.AppendLine();

                // Network adapter info
                results.AppendLine("5. Network Adapter Information:");
                LogNetworkAdapterInfo();
                results.AppendLine();

                // Overall assessment
                results.AppendLine("6. Overall Assessment:");
                if (dnsSuccess && pingSuccess && tcpSuccess)
                {
                    results.AppendLine("   ✅ Network connectivity appears to be working correctly.");
                    results.AppendLine("   The issue may be related to:");
                    results.AppendLine("   - Application-level timeouts");
                    results.AppendLine("   - Server-side rate limiting");
                    results.AppendLine("   - Authentication issues");
                    results.AppendLine("   - Firewall/proxy blocking HTTP requests");
                }
                else
                {
                    results.AppendLine("   ❌ Network connectivity issues detected:");
                    if (!dnsSuccess) results.AppendLine("   - DNS resolution failed");
                    if (!pingSuccess) results.AppendLine("   - Host unreachable via ICMP");
                    if (!tcpSuccess)
                    {
                        results.AppendLine($"   - TCP connection to port {port} failed");
                        results.AppendLine("   - Possible causes:");
                        results.AppendLine("     * Firewall blocking outbound connections");
                        results.AppendLine("     * Corporate proxy/gateway restrictions");
                        results.AppendLine("     * Server-side firewall blocking your IP");
                        results.AppendLine("     * Port-specific filtering");
                        results.AppendLine("     * Network routing issues");
                    }
                }
            }
            catch (Exception ex)
            {
                results.AppendLine($"Error during diagnostics: {ex.Message}");
            }

            return results.ToString();
        }
    }
}
