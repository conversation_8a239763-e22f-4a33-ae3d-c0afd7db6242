using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.DependencyInjection;
using NgocMaiTravel.ApiIntegration.Queue;
using NgocMaiTravel.Application.System.ActivityLog;
using NgocMaiTravel.Application.System.Origin;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities.Payment;
using NgocMaiTravel.ViewModels.Catalog.PaymentFeeConfig;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using UAParser;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace NgocMaiTravel.Application.Catalog.PaymentFeeConfig
{
    public class PaymentFeeConfigService : IPaymentFeeConfigService
    {
        private readonly NgocMaiTravelDbContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IOriginService _originService;
        private readonly IQueueService _queueService;

        public PaymentFeeConfigService(NgocMaiTravelDbContext context, IHttpContextAccessor httpContextAccessor, IOriginService originService, IQueueService queueService)
        {
            _context = context;
            _httpContextAccessor = httpContextAccessor;
            _originService = originService;
            _queueService = queueService;
        }

        public async Task<ApiResult<PagedResult<PaymentFeeVM>>> GetPagingAsync(PaymentFeePagingRequest request)
        {
            var query = _context.tblPaymentFeeConfigs.AsNoTracking().Where(fee => fee.TimeDeleted == null);

            // Lọc theo keyword
            if (!string.IsNullOrEmpty(request.Keyword))
            {
                query = query.Where(x => x.PaymentMethodCode.Contains(request.Keyword) || x.PaymentMethod.Contains(request.Keyword));
            }
            // Lọc theo trạng thái
            if (request.FilterStatus.HasValue)
            {
                query = request.FilterStatus.Value == 1 ? query.Where(x => x.IsActive) : query.Where(x => !x.IsActive);
            }
            // Lọc theo loại
            if (!string.IsNullOrEmpty(request.FilterType))
            {
                query = query.Where(x => x.Type == request.FilterType);
            }


            // Sắp xếp động nếu hợp lệ
            if (!string.IsNullOrEmpty(request.SortColumn))
            {
                if (typeof(tblPaymentFeeConfig).GetProperty(request.SortColumn, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance) != null)
                {
                    query = request.SortOrder?.ToLower() == "desc"
                    ? query.OrderByDescending(e => EF.Property<object>(e, request.SortColumn))
                    : query.OrderBy(e => EF.Property<object>(e, request.SortColumn));
                }
            }
            else
            {
                query = query.OrderByDescending(x => x.CreatedAt);
            }

            var totalRecords = await query.CountAsync();
            var skip = (request.PageIndex - 1) * request.PageSize;
            var dataRaw = await query
                .Skip(skip)
                .Take(request.PageSize)
                .ToListAsync();

            // Lấy thông tin UserName cho UpdateBy (nếu có)
            var userIds = dataRaw.Where(x => x.UpdateBy.HasValue).Select(x => x.UpdateBy.Value).Distinct().ToList();
            var userDict = _context.Users.Where(u => userIds.Contains(u.Id)).ToDictionary(u => u.Id, u => $"{u.LastName} {u.FirstName}" );

            // Gán Index là số thứ tự hiển thị
            var data = dataRaw.Select((fee, idx) => new PaymentFeeVM
            {
                Index = skip + idx + 1,
                PaymentMethodCode = fee.PaymentMethodCode,
                PaymentMethod = fee.PaymentMethod,
                Type = fee.Type,
                BaseFee = fee.BaseFee,
                PercentFee = fee.PercentFee,
                IsActive = fee.IsActive,
                CreateAt = fee.CreatedAt.ToString("dd/MM/yyyy HH:mm:ss"),
                UpdateAt = fee.UpdateAt.HasValue ? fee.UpdateAt.Value.ToString("dd/MM/yyyy HH:mm:ss") : null,
                UpdateBy = fee.UpdateBy.HasValue && userDict.ContainsKey(fee.UpdateBy.Value) ? userDict[fee.UpdateBy.Value] : null
            }).ToList();

            var pagedResult = new PagedResult<PaymentFeeVM>
            {
                TotalRecords = totalRecords,
                PageIndex = request.PageIndex,
                PageSize = request.PageSize,
                Items = data,
                From = totalRecords > 0 ? skip + 1 : 0,
                To = totalRecords > 0 ? Math.Min(request.PageIndex * request.PageSize, totalRecords) : 0
            };

            return new ApiSuccessResult<PagedResult<PaymentFeeVM>>(pagedResult);
        }

        public async Task<ApiResult<tblPaymentFeeConfig>> CreateAsync(PaymentFeeCreateModelRq model)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            var user = await _originService.CheckUserRequest(httpContext);
            if (user == null)
            {
                return new ApiErrorResult<tblPaymentFeeConfig>("Not found user");
            }
            var feePayment = new tblPaymentFeeConfig()
            {
                PaymentMethodCode = model.PaymentMethodCode,
                PaymentMethod = model.PaymentMethod,
                Type = model.Type,
                BaseFee = model.BaseFee,
                PercentFee = model.PercentFee,
                IsActive = model.IsActive,
                CreatedAt = DateTime.Now,
                CreateBy = user.Id
            };

            _context.tblPaymentFeeConfigs.Add(feePayment);
            var check = await _context.SaveChangesAsync();

            var ipAddress = _originService.GetClientIpAddress(httpContext);
            var userAgent = httpContext.Request.Headers["User-Agent"].ToString();

            if (check > 0)
            {
                EnqueueLog(httpContext, user.Id, "CREATE_PaymentFee_SUCCESS", $"Tạo mới phí xuất vé {feePayment.PaymentMethodCode} thành công", ipAddress, userAgent);
                return new ApiSuccessResult<tblPaymentFeeConfig>(feePayment);
            }
            EnqueueLog(httpContext, user.Id, "CREATE_PaymentFee_FAILED", $"Tạo mới phí xuất vé {feePayment.PaymentMethodCode} thất bại", ipAddress, userAgent);
            return new ApiErrorResult<tblPaymentFeeConfig>("create failde");
        }

        public async Task<ApiResult<tblPaymentFeeConfig>> UpdateAsync(PaymentFeeUpdateModelRq model)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            var user = await _originService.CheckUserRequest(httpContext);
            if (user == null) return new ApiErrorResult<tblPaymentFeeConfig>("Not found user");

            var item = await _context.tblPaymentFeeConfigs.Where(x => x.PaymentMethodCode == model.paymentMethodCode).FirstOrDefaultAsync();
            if (item == null) return new ApiErrorResult<tblPaymentFeeConfig>("Not found payment fee");

            item.PaymentMethod = model.PaymentMethod;
            item.Type = model.Type;
            item.BaseFee = model.BaseFee;
            item.PercentFee = model.PercentFee;
            item.IsActive = model.IsActive;
            item.UpdateAt = DateTime.Now;
            item.UpdateBy = user.Id;

            _context.tblPaymentFeeConfigs.Update(item);

            var check = await _context.SaveChangesAsync();

            var ipAddress = _originService.GetClientIpAddress(httpContext);
            var userAgent = httpContext.Request.Headers["User-Agent"].ToString();

            if (check > 0)
            {
                EnqueueLog(httpContext, user.Id, "UPDATE_PaymentFee_SUCCESS", $"Cập nhật phí xuất vé {item.PaymentMethodCode} thành công", ipAddress, userAgent);
                return new ApiSuccessResult<tblPaymentFeeConfig>(item);
            }
            EnqueueLog(httpContext, user.Id, "UPDATE_PaymentFee_FAILED", $"Cập nhật phí xuất vé {item.PaymentMethodCode} thất bại", ipAddress, userAgent);
            return new ApiErrorResult<tblPaymentFeeConfig>("update failde");
        }

        public async Task<ApiResult<bool>> DeleteAsync(string code)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            var user = await _originService.CheckUserRequest(httpContext);
            if (user == null) return new ApiErrorResult<bool>("Not found user");

            var item = await _context.tblPaymentFeeConfigs.Where(x => x.PaymentMethodCode == code).FirstOrDefaultAsync();
            if (item == null) return new ApiErrorResult<bool>("Not found payment fee");

            item.IsActive = false;
            item.TimeDeleted = DateTime.Now;
            item.DeletedBy = user.Id;

            _context.tblPaymentFeeConfigs.Update(item);

            var check = await _context.SaveChangesAsync();

            var ipAddress = _originService.GetClientIpAddress(httpContext);
            var userAgent = httpContext.Request.Headers["User-Agent"].ToString();

            if (check > 0)
            {
                EnqueueLog(httpContext, user.Id, "DELETE_PaymentFee_SUCCESS", $"Xóa phí xuất vé {item.PaymentMethodCode} thành công", ipAddress, userAgent);
                return new ApiSuccessResult<bool>();
            }
            EnqueueLog(httpContext, user.Id, "DELETE_PaymentFee_FAILED", $"Xóa phí xuất vé {item.PaymentMethodCode} thất bại", ipAddress, userAgent);
            return new ApiErrorResult<bool>("update failde");
        }

        private void EnqueueLog(HttpContext context, Guid userId, string action, string description, string ipAddress, string userAgent)
        {
            _queueService.Enqueue(async provider =>
            {
                var activityLogService = provider.GetRequiredService<IActivityLogService>();
                await activityLogService.LogAsync(userId, action, description, ipAddress, userAgent);
            });
        }

        public async Task<ApiResult<List<PaymentFeeCalculatorRp>>> CalculatorAmount(decimal amount)
        {
            var fees = await _context.tblPaymentFeeConfigs.AsNoTracking()
                .Where(f => f.IsActive && f.TimeDeleted == null)
                .ToListAsync();
            if (fees == null || !fees.Any())
            {
                return new ApiErrorResult<List<PaymentFeeCalculatorRp>>("No active payment fees found");
            }
            var results = fees.Select(fee => new PaymentFeeCalculatorRp
            {
                PaymentFeeCode = fee.PaymentMethodCode,
                TransactionFee = (decimal)Math.Ceiling(fee.BaseFee + (fee.PercentFee / 100) * amount),
                FinishAmount = (decimal)Math.Ceiling(amount + (fee.BaseFee + (fee.PercentFee / 100) * amount))
            }).ToList();
            return new ApiSuccessResult<List<PaymentFeeCalculatorRp>>(results);
        }
    }
}
