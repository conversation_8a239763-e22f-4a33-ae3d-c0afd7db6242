﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Data.Entities
{
    public class tblApiKeyLib
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid(); 
        [Required]
        [MaxLength(255)]
        public string Name { get; set; }
        [Required]
        [MaxLength(100)]
        public string XApiKey { get; set; } = string.Empty; 
        [Required]
        [MaxLength(255)]
        public string Domain { get; set; } = string.Empty; 
        [MaxLength(255)]
        public string? IPAddress { get; set; }
        [Required]
        public long RequestLimit { get; set; }
        [Required]
        public Guid CreatedBy { get; set; } 
        public Guid? UpdatedBy { get; set; } 
        public bool IsActive { get; set; } = true; 
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow; 
        public DateTime? UpdateAt { get; set; } = DateTime.UtcNow; 
        public DateTime? ExpiryDate { get; set; }

        public string? EmailRecive { get; set; }
        public string? SenderEmail { get; set; }
        public string? SmtpServer { get; set; }
        public int? EmailPort { get; set; }
        public string? EmailPass { get; set; }
    }
}
