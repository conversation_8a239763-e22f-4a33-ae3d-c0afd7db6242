using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NgocMaiTravel.Data.Entities.ESim
{
    /// <summary>
    /// ESim Sync Log entity
    /// </summary>
    [Table("tblESimSyncLog")]
    public class ESimSyncLog
    {
        /// <summary>
        /// Primary key
        /// </summary>
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// Sync type: FULL_SYNC, INCREMENTAL_SYNC, GROUP_SYNC
        /// </summary>
        [Required]
        [StringLength(50)]
        public string SyncType { get; set; } = string.Empty;

        /// <summary>
        /// Status: PENDING, RUNNING, SUCCESS, FAILED
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Sync start timestamp
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Sync end timestamp
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// Duration in milliseconds
        /// </summary>
        public int? Duration { get; set; }

        /// <summary>
        /// Total packages to process
        /// </summary>
        public int? TotalPackages { get; set; }

        /// <summary>
        /// Number of packages processed
        /// </summary>
        public int? ProcessedPackages { get; set; }

        /// <summary>
        /// Number of new packages added
        /// </summary>
        public int? NewPackages { get; set; }

        /// <summary>
        /// Number of packages updated
        /// </summary>
        public int? UpdatedPackages { get; set; }

        /// <summary>
        /// Error message if sync failed
        /// </summary>
        [Column(TypeName = "NVARCHAR(MAX)")]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// User or system that requested the sync
        /// </summary>
        [StringLength(255)]
        public string? RequestedBy { get; set; }

        /// <summary>
        /// Source API provider
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ApiSource { get; set; } = "ESimBlue";
    }
}
