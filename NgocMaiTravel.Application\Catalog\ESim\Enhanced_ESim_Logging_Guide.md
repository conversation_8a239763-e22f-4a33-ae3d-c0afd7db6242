# Enhanced ESim Service Logging Guide

## 📋 Overview

ESimService đã được cập nhật để sử dụng file-based logging system thay vì ILoggerService. Hệ thống mới cung cấp logging chi tiết hơn với context thông tin customer và performance tracking.

## 🔧 Key Changes

### **1. Dependencies Updated:**
- ❌ Removed: `ILoggerService`
- ✅ Added: `IESimFileLogger`

### **2. Enhanced Logging Features:**
- ✅ **Request/Response file storage**
- ✅ **Customer context logging**
- ✅ **Performance timing**
- ✅ **Detailed error tracking**
- ✅ **Trace ID correlation**

## 📊 Available Methods

### **Core Methods (Updated):**

#### **1. GetBalanceAsync()**
```csharp
var result = await _eSimService.GetBalanceAsync();
```
**Logging Features:**
- Request/response stored in files
- Performance timing
- Error tracking with stack traces

#### **2. SearchPlansAsync()**
```csharp
var result = await _eSimService.SearchPlansAsync(rq);
```
**Logging Features:**
- Full request parameters logged
- Package count in response
- Search performance metrics

#### **3. GetEsimProflieAsync()**
```csharp
var result = await _eSimService.GetEsimProflieAsync(serial);
```
**Logging Features:**
- Serial number tracking
- Profile data logging
- Error correlation

### **Enhanced Methods (New):**

#### **4. SearchPlansWithContextAsync()**
```csharp
var result = await _eSimService.SearchPlansWithContextAsync(
    rq, 
    customerEmail: "<EMAIL>",
    customerPhone: "+84123456789"
);
```
**Enhanced Features:**
- Customer context in logs
- Better traceability
- Customer journey tracking

#### **5. CreateOrderAsync()**
```csharp
var result = await _eSimService.CreateOrderAsync(
    planId: "plan_001",
    customerEmail: "<EMAIL>", 
    customerPhone: "+84123456789",
    orderId: "order_123"
);
```
**Order Tracking:**
- Complete order lifecycle logging
- Customer information correlation
- Order status tracking

#### **6. GetRecentLogsAsync()**
```csharp
var logs = await _eSimService.GetRecentLogsAsync(count: 100);
```
**Analytics:**
- Recent activity monitoring
- Quick troubleshooting
- Performance overview

#### **7. GetPerformanceStatsAsync()**
```csharp
var stats = await _eSimService.GetPerformanceStatsAsync(
    fromDate: DateTime.Today.AddDays(-7),
    toDate: DateTime.Now
);
```
**Performance Metrics:**
- Success/failure rates
- Average response times
- Top actions analysis

## 🌐 API Endpoints

### **Updated Controller Methods:**

#### **1. Basic Search**
```http
GET /api/ESim/search?type=vietnam&locationCode=VN
```

#### **2. Enhanced Search with Context**
```http
POST /api/ESim/search-with-context
Content-Type: application/json

{
  "searchRequest": {
    "type": "vietnam",
    "locationCode": "VN"
  },
  "customerEmail": "<EMAIL>",
  "customerPhone": "+84123456789",
  "source": "web"
}
```

#### **3. Get Balance**
```http
GET /api/ESim/balance
```

#### **4. Get Profile**
```http
GET /api/ESim/profile/SERIAL123456
```

#### **5. Create Order**
```http
POST /api/ESim/orders
Content-Type: application/json

{
  "planId": "plan_001",
  "customerEmail": "<EMAIL>",
  "customerPhone": "+84123456789",
  "customerName": "John Doe",
  "paymentMethod": "credit_card",
  "source": "web"
}
```

#### **6. Get Recent Logs (Admin)**
```http
GET /api/ESim/logs/recent?count=50
```

#### **7. Get Performance Stats (Admin)**
```http
GET /api/ESim/stats?fromDate=2024-12-01&toDate=2024-12-30
```

## 📁 Log File Structure

### **File Organization:**
```
C:\Logs\ESim\
├── 2024\
│   ├── 12\
│   │   └── 30\
│   │       ├── abc123_request_20241230_143022.json
│   │       ├── abc123_response_20241230_143025.json
│   │       ├── def456_request_20241230_144011.json
│   │       └── def456_response_20241230_144015.json
└── 2025\
```

### **Request File Example:**
```json
{
  "action": "search_plans_with_context",
  "timestamp": "2024-12-30T14:30:22.123Z",
  "traceId": "abc123def456",
  "customerEmail": "<EMAIL>",
  "customerPhone": "+84123456789",
  "searchRequest": {
    "type": "vietnam",
    "locationCode": "VN",
    "transID": "trans_001"
  }
}
```

### **Response File Example:**
```json
{
  "packageCount": 15,
  "packages": {
    "packageList": [
      {
        "id": 1,
        "name": "Vietnam 10GB Plan",
        "price": 500000,
        "extraData": {
          "volume": 10240,
          "duration": 30
        }
      }
    ]
  },
  "responseTime": "1.234s",
  "status": "success"
}
```

## 📈 Performance Monitoring

### **Database Queries for Analysis:**

#### **Success Rate by Action:**
```sql
SELECT 
    Action,
    COUNT(*) as TotalRequests,
    SUM(CASE WHEN Status = 'success' THEN 1 ELSE 0 END) as SuccessCount,
    (SUM(CASE WHEN Status = 'success' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as SuccessRate
FROM tblESimLogs 
WHERE Timestamp >= DATEADD(day, -7, GETDATE())
GROUP BY Action
ORDER BY TotalRequests DESC;
```

#### **Average Response Times:**
```sql
SELECT 
    Action,
    AVG(DATEDIFF(MILLISECOND, '00:00:00', Duration)) as AvgResponseTimeMs,
    MIN(DATEDIFF(MILLISECOND, '00:00:00', Duration)) as MinResponseTimeMs,
    MAX(DATEDIFF(MILLISECOND, '00:00:00', Duration)) as MaxResponseTimeMs
FROM tblESimLogs 
WHERE Status = 'success' 
    AND Duration IS NOT NULL
    AND Timestamp >= DATEADD(day, -7, GETDATE())
GROUP BY Action
ORDER BY AvgResponseTimeMs DESC;
```

#### **Customer Activity:**
```sql
SELECT 
    CustomerEmail,
    COUNT(*) as RequestCount,
    MAX(Timestamp) as LastActivity,
    COUNT(DISTINCT Action) as UniqueActions
FROM tblESimLogs 
WHERE CustomerEmail IS NOT NULL
    AND Timestamp >= DATEADD(day, -30, GETDATE())
GROUP BY CustomerEmail
ORDER BY RequestCount DESC;
```

## 🚨 Error Monitoring

### **Common Error Patterns:**
```sql
SELECT 
    Action,
    ErrorMessage,
    COUNT(*) as ErrorCount,
    MAX(Timestamp) as LastOccurrence
FROM tblESimLogs 
WHERE Status = 'error'
    AND Timestamp >= DATEADD(day, -7, GETDATE())
GROUP BY Action, ErrorMessage
ORDER BY ErrorCount DESC;
```

### **Error Rate Trends:**
```sql
SELECT 
    CAST(Timestamp as DATE) as Date,
    COUNT(*) as TotalRequests,
    SUM(CASE WHEN Status = 'error' THEN 1 ELSE 0 END) as ErrorCount,
    (SUM(CASE WHEN Status = 'error' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)) as ErrorRate
FROM tblESimLogs 
WHERE Timestamp >= DATEADD(day, -30, GETDATE())
GROUP BY CAST(Timestamp as DATE)
ORDER BY Date DESC;
```

## 🔧 Configuration

### **appsettings.json:**
```json
{
  "ESimLogging": {
    "BasePath": "C:\\Logs\\ESim",
    "RetentionDays": 30,
    "MaxFileSize": "10MB"
  }
}
```

### **Dependency Injection:**
```csharp
// Program.cs
builder.Services.AddTransient<IESimFileLogger, ESimFileLogger>();
builder.Services.AddTransient<IESimService, ESimService>();
```

## ✅ Benefits of Enhanced Logging

1. **Detailed Audit Trail**: Complete request/response data in files
2. **Customer Journey Tracking**: Link activities to specific customers
3. **Performance Monitoring**: Detailed timing and success metrics
4. **Scalable Storage**: File-based storage prevents database bloat
5. **Easy Troubleshooting**: Trace IDs correlate related activities
6. **Analytics Ready**: Structured data for business intelligence

## 🔍 Troubleshooting

### **Common Issues:**

1. **File Permission Errors**: Ensure log directory has write permissions
2. **Disk Space**: Monitor log directory size and cleanup old files
3. **Performance Impact**: File I/O is async and shouldn't block requests
4. **Missing Logs**: Check if IESimFileLogger is properly injected

### **Debug Commands:**
```csharp
// Check recent logs
var logs = await _eSimService.GetRecentLogsAsync(10);

// Get performance stats
var stats = await _eSimService.GetPerformanceStatsAsync();

// Read specific log files
var requestData = await _fileLogger.ReadRequestDataAsync(logId);
var responseData = await _fileLogger.ReadResponseDataAsync(logId);
```

This enhanced logging system provides comprehensive monitoring and troubleshooting capabilities for the ESim service while maintaining optimal performance.
