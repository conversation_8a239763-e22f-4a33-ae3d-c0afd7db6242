﻿using NgocMaiTravel.Data.Entities;
using NgocMaiTravel.ViewModels.Catalog.Flight;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Catalog.Flight
{
    public interface IFlightService
    {
        Task<ApiResult<bool>> SentRequest(FlightRequestVM request);
        Task<ApiResult<OrderTripVM>> TripAvailable(TripAvailableRequest request);
        Task<ApiResult<TripAvailableRequest>> RequestTrip(FlightRequestVM request);
        Task<ApiResult<PagedResult<FlightRequestPagingVM>>> PagingRequest(FlightRequestPagingRequest request);
        Task<ApiResult<PagedResult<PartnerTicketPagingVM>>> PartnerPagingRequest(PartnerTicketPagingRequest request);
        Task<ApiResult<FlightRequestDetails>> GetRequestByID(string id);
        Task<ApiResult<FlightRequestDetails>> GetRequestDetailsByID(string id);
        Task<ApiResult<bool>> UpdateStatus(string Id, int status);
        Task<ApiResult<string>> RePayment(TripRePaymentRequest request);
        Task<ApiResult<bool>> AddNoteUser(FlightNoteUserRq request);

    }
}
