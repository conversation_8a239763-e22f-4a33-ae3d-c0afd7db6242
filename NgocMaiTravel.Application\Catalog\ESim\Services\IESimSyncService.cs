using NgocMaiTravel.Data.Entities.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;

namespace NgocMaiTravel.Application.Catalog.ESim.Services
{
    /// <summary>
    /// ESim Sync Service Interface
    /// </summary>
    public interface IESimSyncService
    {
        /// <summary>
        /// Queue full sync from API to database
        /// </summary>
        Task QueueFullSyncAsync(string? requestedBy = null);

        /// <summary>
        /// Queue incremental sync from API to database
        /// </summary>
        Task QueueIncrementalSyncAsync(string? requestedBy = null);

        /// <summary>
        /// Queue sync for specific group
        /// </summary>
        Task QueueGroupSyncAsync(string groupCode, string? requestedBy = null);

        /// <summary>
        /// Execute full sync immediately
        /// </summary>
        Task<ESimSyncLog> ExecuteFullSyncAsync(string? requestedBy = null);

        /// <summary>
        /// Execute incremental sync immediately
        /// </summary>
        Task<ESimSyncLog> ExecuteIncrementalSyncAsync(string? requestedBy = null);

        /// <summary>
        /// Execute group sync immediately
        /// </summary>
        Task<ESimSyncLog> ExecuteGroupSyncAsync(string groupCode, string? requestedBy = null);

        /// <summary>
        /// Check if sync is needed based on last sync time
        /// </summary>
        Task<bool> IsSyncNeededAsync(TimeSpan maxAge);

        /// <summary>
        /// Get sync status
        /// </summary>
        Task<ESimSyncLog?> GetLatestSyncStatusAsync();

        /// <summary>
        /// Convert API packages to database entities
        /// </summary>
        Task<IEnumerable<ESimPackage>> ConvertApiPackagesToEntitiesAsync(IEnumerable<EsimPackagesRp> apiPackages);

        /// <summary>
        /// Map packages to groups based on location codes
        /// </summary>
        Task<IEnumerable<ESimGroupPackage>> MapPackagesToGroupsAsync(IEnumerable<ESimPackage> packages);

        /// <summary>
        /// Get or create group by location code
        /// </summary>
        Task<ESimGroup> GetOrCreateGroupAsync(string locationCode, string? locationName = null);
    }

    /// <summary>
    /// ESim Sync Queue Item
    /// </summary>
    public class ESimSyncQueueItem
    {
        public string SyncType { get; set; } = string.Empty;
        public string? GroupCode { get; set; }
        public string? RequestedBy { get; set; }
        public DateTime QueuedAt { get; set; } = DateTime.UtcNow;
        public int Priority { get; set; } = 0; // Higher number = higher priority
    }

    /// <summary>
    /// Sync Configuration
    /// </summary>
    public class ESimSyncConfiguration
    {
        public TimeSpan CacheExpiry { get; set; } = TimeSpan.FromHours(6); // Cache expires after 6 hours
        public TimeSpan BackgroundSyncInterval { get; set; } = TimeSpan.FromHours(1); // Background sync every hour
        public int MaxRetryAttempts { get; set; } = 3;
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromMinutes(5);
        public int BatchSize { get; set; } = 100; // Process packages in batches
        public bool EnableBackgroundSync { get; set; } = true;
        public bool EnableQueueProcessing { get; set; } = true;
    }
}
