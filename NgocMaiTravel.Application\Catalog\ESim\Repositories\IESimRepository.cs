
using NgocMaiTravel.Data.Entities.ESim;

namespace NgocMaiTravel.Application.Catalog.ESim.Repositories
{
    /// <summary>
    /// ESim Repository Interface
    /// </summary>
    public interface IESimRepository
    {
        // Group operations
        Task<IEnumerable<ESimGroup>> GetAllGroupsAsync(bool includeInactive = false);
        Task<ESimGroup?> GetGroupByCodeAsync(string groupCode);
        Task<ESimGroup?> GetGroupByIdAsync(long groupId);
        Task<ESimGroup> CreateGroupAsync(ESimGroup group);
        Task<ESimGroup> UpdateGroupAsync(ESimGroup group);
        Task<bool> DeleteGroupAsync(long groupId);

        // Package operations
        Task<IEnumerable<ESimPackage>> GetAllPackagesAsync(bool includeInactive = false);
        Task<ESimPackage?> GetPackageBySkuAsync(string sku);
        Task<ESimPackage?> GetPackageByIdAsync(long packageId);
        Task<ESimPackage?> GetPackageByExternalIdAsync(string externalPackageId);
        Task<ESimPackage> CreatePackageAsync(ESimPackage package);
        Task<ESimPackage> UpdatePackageAsync(ESimPackage package);
        Task<bool> DeletePackageAsync(long packageId);
        Task<IEnumerable<ESimPackage>> GetPackagesByGroupCodeAsync(string groupCode);

        // Group-Package mapping operations
        Task<ESimGroupPackage> AddPackageToGroupAsync(long groupId, long packageId, int displayOrder = 0);
        Task<bool> RemovePackageFromGroupAsync(long groupId, long packageId);
        Task<IEnumerable<ESimGroupPackage>> GetGroupPackagesAsync(long groupId, bool includeInactive = false);
        Task<IEnumerable<ESimGroupPackage>> GetPackageGroupsAsync(long packageId, bool includeInactive = false);

        // Search and filter operations
        Task<IEnumerable<ESimGroup>> SearchGroupsAsync(string? searchTerm = null, string? groupType = null);
        Task<IEnumerable<ESimPackage>> SearchPackagesAsync(
            string? searchTerm = null,
            string? groupCode = null,
            decimal? minPrice = null,
            decimal? maxPrice = null,
            long? minDataAmount = null,
            long? maxDataAmount = null,
            int? minValidityDays = null,
            int? maxValidityDays = null,
            string? packageType = null,
            bool? isUnlimited = null);

        // Sync operations
        Task<ESimSyncLog> CreateSyncLogAsync(ESimSyncLog syncLog);
        Task<ESimSyncLog> UpdateSyncLogAsync(ESimSyncLog syncLog);
        Task<ESimSyncLog?> GetLatestSyncLogAsync(string syncType);
        Task<IEnumerable<ESimSyncLog>> GetSyncLogsAsync(int take = 50);

        // Bulk operations
        Task<int> BulkCreatePackagesAsync(IEnumerable<ESimPackage> packages);
        Task<int> BulkUpdatePackagesAsync(IEnumerable<ESimPackage> packages);
        Task<int> BulkCreateGroupPackagesAsync(IEnumerable<ESimGroupPackage> groupPackages);

        // Cache management
        Task<DateTime?> GetLastSyncDateAsync();
        Task<bool> HasDataAsync();
        Task<int> GetTotalPackagesCountAsync();
        Task<int> GetTotalGroupsCountAsync();
    }
}
