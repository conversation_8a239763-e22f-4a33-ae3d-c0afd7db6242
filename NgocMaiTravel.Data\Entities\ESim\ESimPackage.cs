using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NgocMaiTravel.Data.Entities.ESim
{
    /// <summary>
    /// ESim Package entity
    /// </summary>
    [Table("tblESimPackage")]
    public class ESimPackage
    {
        /// <summary>
        /// Primary key
        /// </summary>
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// External package ID from API
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PackageId { get; set; } = string.Empty;

        /// <summary>
        /// Package SKU
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Sku { get; set; } = string.Empty;

        /// <summary>
        /// Package display name
        /// </summary>
        [Required]
        [StringLength(255)]
        public string PackageName { get; set; } = string.Empty;

        /// <summary>
        /// Package description
        /// </summary>
        [StringLength(2000)]
        public string? Description { get; set; }

        /// <summary>
        /// Data amount in bytes
        /// </summary>
        public long DataAmount { get; set; }

        /// <summary>
        /// Validity period in days
        /// </summary>
        public int ValidityDays { get; set; }

        /// <summary>
        /// Package price
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Price { get; set; }

        /// <summary>
        /// Price currency
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Currency { get; set; } = "USD";

        /// <summary>
        /// Original price before discount
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? OriginalPrice { get; set; }

        /// <summary>
        /// Discount percentage
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal? DiscountPercent { get; set; }

        /// <summary>
        /// Package type: DATA, VOICE, SMS, COMBO
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PackageType { get; set; } = "DATA";

        /// <summary>
        /// Network type: 3G, 4G, 5G
        /// </summary>
        [StringLength(50)]
        public string? NetworkType { get; set; }

        /// <summary>
        /// Whether data is unlimited
        /// </summary>
        public bool IsUnlimited { get; set; } = false;

        /// <summary>
        /// Whether top-up is supported
        /// </summary>
        public bool IsTopUpSupported { get; set; } = false;

        /// <summary>
        /// Whether the package is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Source API provider
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ApiSource { get; set; } = "ESimBlue";

        /// <summary>
        /// JSON data from external API
        /// </summary>
        [Column(TypeName = "NVARCHAR(MAX)")]
        public string? ExternalData { get; set; }

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Last sync from API timestamp
        /// </summary>
        public DateTime? LastSyncDate { get; set; }

        /// <summary>
        /// User who created the record
        /// </summary>
        [StringLength(255)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// User who last updated the record
        /// </summary>
        [StringLength(255)]
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// Navigation property to group-package mappings
        /// </summary>
        public virtual ICollection<ESimGroupPackage> GroupPackages { get; set; } = new List<ESimGroupPackage>();

        /// <summary>
        /// Navigation property to groups (through GroupPackages)
        /// </summary>
        public virtual IEnumerable<ESimGroup> Groups => GroupPackages.Select(gp => gp.Group);
    }
}
