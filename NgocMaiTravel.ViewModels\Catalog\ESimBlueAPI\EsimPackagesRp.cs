using global::System.Globalization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI
{
        public partial class EsimPackagesRp
        {
            [JsonProperty("packageList")]
            public PackageList[] PackageList { get; set; }
        }

        public partial class PackageList
        {
            [JsonProperty("id")]
            public long Id { get; set; }

            [JsonProperty("sku")]
            public string Sku { get; set; }

            [JsonProperty("name")]
            public string Name { get; set; }

            [JsonProperty("model")]
            public string Model { get; set; }

            [JsonProperty("regional")]
            public string Regional { get; set; }

            [JsonProperty("typePackage")]
            public long TypePackage { get; set; }

            [JsonProperty("price")]
            public long Price { get; set; }

            [JsonProperty("currencyCode")]
            public string CurrencyCode { get; set; }

            [JsonProperty("imageUrl")]
            public string ImageUrl { get; set; }

            [JsonProperty("periodNum")]
            public object PeriodNum { get; set; }

            [JsonProperty("extraData")]
            public ExtraData ExtraData { get; set; }
        }

        public partial class ExtraData
        {
            [JsonProperty("slug")]
            public string Slug { get; set; }

            [JsonProperty("image")]
            public string Image { get; set; }

            [JsonProperty("speed")]
            public string Speed { get; set; }

            [JsonProperty("title")]
            public string Title { get; set; }

        [JsonProperty("volume", NullValueHandling = NullValueHandling.Ignore)]
        [JsonConverter(typeof(VolumeConverter))]
        public Volume Volume { get; set; }

        [JsonProperty("dataType")]
            public long DataType { get; set; }

            [JsonProperty("duration", NullValueHandling = NullValueHandling.Ignore)]
            public long? Duration { get; set; }

            [JsonProperty("location", NullValueHandling = NullValueHandling.Ignore)]
            public string Location { get; set; }

            [JsonProperty("operator")]
            public Operator Operator { get; set; }

            [JsonProperty("smsStatus")]
            public long SmsStatus { get; set; }

            [JsonProperty("activeType")]
            public long ActiveType { get; set; }

            [JsonProperty("unusedValidTime")]
            public long? UnusedValidTime { get; set; }

            [JsonProperty("supportTopUpType")]
            public long SupportTopUpType { get; set; }
        }

        public partial class Operator
        {
            [JsonProperty("type", NullValueHandling = NullValueHandling.Ignore)]
            public string? Type { get; set; }

            [JsonProperty("coverages")]
            public Coverages Coverages { get; set; }

            [JsonProperty("remark", NullValueHandling = NullValueHandling.Ignore)]
            public string Remark { get; set; }
        }

        public partial class Coverage
        {
            [JsonProperty("locationCode")]
            public string LocationCode { get; set; }

            [JsonProperty("locationLogo")]
            public string LocationLogo { get; set; }

            [JsonProperty("locationName")]
            public string LocationName { get; set; }

            [JsonProperty("operatorList")]
            public OperatorList[] OperatorList { get; set; }
        }

        public partial class OperatorList
        {
            [JsonProperty("networkType")]
            public string NetworkType { get; set; }

            [JsonProperty("operatorName")]
            public string OperatorName { get; set; }
        }


        public partial struct Coverages
        {
            public Coverage[] CoverageArray;
            public string String;

            public static implicit operator Coverages(Coverage[] CoverageArray) => new Coverages { CoverageArray = CoverageArray };
            public static implicit operator Coverages(string String) => new Coverages { String = String };
        }

        public partial struct Volume
        {
            public long? Integer;
            public string String;

            public static implicit operator Volume(long value) => new Volume { Integer = value };
            public static implicit operator Volume(string value) => new Volume { String = value };

            public override string ToString() => Integer?.ToString() ?? String ?? string.Empty;
        }

    internal class VolumeConverter : JsonConverter
    {
        public override bool CanConvert(Type objectType)
        {
            return objectType == typeof(Volume);
        }

        public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Integer)
            {
                return new Volume { Integer = Convert.ToInt64(reader.Value) };
            }
            else if (reader.TokenType == JsonToken.Float)
            {
                return new Volume { Integer = Convert.ToInt64(reader.Value) };
            }
            else if (reader.TokenType == JsonToken.String)
            {
                return new Volume { String = reader.Value.ToString() };
            }
            else if (reader.TokenType == JsonToken.Null)
            {
                return new Volume(); // default
            }

            throw new JsonSerializationException($"Unexpected token when parsing Volume: {reader.TokenType}");
        }

        public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
        {
            var volume = (Volume)value;
            if (volume.Integer.HasValue)
            {
                writer.WriteValue(volume.Integer.Value);
            }
            else if (!string.IsNullOrEmpty(volume.String))
            {
                writer.WriteValue(volume.String);
            }
            else
            {
                writer.WriteNull();
            }
        }

        // Add the missing Singleton definition to fix the error
        public static readonly VolumeConverter Singleton = new VolumeConverter();
    }


    public partial class EsimPackagesRp
        {
            public static EsimPackagesRp FromJson(string json) => JsonConvert.DeserializeObject<EsimPackagesRp>(json, Converter.Settings);
        }

        public static class Serialize
        {
            public static string ToJson(this EsimPackagesRp self) => JsonConvert.SerializeObject(self, Converter.Settings);
        }

        internal static class Converter
        {
            public static readonly JsonSerializerSettings Settings = new JsonSerializerSettings
            {
                MetadataPropertyHandling = MetadataPropertyHandling.Ignore,
                DateParseHandling = DateParseHandling.None,
                Converters =
            {
                CoveragesConverter.Singleton,
                VolumeConverter.Singleton,
                new IsoDateTimeConverter { DateTimeStyles = DateTimeStyles.AssumeUniversal }
            },
            };
        }


        internal class CoveragesConverter : JsonConverter
        {
            public override bool CanConvert(Type t) => t == typeof(Coverages) || t == typeof(Coverages?);

            public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
            {
                switch (reader.TokenType)
                {
                    case JsonToken.String:
                    case JsonToken.Date:
                        var stringValue = serializer.Deserialize<string>(reader);
                        return new Coverages { String = stringValue };
                    case JsonToken.StartArray:
                        var arrayValue = serializer.Deserialize<Coverage[]>(reader);
                        return new Coverages { CoverageArray = arrayValue };
                }
                throw new Exception("Cannot unmarshal type Coverages");
            }

            public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
            {
                var value = (Coverages)untypedValue;
                if (value.String != null)
                {
                    serializer.Serialize(writer, value.String);
                    return;
                }
                if (value.CoverageArray != null)
                {
                    serializer.Serialize(writer, value.CoverageArray);
                    return;
                }
                throw new Exception("Cannot marshal type Coverages");
            }

            public static readonly CoveragesConverter Singleton = new CoveragesConverter();
        }


}
