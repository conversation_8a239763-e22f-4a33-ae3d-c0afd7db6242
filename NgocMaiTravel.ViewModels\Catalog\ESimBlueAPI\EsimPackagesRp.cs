using global::System.Globalization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;


namespace NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI
{
    public partial class EsimPackagesRp
    {
        [JsonProperty("packageList")]
        public PackageList[] PackageList { get; set; }
    }

    public partial class PackageList
    {
        [JsonProperty("id")]
        public long Id { get; set; }

        [JsonProperty("sku")]
        public string Sku { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("model")]
        public string Model { get; set; }

        [JsonProperty("regional")]
        public string Regional { get; set; }

        [JsonProperty("typePackage")]
        public long TypePackage { get; set; }

        [JsonProperty("price")]
        public long Price { get; set; }

        [JsonProperty("currencyCode")]
        public CurrencyCode CurrencyCode { get; set; }

        [JsonProperty("imageUrl")]
        public string ImageUrl { get; set; }

        [JsonProperty("periodNum")]
        public object PeriodNum { get; set; }

        [JsonProperty("extraData")]
        public ExtraData ExtraData { get; set; }
    }

    public partial class ExtraData
    {
        [JsonProperty("slug")]
        public string Slug { get; set; }

        [JsonProperty("image")]
        public string Image { get; set; }

        [JsonProperty("speed")]
        public Speed Speed { get; set; }

        [JsonProperty("title")]
        public string Title { get; set; }

        [JsonProperty("volume", NullValueHandling = NullValueHandling.Ignore)]
        public Volume? Volume { get; set; }

        [JsonProperty("dataType")]
        public long DataType { get; set; }

        [JsonProperty("duration", NullValueHandling = NullValueHandling.Ignore)]
        public long? Duration { get; set; }

        [JsonProperty("location", NullValueHandling = NullValueHandling.Ignore)]
        public string Location { get; set; }

        [JsonProperty("operator")]
        public Operator Operator { get; set; }

        [JsonProperty("smsStatus")]
        public long SmsStatus { get; set; }

        [JsonProperty("activeType")]
        public long ActiveType { get; set; }

        [JsonProperty("unusedValidTime")]
        public long? UnusedValidTime { get; set; }

        [JsonProperty("supportTopUpType")]
        public long SupportTopUpType { get; set; }
    }

    public partial class Operator
    {
        [JsonProperty("type", NullValueHandling = NullValueHandling.Ignore)]
        public TypeEnum? Type { get; set; }

        [JsonProperty("coverages")]
        public Coverages Coverages { get; set; }

        [JsonProperty("remark", NullValueHandling = NullValueHandling.Ignore)]
        public string Remark { get; set; }
    }

    public partial class Coverage
    {
        [JsonProperty("locationCode")]
        public string LocationCode { get; set; }

        [JsonProperty("locationLogo")]
        public string LocationLogo { get; set; }

        [JsonProperty("locationName")]
        public string LocationName { get; set; }

        [JsonProperty("operatorList")]
        public OperatorList[] OperatorList { get; set; }
    }

    public partial class OperatorList
    {
        [JsonProperty("networkType")]
        public NetworkType NetworkType { get; set; }

        [JsonProperty("operatorName")]
        public string OperatorName { get; set; }
    }

    public enum CurrencyCode { Vnd };

    public enum NetworkType { Lte, The3G, The4G, The4G4G, The5G };

    public enum TypeEnum { Global, Local };

    public enum Speed { The3G4G, The4G3G };

    public partial struct Coverages
    {
        public Coverage[] CoverageArray { get; set; }
        public string String { get; set; }

        public static implicit operator Coverages(Coverage[] CoverageArray) => new Coverages { CoverageArray = CoverageArray };
        public static implicit operator Coverages(string String) => new Coverages { String = String };
    }

    public partial struct Volume
    {
        public long? Integer { get; set; }
        public string String { get; set; }

        public static implicit operator Volume(long Integer) => new Volume { Integer = Integer };
        public static implicit operator Volume(string String) => new Volume { String = String };
    }

    public partial class EsimPackagesRp
    {
        public static EsimPackagesRp FromJson(string json) => JsonConvert.DeserializeObject<EsimPackagesRp>(json, Converter.Settings);
    }

    public static class Serialize
    {
        public static string ToJson(this EsimPackagesRp self) => JsonConvert.SerializeObject(self, Converter.Settings);
    }

    internal static class Converter
    {
        public static readonly JsonSerializerSettings Settings = new JsonSerializerSettings
        {
            MetadataPropertyHandling = MetadataPropertyHandling.Ignore,
            DateParseHandling = DateParseHandling.None,
            Converters =
            {
                CurrencyCodeConverter.Singleton,
                CoveragesConverter.Singleton,
                NetworkTypeConverter.Singleton,
                TypeEnumConverter.Singleton,
                SpeedConverter.Singleton,
                VolumeConverter.Singleton,
                new IsoDateTimeConverter { DateTimeStyles = DateTimeStyles.AssumeUniversal }
            },
        };
    }

    internal class CurrencyCodeConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(CurrencyCode) || t == typeof(CurrencyCode?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            if (value == "VND")
            {
                return CurrencyCode.Vnd;
            }
            throw new Exception("Cannot unmarshal type CurrencyCode");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (CurrencyCode)untypedValue;
            if (value == CurrencyCode.Vnd)
            {
                serializer.Serialize(writer, "VND");
                return;
            }
            throw new Exception("Cannot marshal type CurrencyCode");
        }

        public static readonly CurrencyCodeConverter Singleton = new CurrencyCodeConverter();
    }

    internal class CoveragesConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(Coverages) || t == typeof(Coverages?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            switch (reader.TokenType)
            {
                case JsonToken.String:
                case JsonToken.Date:
                    var stringValue = serializer.Deserialize<string>(reader);
                    return new Coverages { String = stringValue };
                case JsonToken.StartArray:
                    var arrayValue = serializer.Deserialize<Coverage[]>(reader);
                    return new Coverages { CoverageArray = arrayValue };
            }
            throw new Exception("Cannot unmarshal type Coverages");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            var value = (Coverages)untypedValue;
            if (value.String != null)
            {
                serializer.Serialize(writer, value.String);
                return;
            }
            if (value.CoverageArray != null)
            {
                serializer.Serialize(writer, value.CoverageArray);
                return;
            }
            throw new Exception("Cannot marshal type Coverages");
        }

        public static readonly CoveragesConverter Singleton = new CoveragesConverter();
    }

    internal class NetworkTypeConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(NetworkType) || t == typeof(NetworkType?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "3G":
                    return NetworkType.The3G;
                case "4G":
                    return NetworkType.The4G;
                case "4G, 4G":
                    return NetworkType.The4G4G;
                case "5G":
                    return NetworkType.The5G;
                case "LTE":
                    return NetworkType.Lte;
            }
            throw new Exception("Cannot unmarshal type NetworkType");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (NetworkType)untypedValue;
            switch (value)
            {
                case NetworkType.The3G:
                    serializer.Serialize(writer, "3G");
                    return;
                case NetworkType.The4G:
                    serializer.Serialize(writer, "4G");
                    return;
                case NetworkType.The4G4G:
                    serializer.Serialize(writer, "4G, 4G");
                    return;
                case NetworkType.The5G:
                    serializer.Serialize(writer, "5G");
                    return;
                case NetworkType.Lte:
                    serializer.Serialize(writer, "LTE");
                    return;
            }
            throw new Exception("Cannot marshal type NetworkType");
        }

        public static readonly NetworkTypeConverter Singleton = new NetworkTypeConverter();
    }

    internal class TypeEnumConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(TypeEnum) || t == typeof(TypeEnum?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "global":
                    return TypeEnum.Global;
                case "local":
                    return TypeEnum.Local;
            }
            throw new Exception("Cannot unmarshal type TypeEnum");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (TypeEnum)untypedValue;
            switch (value)
            {
                case TypeEnum.Global:
                    serializer.Serialize(writer, "global");
                    return;
                case TypeEnum.Local:
                    serializer.Serialize(writer, "local");
                    return;
            }
            throw new Exception("Cannot marshal type TypeEnum");
        }

        public static readonly TypeEnumConverter Singleton = new TypeEnumConverter();
    }

    internal class SpeedConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(Speed) || t == typeof(Speed?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null) return null;
            var value = serializer.Deserialize<string>(reader);
            switch (value)
            {
                case "3G/4G":
                    return Speed.The3G4G;
                case "4G/3G":
                    return Speed.The4G3G;
            }
            throw new Exception("Cannot unmarshal type Speed");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            if (untypedValue == null)
            {
                serializer.Serialize(writer, null);
                return;
            }
            var value = (Speed)untypedValue;
            switch (value)
            {
                case Speed.The3G4G:
                    serializer.Serialize(writer, "3G/4G");
                    return;
                case Speed.The4G3G:
                    serializer.Serialize(writer, "4G/3G");
                    return;
            }
            throw new Exception("Cannot marshal type Speed");
        }

        public static readonly SpeedConverter Singleton = new SpeedConverter();
    }

    internal class VolumeConverter : JsonConverter
    {
        public override bool CanConvert(Type t) => t == typeof(Volume) || t == typeof(Volume?);

        public override object ReadJson(JsonReader reader, Type t, object existingValue, JsonSerializer serializer)
        {
            switch (reader.TokenType)
            {
                case JsonToken.Integer:
                    var integerValue = serializer.Deserialize<long>(reader);
                    return new Volume { Integer = integerValue };
                case JsonToken.String:
                case JsonToken.Date:
                    var stringValue = serializer.Deserialize<string>(reader);
                    return new Volume { String = stringValue };
            }
            throw new Exception("Cannot unmarshal type Volume");
        }

        public override void WriteJson(JsonWriter writer, object untypedValue, JsonSerializer serializer)
        {
            var value = (Volume)untypedValue;
            if (value.Integer != null)
            {
                serializer.Serialize(writer, value.Integer.Value);
                return;
            }
            if (value.String != null)
            {
                serializer.Serialize(writer, value.String);
                return;
            }
            throw new Exception("Cannot marshal type Volume");
        }

        public static readonly VolumeConverter Singleton = new VolumeConverter();
    }
}
