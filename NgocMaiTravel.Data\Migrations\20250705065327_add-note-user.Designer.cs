﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NgocMaiTravel.Data.EF;

#nullable disable

namespace NgocMaiTravel.Data.Migrations
{
    [DbContext(typeof(NgocMaiTravelDbContext))]
    [Migration("20250705065327_add-note-user")]
    partial class addnoteuser
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<System.Guid>", b =>
                {
                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.ToTable("AppRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<System.Guid>", b =>
                {
                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.ToTable("AppUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<System.Guid>", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LoginProvider")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProviderKey")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId");

                    b.ToTable("AppUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<System.Guid>", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("UserId", "RoleId");

                    b.ToTable("AppUserRoles", (string)null);

                    b.HasData(
                        new
                        {
                            UserId = new Guid("69bd714f-9576-45ba-b5b7-f00649be00de"),
                            RoleId = new Guid("8d04dce2-969a-435d-bba4-df3f325983dc")
                        });
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<System.Guid>", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(34)
                        .HasColumnType("nvarchar(34)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AppUserTokens", (string)null);

                    b.HasDiscriminator().HasValue("IdentityUserToken<Guid>");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Address", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Country")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HrefLinkDefault")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Image")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Province")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("SeoTitle")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("SortDescription")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime?>("TimeCreate")
                        .HasColumnType("datetime");

                    b.Property<Guid?>("UserAppId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserAppId");

                    b.ToTable("Address", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AddressTranslation", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Country")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LanguageId")
                        .IsRequired()
                        .HasColumnType("varchar(5)");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Province")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("SeoTitle")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("SortDescription")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("LanguageId");

                    b.ToTable("AddressTranslation", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Airport", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<Guid>("AirportTypeID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Province")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("SeoTitle")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("ShowInHome")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<DateTime>("TimeCreate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.HasKey("Id");

                    b.HasIndex("AirportTypeID")
                        .IsDescending();

                    b.HasIndex("SeoTitle")
                        .IsUnique();

                    b.ToTable("Airports", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AirportMeta.tblMetaAirportTranslate", b =>
                {
                    b.Property<string>("AirportIdent")
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)");

                    b.Property<string>("Lang")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("AirportIdent", "Lang");

                    b.ToTable("tblMetaAirportTranslates", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AirportMeta.tblMetaContinent", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("NameTranslate")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Order")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<bool>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.HasKey("Code");

                    b.ToTable("tblMetaContinents", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AirportMeta.tblMetaCountry", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("Capital")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Continent")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Currency")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Currency_Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Currency_Symbol")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Emoji")
                        .HasMaxLength(191)
                        .HasColumnType("nvarchar(191)")
                        .HasColumnName("emoji");

                    b.Property<string>("EmojiU")
                        .HasMaxLength(191)
                        .HasColumnType("nvarchar(191)")
                        .HasColumnName("emojiU");

                    b.Property<decimal?>("Latitude")
                        .HasColumnType("decimal(10, 8)")
                        .HasColumnName("latitude");

                    b.Property<decimal?>("Longitude")
                        .HasColumnType("decimal(11, 8)")
                        .HasColumnName("longitude");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Nationality")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Native")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("native");

                    b.Property<string>("PhoneCode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SubContinent")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Timezones")
                        .HasMaxLength(5350)
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tld")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("tld");

                    b.Property<string>("Translations")
                        .HasMaxLength(5000)
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Code");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("tblMetaCountries", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AirportMeta.tblMetaOurAirport", b =>
                {
                    b.Property<string>("Ident")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("AreaCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Continent")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int?>("ElevationFt")
                        .HasColumnType("int");

                    b.Property<string>("GpsCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("IataCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("IcaoCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("IsoCountry")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("IsoRegion")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Keywords")
                        .HasMaxLength(5000)
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("LatitudeDeg")
                        .HasColumnType("float");

                    b.Property<string>("LocalCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<double?>("LongitudeDeg")
                        .HasColumnType("float");

                    b.Property<string>("Municipality")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("Order")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<float>("Score")
                        .HasColumnType("real");

                    b.Property<float?>("TimeZone")
                        .HasColumnType("real");

                    b.Property<string>("Type")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Ident");

                    b.HasIndex("IataCode")
                        .HasDatabaseName("IX_tblOurAirports_IataCode");

                    b.HasIndex("IcaoCode")
                        .HasDatabaseName("IX_tblOurAirports_IcaoCode");

                    b.HasIndex("Ident")
                        .HasDatabaseName("IX_tblOurAirports_Ident");

                    b.HasIndex("LocalCode")
                        .HasDatabaseName("IX_tblOurAirports_LocalCode");

                    b.ToTable("tblMetaOurAirports", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AirportMeta.tblMetaRegion", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Continent")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("ISOCountry")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("KeyWords")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LocalCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NameTranslate")
                        .HasMaxLength(5000)
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Code");

                    b.ToTable("tblMetaRegions", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AirportMeta.tblMetaSubContinent", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("NameTranslate")
                        .IsRequired()
                        .HasMaxLength(5000)
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Code");

                    b.ToTable("tblMetaSubContinents", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppConfig", b =>
                {
                    b.Property<string>("Key")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Key");

                    b.ToTable("AppConfigs", (string)null);

                    b.HasData(
                        new
                        {
                            Key = "HomeTitle",
                            Value = "This is home page of NgocMaiTravelSolution"
                        },
                        new
                        {
                            Key = "HomeKeyword",
                            Value = "This is keyword of NgocMaiTravelSolution"
                        },
                        new
                        {
                            Key = "HomeDescription",
                            Value = "This is description of NgocMaiTravelSolution"
                        });
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppGroupUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CreateAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("CreateBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("GroupName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("SeoDescription")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("SeoTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdateAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("XApiKeyID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("currentUsers")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<int>("maxUser")
                        .HasColumnType("int");

                    b.Property<bool>("status")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("CreateBy");

                    b.HasIndex("GroupName")
                        .IsUnique();

                    b.HasIndex("SeoTitle")
                        .IsUnique();

                    b.HasIndex("UpdateBy");

                    b.ToTable("AppGroupUsers", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppLockUserHistory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValue(new DateTime(2025, 7, 5, 13, 53, 26, 960, DateTimeKind.Local).AddTicks(5866));

                    b.Property<string>("Note")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("UserId");

                    b.ToTable("AppLockUserHistories", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime>("TimeCreate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UserCreateID")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserCreateID");

                    b.ToTable("AppRoles", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("8d04dce2-969a-435d-bba4-df3f325983dc"),
                            Description = "Administrator role",
                            Name = "admin",
                            NormalizedName = "admin",
                            TimeCreate = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("7ca0f1fa-a22c-4c42-af6b-f3bd4a1f7571"),
                            Description = "Client role",
                            Name = "client",
                            NormalizedName = "client",
                            TimeCreate = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        },
                        new
                        {
                            Id = new Guid("0468ad51-08c4-495c-bbcd-51adce08164a"),
                            Description = "Employee role",
                            Name = "employee",
                            NormalizedName = "employee",
                            TimeCreate = new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified)
                        });
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Dob")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<Guid?>("GroupUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ImagePath")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("IsPartner")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastActivity")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NormalizedUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Token")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("TokenCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("TokenExpires")
                        .HasColumnType("datetime2");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("GroupUserId");

                    b.ToTable("AppUsers", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("69bd714f-9576-45ba-b5b7-f00649be00de"),
                            AccessFailedCount = 0,
                            Active = true,
                            ConcurrencyStamp = "2561198b-3558-41d4-ae24-92f4979fd02e",
                            Dob = new DateTime(2020, 1, 31, 0, 0, 0, 0, DateTimeKind.Unspecified),
                            Email = "<EMAIL>",
                            EmailConfirmed = true,
                            FirstName = "Đặng Quốc",
                            IsPartner = false,
                            LastName = "Vũ",
                            LockoutEnabled = false,
                            NormalizedEmail = "<EMAIL>",
                            NormalizedUserName = "admin",
                            PasswordHash = "AQAAAAIAAYagAAAAECA1eeToLn58Fie0ihR1CJDRaK/xlrZONjo2Ky/YwKIKyhOlMCAhi4BeGEkRwkUJrQ==",
                            PhoneNumberConfirmed = false,
                            SecurityStamp = "",
                            TwoFactorEnabled = false,
                            UserName = "admin"
                        });
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryAirport", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("SeoTitle")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<bool>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<DateTime>("TimeCreate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.HasKey("Id");

                    b.ToTable("CategoryAirports", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryHotel", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AppUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SeoTitle")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("SortOrder")
                        .IsRequired()
                        .HasColumnType("int");

                    b.Property<bool?>("Status")
                        .IsRequired()
                        .HasColumnType("bit");

                    b.Property<DateTime?>("TimeCreate")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.HasKey("Id");

                    b.HasIndex("AppUserId");

                    b.ToTable("CategoryHotels", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryNews", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("description");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("name");

                    b.Property<Guid?>("OwnerID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SeoTitle")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("seo_title");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("sort_order");

                    b.Property<bool?>("Status")
                        .HasColumnType("bit")
                        .HasColumnName("status");

                    b.Property<DateTime?>("TimeCreate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("category_news", (string)null);

                    b.HasData(
                        new
                        {
                            Id = new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab53"),
                            Description = "Điểm đến",
                            Name = "Điểm đến",
                            SeoTitle = "diem-den",
                            SortOrder = 1,
                            Status = true,
                            TimeCreate = new DateTime(2025, 7, 5, 13, 53, 27, 8, DateTimeKind.Local).AddTicks(5317),
                            UserId = new Guid("69bd714f-9576-45ba-b5b7-f00649be00de")
                        },
                        new
                        {
                            Id = new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab54"),
                            Description = "Khám phá",
                            Name = "Khám phá",
                            SeoTitle = "kham-pha",
                            SortOrder = 2,
                            Status = true,
                            TimeCreate = new DateTime(2025, 7, 5, 13, 53, 27, 8, DateTimeKind.Local).AddTicks(5354),
                            UserId = new Guid("69bd714f-9576-45ba-b5b7-f00649be00de")
                        });
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryTour", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("AppUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Name")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SeoTitle")
                        .HasMaxLength(50)
                        .IsUnicode(false)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int");

                    b.Property<bool?>("Status")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("TimeCreate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex(new[] { "AppUserId" }, "IX_CategoryTours_AppUserId");

                    b.ToTable("CategoryTours");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryVisa", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GetDate()");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("SeoTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("ShowInHome")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("UpdatedBy");

                    b.ToTable("CategoryVisas", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryVisa_Visa", b =>
                {
                    b.Property<Guid>("CategoryVisaId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("VisaId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("CategoryVisaId", "VisaId");

                    b.HasIndex("VisaId");

                    b.ToTable("CategoryVisa_Visa", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.ChatMessage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(2550)
                        .HasColumnType("nvarchar(2550)");

                    b.Property<string>("SenderId")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasMaxLength(2550)
                        .HasColumnType("nvarchar(2550)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("SenderId")
                        .IsDescending();

                    b.HasIndex("SessionId")
                        .IsDescending();

                    b.ToTable("ChatMessages", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Contact", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Context")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Email")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("Note")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<string>("Position")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<Guid?>("UserCheck")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserCheck");

                    b.ToTable("Contact", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Country", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Abbreviate2")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("Abbreviate3")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<string>("Continent")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NameVN")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("Countries", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.EmailContact", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("TimeCreate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.HasKey("Id");

                    b.ToTable("EmailContact", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.FlightRequest", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<int>("Adult")
                        .HasColumnType("int");

                    b.Property<string>("Airlines")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Arrival")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("Child")
                        .HasColumnType("int");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Depart")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("DepartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("Infant")
                        .HasColumnType("int");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("OwnerID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("Pnrs")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("ReturnDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("TicketNumbers")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("TimeCompletion")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("TimeCreate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<DateTime?>("TimeUpdate")
                        .HasColumnType("datetime2");

                    b.Property<long>("TotalPrice")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasDefaultValue(0L);

                    b.Property<string>("TransactionId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("UpdateBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserNote")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("VoucherCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("VoucherDiscount")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("Arrival");

                    b.HasIndex("Depart");

                    b.HasIndex("PhoneNumber");

                    b.ToTable("FlightRequests", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Hotel", b =>
                {
                    b.Property<Guid>("ID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Amenities")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<Guid>("CategoryHotelId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("EmailOwner")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("NumberOfRooms")
                        .HasColumnType("int");

                    b.Property<string>("Owner")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PhoneNumberOwner")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<decimal>("PricePerNight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Rating")
                        .HasColumnType("decimal(3,2)");

                    b.Property<string>("SeoTitle")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("ShowInHome")
                        .HasColumnType("bit");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<Guid?>("UserAppId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("YearStart")
                        .HasMaxLength(4)
                        .HasColumnType("int");

                    b.HasKey("ID");

                    b.HasIndex("CategoryHotelId");

                    b.HasIndex("UserAppId");

                    b.ToTable("Hotels", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.HotelBooking", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Adult")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<DateTime>("CheckIn")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOut")
                        .HasColumnType("datetime");

                    b.Property<int>("Children")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("CustomerEmail")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CustomerPhone")
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<string>("Note")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("NumberOfRoom")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<decimal?>("Pay")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("Price")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("Promotion")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<Guid>("RoomId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<decimal?>("Tax")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("Total")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<Guid?>("UserAppId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("RoomId");

                    b.HasIndex("UserAppId");

                    b.ToTable("HotelBookings", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.HotelImage", b =>
                {
                    b.Property<Guid>("HotelImageID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("HotelID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("TimeCreate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("UrlOnline")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("UrlPhysical")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("HotelImageID");

                    b.HasIndex("HotelID");

                    b.ToTable("HotelImages", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.HotelRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Adult")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<DateTime>("CheckIn")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("CheckOut")
                        .HasColumnType("datetime");

                    b.Property<int>("Children")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime");

                    b.Property<string>("CustomerEmail")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CustomerPhone")
                        .IsRequired()
                        .HasMaxLength(13)
                        .HasColumnType("nvarchar(13)");

                    b.Property<string>("HotelName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Note")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("NumberOfRoom")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("OtherRequest")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<decimal?>("Pay")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("Price")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("Promotion")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<decimal?>("Tax")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal?>("Total")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime");

                    b.Property<Guid?>("UserUpdateAppId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserUpdateAppId");

                    b.ToTable("HotelRequests", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Language", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(5)
                        .IsUnicode(false)
                        .HasColumnType("varchar(5)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.ToTable("Languages", (string)null);

                    b.HasData(
                        new
                        {
                            Id = "vi",
                            IsDefault = true,
                            Name = "Tiếng Việt"
                        },
                        new
                        {
                            Id = "en",
                            IsDefault = false,
                            Name = "English"
                        });
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.News", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<bool?>("Active")
                        .HasColumnType("bit")
                        .HasColumnName("active");

                    b.Property<Guid?>("CategoryId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("category_id");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)")
                        .HasColumnName("description");

                    b.Property<string>("Detail")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("detail");

                    b.Property<string>("ImagePath")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("image_path");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("name");

                    b.Property<Guid?>("OwnerID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SeoTitle")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("seo_title");

                    b.Property<bool?>("ShowInHome")
                        .HasColumnType("bit")
                        .HasColumnName("show_in_home");

                    b.Property<int?>("Stock")
                        .HasColumnType("int")
                        .HasColumnName("stock");

                    b.Property<DateTime?>("TimeCreate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<int>("viewCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("SeoTitle")
                        .IsUnique()
                        .HasFilter("[seo_title] IS NOT NULL");

                    b.HasIndex("UserId");

                    b.ToTable("news", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Order", b =>
                {
                    b.Property<Guid>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("Email")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("MethodPayment")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Pay")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(13)
                        .IsUnicode(false)
                        .HasColumnType("varchar(13)");

                    b.Property<decimal?>("Promotion")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<int?>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<decimal?>("Tax")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<DateTime?>("TimeCreate")
                        .HasColumnType("datetime");

                    b.Property<DateTime?>("TimeStart")
                        .HasColumnType("datetime");

                    b.Property<decimal?>("Total")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<Guid?>("TourId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserAppId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserOrder")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("OrderId");

                    b.HasIndex("TourId");

                    b.HasIndex("UserAppId");

                    b.ToTable("Order", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.OrderDetail", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("PaxInfo")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("PriceAdult")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("PriceBaby")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("PriceChildren25")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("PriceChildren611")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<int?>("QuantityAdult")
                        .HasColumnType("int");

                    b.Property<int?>("QuantityBaby")
                        .HasColumnType("int");

                    b.Property<int?>("QuantityChildren25")
                        .HasColumnType("int");

                    b.Property<int?>("QuantityChildren611")
                        .HasColumnType("int");

                    b.Property<DateTime?>("TimeCreate")
                        .HasColumnType("datetime");

                    b.Property<Guid?>("UserAppId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("OrderId");

                    b.HasIndex("UserAppId");

                    b.ToTable("OrderDetails", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Payment.tblPaymentFeeConfig", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<decimal>("BaseFee")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,4)")
                        .HasDefaultValue(0m);

                    b.Property<Guid>("CreateBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("PaymentMethod")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PaymentMethodCode")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("PercentFee")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(5,4)")
                        .HasDefaultValue(0m);

                    b.Property<DateTime?>("TimeDeleted")
                        .HasColumnType("datetime2");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdateAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateBy")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("ID");

                    b.ToTable("tblPaymentFeeConfigs");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Payment.tblVoucher", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("CreateBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("DeletedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(5000)
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DiscountPercent")
                        .HasColumnType("decimal(7,4)");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<decimal?>("MaxDiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MinOrderAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("TimeDeleted")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("UsageLimit")
                        .HasColumnType("int");

                    b.Property<int>("UsedCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("VoucherName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("ID");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("tblVoucher", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Promotion", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ConditionsApply")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal>("DiscountAmount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 2)")
                        .HasDefaultValue(0m);

                    b.Property<int>("DiscountPercentage")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<DateTime>("ModifiedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PathOnline")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("PathPhysical")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("SeoServiceTitle")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("SeoTitle")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ServiceApply")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("ShowBanner")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTime>("TimeEnd")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<DateTime>("TimeStart")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<Guid?>("UserAppId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ViewCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("linkHref")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("UserAppId");

                    b.ToTable("Promotions", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Province", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("FullName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FullNameEN")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Latitude")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Longitude")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NameEN")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("Provinces", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Room", b =>
                {
                    b.Property<Guid>("RoomID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("Area")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Capacity")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<Guid>("HotelID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("PricePerNight")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("RoomType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("UserAppId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("RoomID");

                    b.HasIndex("HotelID");

                    b.HasIndex("UserAppId");

                    b.ToTable("Rooms", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Screen", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("description");

                    b.Property<string>("Icon")
                        .HasMaxLength(10000)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("icon");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("isActive");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("isDeleted");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("parent_id");

                    b.Property<string>("Path")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("path");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("title");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("updated_by");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .HasDatabaseName("IX_Screen_Code");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("ParentId");

                    b.HasIndex("UpdatedBy");

                    b.ToTable("Screens", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.ScreenRole", b =>
                {
                    b.Property<Guid>("ScreenId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserCreate")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserUpdate")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("ScreenId", "RoleId");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserCreate");

                    b.HasIndex("UserUpdate");

                    b.ToTable("tblScreenRoles", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.ScreenUser", b =>
                {
                    b.Property<Guid>("ScreenId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("UserCreate")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserUpdate")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("ScreenId", "UserId");

                    b.HasIndex("UserCreate");

                    b.HasIndex("UserId");

                    b.HasIndex("UserUpdate");

                    b.ToTable("tblScreenUsers", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Tour", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AddressEnd")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressStart")
                        .HasMaxLength(25)
                        .HasColumnType("nvarchar(25)");

                    b.Property<string>("BookingInstructions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("CategoryTourId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(15)
                        .IsUnicode(true)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryEnd")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("CountryStart")
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<int>("CutOffDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("DateStart")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PayInstructions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("Price")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<string>("PriceChildren")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PriceInclude")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PriceNotInclude")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PromotionPrice")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<string>("ScheduleShort")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<string>("SeoTitle")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("ShowInHome")
                        .HasColumnType("bit");

                    b.Property<int?>("SortOrder")
                        .HasColumnType("int");

                    b.Property<bool?>("Status")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("TimeCreate")
                        .HasColumnType("datetime");

                    b.Property<string>("TimeLong")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Transport")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("TypeOwner")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<Guid?>("UserAppId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Visa")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AddressStart")
                        .HasDatabaseName("IX_Tours_address_start");

                    b.HasIndex("CategoryTourId");

                    b.HasIndex("Code")
                        .HasDatabaseName("IX_Tours_Code");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_Tours_Name");

                    b.HasIndex("SeoTitle")
                        .HasDatabaseName("IX_Tours_seo_title");

                    b.HasIndex("TypeOwner")
                        .HasDatabaseName("IX_Tours_TypeOwner");

                    b.HasIndex("UserAppId");

                    b.ToTable("Tours", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourAddress", b =>
                {
                    b.Property<Guid>("TourId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("AddressId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("TimeCreate")
                        .HasMaxLength(10)
                        .HasColumnType("datetime2")
                        .IsFixedLength();

                    b.Property<Guid?>("UserAppId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("TourId", "AddressId");

                    b.HasIndex("AddressId");

                    b.HasIndex("UserAppId");

                    b.ToTable("TourAddress", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourFeedback", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<Guid>("TourId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UserCreated")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("TourId");

                    b.ToTable("TourFeedbacks", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourFeedbackImage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ImagePath")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<string>("OnlinePath")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<Guid>("TourFeedbackId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TourFeedbackId");

                    b.ToTable("TourFeedbackImages", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourImage", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<Guid>("TourId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("TourID");

                    b.Property<string>("ImagePath")
                        .HasMaxLength(150)
                        .HasColumnType("nvarchar(150)");

                    b.Property<DateTime?>("TimeCreate")
                        .HasColumnType("datetime");

                    b.Property<Guid?>("UserAppId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id", "TourId");

                    b.HasIndex("TourId");

                    b.HasIndex("UserAppId");

                    b.ToTable("TourImage", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourPrice", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Depart")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Destination")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<decimal?>("PriceAdult")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("PriceBaby")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<decimal?>("PriceChildren")
                        .HasColumnType("decimal(18, 0)");

                    b.Property<DateTime?>("TimeCreate")
                        .HasColumnType("datetime");

                    b.Property<string>("TimeStart")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<Guid?>("TourId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserAppId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TourId");

                    b.HasIndex("UserAppId");

                    b.ToTable("TourPrice", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourTour", b =>
                {
                    b.Property<Guid>("TourId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("TourID");

                    b.Property<int>("TourDay")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Note")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime?>("TimeCreate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UserAppId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("TourId", "TourDay")
                        .HasName("PK_TourTours_1");

                    b.HasIndex(new[] { "UserAppId" }, "IX_TourTours_UserAppId");

                    b.ToTable("TourTours", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Translate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreateAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<Guid>("CreateBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("EN")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("EN");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<Guid>("ScreenID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdateAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdateBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("VN")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("VN");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("CreateBy");

                    b.HasIndex("ScreenID");

                    SqlServerIndexBuilderExtensions.SortInTempDb(b.HasIndex("ScreenID"), true);

                    b.HasIndex("UpdateBy");

                    b.ToTable("Translates", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Visa", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("Code")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Continent")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GetDate()");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("PathOnline")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("PathPhysical")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("ShowInHome")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ViewCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.HasIndex("CreatedBy");

                    b.HasIndex("UpdatedBy");

                    b.ToTable("Visas", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.VisaRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Country")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime>("DateRequest")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateReturn")
                        .HasColumnType("datetime2");

                    b.Property<string>("EmailCustomer")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NameCustomer")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Note")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("PhoneCustomer")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<int?>("Quantity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<bool>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("TimeUse")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<Guid?>("UserReplyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("VisaType")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("UserReplyId");

                    b.ToTable("VisaRequests", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.VisaTranslation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Details")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EffectivePeriod")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LanguageId")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ReviewTime")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("SeoTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("TypeVisa")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid>("VisaId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("VisaId");

                    b.ToTable("VisaTranslations", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldCity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nchar(2)")
                        .HasColumnName("country_code")
                        .IsFixedLength();

                    b.Property<int>("CountryId")
                        .HasColumnType("int")
                        .HasColumnName("country_id");

                    b.Property<string>("CountryName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("country_name");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<bool>("Flag")
                        .HasColumnType("bit")
                        .HasColumnName("flag");

                    b.Property<decimal>("Latitude")
                        .HasColumnType("decimal(10, 8)")
                        .HasColumnName("latitude");

                    b.Property<decimal>("Longitude")
                        .HasColumnType("decimal(11, 8)")
                        .HasColumnName("longitude");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("name");

                    b.Property<string>("StateCode")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("state_code");

                    b.Property<int>("StateId")
                        .HasColumnType("int")
                        .HasColumnName("state_id");

                    b.Property<string>("StateName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("state_name");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.Property<string>("WikiDataId")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("wikiDataId");

                    b.HasKey("Id");

                    b.HasIndex("CountryCode");

                    b.HasIndex("CountryId");

                    b.HasIndex("Name");

                    b.HasIndex("StateCode");

                    b.HasIndex("StateId");

                    b.ToTable("WorldCities", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldCountry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Capital")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("capital");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("Currency")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("currency");

                    b.Property<string>("Currency_Name")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("currency_name");

                    b.Property<string>("Currency_Symbol")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("currency_symbol");

                    b.Property<string>("Emoji")
                        .HasMaxLength(191)
                        .HasColumnType("nvarchar(191)")
                        .HasColumnName("emoji");

                    b.Property<string>("EmojiU")
                        .HasMaxLength(191)
                        .HasColumnType("nvarchar(191)")
                        .HasColumnName("emojiU");

                    b.Property<bool>("Flag")
                        .HasColumnType("bit")
                        .HasColumnName("flag");

                    b.Property<string>("Iso2")
                        .HasMaxLength(2)
                        .HasColumnType("nchar(2)")
                        .HasColumnName("iso2")
                        .IsFixedLength();

                    b.Property<string>("Iso3")
                        .HasMaxLength(3)
                        .HasColumnType("nchar(3)")
                        .HasColumnName("iso3")
                        .IsFixedLength();

                    b.Property<decimal>("Latitude")
                        .HasColumnType("decimal(10, 8)")
                        .HasColumnName("latitude");

                    b.Property<decimal?>("Longitude")
                        .HasColumnType("decimal(11, 8)")
                        .HasColumnName("longitude");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("name");

                    b.Property<string>("Nationality")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("nationality");

                    b.Property<string>("Native")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("native");

                    b.Property<string>("Numeric_Code")
                        .HasMaxLength(3)
                        .HasColumnType("nchar(3)")
                        .HasColumnName("numeric_code")
                        .IsFixedLength();

                    b.Property<string>("PhoneCode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("phonecode");

                    b.Property<string>("Region")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("region");

                    b.Property<int?>("Region_Id")
                        .HasColumnType("int")
                        .HasColumnName("region_id");

                    b.Property<string>("Subregion")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("subregion");

                    b.Property<int?>("Subregion_Id")
                        .HasColumnType("int")
                        .HasColumnName("subregion_id");

                    b.Property<string>("Timezones")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("timezones");

                    b.Property<string>("Tld")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("tld");

                    b.Property<string>("Translations")
                        .HasMaxLength(1400)
                        .HasColumnType("nvarchar(1400)")
                        .HasColumnName("translations");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.Property<string>("WikiDataId")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("wikiDataId");

                    b.HasKey("Id");

                    b.HasIndex("Iso2")
                        .IsUnique()
                        .HasFilter("[iso2] IS NOT NULL");

                    b.HasIndex("Iso3")
                        .IsUnique()
                        .HasFilter("[iso3] IS NOT NULL");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.HasIndex("Numeric_Code")
                        .IsUnique()
                        .HasFilter("[numeric_code] IS NOT NULL");

                    b.HasIndex("Region_Id");

                    b.HasIndex("Subregion_Id");

                    b.ToTable("WorldCountries", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldRegion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("Created_At")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("Created_At")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<bool>("Flag")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("Flag");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Name");

                    b.Property<string>("NameVN")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("NameVN");

                    b.Property<string>("Translations")
                        .HasMaxLength(600)
                        .HasColumnType("nvarchar(600)")
                        .HasColumnName("Translations");

                    b.Property<DateTime>("Updated_At")
                        .HasColumnType("datetime2")
                        .HasColumnName("Updated_At");

                    b.Property<string>("WikiDataId")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("WikiDataId");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_WorldRegion_Name");

                    b.ToTable("WorldRegions", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldState", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nchar(2)")
                        .HasColumnName("country_code")
                        .IsFixedLength();

                    b.Property<int>("CountryId")
                        .HasColumnType("int")
                        .HasColumnName("country_id");

                    b.Property<string>("CountryName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("country_name");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at");

                    b.Property<string>("FipsCode")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("fips_code");

                    b.Property<bool>("Flag")
                        .HasColumnType("bit")
                        .HasColumnName("flag");

                    b.Property<string>("Iso2")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("iso2");

                    b.Property<decimal?>("Latitude")
                        .HasColumnType("decimal(10, 8)")
                        .HasColumnName("latitude");

                    b.Property<decimal?>("Longitude")
                        .HasColumnType("decimal(11, 8)")
                        .HasColumnName("longitude");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("name");

                    b.Property<string>("StateCode")
                        .HasMaxLength(10)
                        .HasColumnType("nchar(10)")
                        .HasColumnName("state_code")
                        .IsFixedLength();

                    b.Property<string>("Type")
                        .HasMaxLength(191)
                        .HasColumnType("nvarchar(191)")
                        .HasColumnName("type");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.Property<string>("WikiDataId")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("wikiDataId");

                    b.HasKey("Id");

                    b.HasIndex("CountryCode")
                        .HasDatabaseName("IX_WorldStates_CountryCode");

                    b.HasIndex("CountryId");

                    b.HasIndex("StateCode")
                        .HasDatabaseName("IX_WorldStates_StateCode");

                    b.ToTable("WorldStates", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldSubregion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("Created_At")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<bool>("Flag")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("Flag");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("Name");

                    b.Property<string>("NameVN")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("NameVN");

                    b.Property<int>("Region_Id")
                        .HasColumnType("int");

                    b.Property<string>("Translations")
                        .HasMaxLength(700)
                        .HasColumnType("nvarchar(700)")
                        .HasColumnName("Translations");

                    b.Property<DateTime>("Updated_At")
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at");

                    b.Property<string>("WikiDataId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("WikiDataId");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_WorldSubregion_Name");

                    b.HasIndex("Region_Id");

                    b.ToTable("WorldSubregions", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblAirport", b =>
                {
                    b.Property<int>("Indentity")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Indentity"));

                    b.Property<string>("CityCode")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<string>("CityNameOther")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CityName_En")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CityName_Vi")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<string>("CountryCode")
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<string>("CountryName_En")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CountryName_Vi")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CraetedUser")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name_En")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name_Vi")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Order")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("RegionCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("RegionName_En")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RegionName_Vi")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("TopDestination")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UpdatedUser")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("Visible")
                        .HasColumnType("bit");

                    b.HasKey("Indentity");

                    b.ToTable("tblAirport", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblApiKeyLib", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Domain")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("EmailPass")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("EmailPort")
                        .HasColumnType("int");

                    b.Property<string>("EmailRecive")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<long>("RequestLimit")
                        .HasColumnType("bigint");

                    b.Property<string>("SenderEmail")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("SmtpServer")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime?>("UpdateAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("XApiKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("XApiKey")
                        .IsUnique()
                        .HasDatabaseName("IX_XApiKey");

                    b.ToTable("tblApiKeyLib", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblFareRule", b =>
                {
                    b.Property<string>("ID")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Airlines")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("BagPieces")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("BusinessLounge")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FareType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("HandBaggage")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("HandWeightBag")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Meal")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ModifyName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NoShow")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NoShowHoliday")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Note")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("PriorityCheckIn")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RebookLeast03h")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RebookWithin03Holiday")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RebookWithin03h")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RefundLeast03h")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RefundNameRetention")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RefundWithin03Holiday")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("RefundWithin03h")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("SeatSelection")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("WeightBag")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("ID");

                    b.ToTable("tblFareRules", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblFeature", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FeatureName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsUnlocked")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Note")
                        .HasMaxLength(2560)
                        .HasColumnType("nvarchar(2560)");

                    b.Property<Guid?>("OwnerID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UnlockDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<Guid>("UserCreate")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserUpdate")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("tblFeatures", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblFlightAirport", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Continent")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("ElevationFt")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("GpsCode")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<string>("IataCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<string>("IcaoCode")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<string>("Ident")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)");

                    b.Property<string>("Iso_Region")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Iso_country")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("Keywords")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("LatitudeDeg")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("LongitudeDeg")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("Municipality")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("MunicipalityVN")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<string>("NameVN")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)");

                    b.Property<int>("Order")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<bool>("ScheduledService")
                        .HasColumnType("bit");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(14)
                        .HasColumnType("nvarchar(14)");

                    b.HasKey("ID");

                    b.ToTable("tblFlightAirports", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblFlightContinent", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<DateTime>("DateCreate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("NameVN")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<int>("Order")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.HasKey("Code");

                    b.ToTable("tblFlightContinents", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblFlightCountry", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("Contrinent")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("Keywords")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NameVN")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("ID");

                    b.ToTable("tblFlightCountries", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblFlightPlane", b =>
                {
                    b.Property<string>("Code")
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(70)
                        .HasColumnType("nvarchar(70)");

                    b.Property<string>("ShortName")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("nvarchar(4)");

                    b.HasKey("Code");

                    b.ToTable("tblFlightPlanes", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblFlightRegion", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ID"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Continent")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("ISOCountry")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)");

                    b.Property<string>("KeyWords")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LocalCode")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("NameVN")
                        .IsRequired()
                        .HasMaxLength(70)
                        .HasColumnType("nvarchar(70)");

                    b.HasKey("ID");

                    b.ToTable("tblFlightRegions", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblGDS", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("GdsType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("tblGDSs");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblLibHis", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ApiKeyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ApiName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Domain")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("datetime2");

                    b.Property<int>("Month")
                        .HasColumnType("int");

                    b.Property<long>("RequestCount")
                        .HasColumnType("bigint");

                    b.Property<int>("Year")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ApiKeyId", "Year", "Month")
                        .HasDatabaseName("IX_ApiKeyId_Year_Month");

                    b.HasIndex("ApiKeyId", "Month", "Year", "ApiName")
                        .IsUnique()
                        .HasDatabaseName("IX_ApiKeyId_Month_Year_ApiName")
                        .HasFilter("[ApiName] IS NOT NULL");

                    b.HasIndex("ApiKeyId", "Year", "Month", "Domain")
                        .HasDatabaseName("IX_ApiKeyId_Year_Month_Domain");

                    b.ToTable("tblLibHis", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblMetaTicketIssueFee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasDefaultValue("VND");

                    b.Property<decimal>("FeeAmountAdult")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("FeeAmountChild")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("FeeAmountInfant")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("FeeType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Notes")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("OwnerID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("SubContinentCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.ToTable("tblMetaTicketIssueFee", (string)null);
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblUserActivityLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("tblUserActivityLogs");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblUserGDS", b =>
                {
                    b.Property<Guid>("XapiID")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("GDSId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("CreatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedBy")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("XapiID", "GDSId");

                    b.HasIndex("GDSId");

                    b.ToTable("tblUserGDSs");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppUserToken", b =>
                {
                    b.HasBaseType("Microsoft.AspNetCore.Identity.IdentityUserToken<System.Guid>");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime");

                    b.Property<DateTime>("Expires")
                        .HasColumnType("datetime");

                    b.HasDiscriminator().HasValue("AppUserToken");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Address", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserApp")
                        .WithMany("Addresses")
                        .HasForeignKey("UserAppId")
                        .HasConstraintName("FK_Address_AppUsers");

                    b.Navigation("UserApp");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AddressTranslation", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Address", "Address")
                        .WithMany("AddressTranslations")
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_AddressTranslation_Address");

                    b.HasOne("NgocMaiTravel.Data.Entities.Language", "Language")
                        .WithMany("AddressTranslations")
                        .HasForeignKey("LanguageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_AddressTranslation_Language");

                    b.Navigation("Address");

                    b.Navigation("Language");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Airport", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.CategoryAirport", "TypeAirport")
                        .WithMany("Airports")
                        .HasForeignKey("AirportTypeID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TypeAirport");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppGroupUser", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Creator")
                        .WithMany("CreateGroupUser")
                        .HasForeignKey("CreateBy");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Updator")
                        .WithMany("UpdateGroupUser")
                        .HasForeignKey("UpdateBy");

                    b.Navigation("Creator");

                    b.Navigation("Updator");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppLockUserHistory", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Creator")
                        .WithMany("UserHistoryLockCreators")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_AppLockUserHistory_Creator");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserLock")
                        .WithMany("UserHistoryLocks")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_AppLockUserHistory_UserLock");

                    b.Navigation("Creator");

                    b.Navigation("UserLock");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppRole", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserCreate")
                        .WithMany("CreateAppRoles")
                        .HasForeignKey("UserCreateID")
                        .HasConstraintName("FK_AppRoles_AppUsers_UserCreateID");

                    b.Navigation("UserCreate");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppUser", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppGroupUser", "GroupUser")
                        .WithMany("Users")
                        .HasForeignKey("GroupUserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("GroupUser");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryHotel", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "AppUser")
                        .WithMany("CategoryHotels")
                        .HasForeignKey("AppUserId");

                    b.Navigation("AppUser");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryNews", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "AppUser")
                        .WithMany("CategoryNews")
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK_category_news_app_users");

                    b.Navigation("AppUser");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryTour", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "AppUser")
                        .WithMany("CategoryTours")
                        .HasForeignKey("AppUserId")
                        .HasConstraintName("FK_CategoryTours_AppUsers");

                    b.HasOne("NgocMaiTravel.Data.Entities.CategoryTour", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("FK_CategoryTours_CategoryTours");

                    b.Navigation("AppUser");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryVisa", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Creator")
                        .WithMany("CategoryVisaCreated")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Updater")
                        .WithMany("CategoryVisaUpdated")
                        .HasForeignKey("UpdatedBy");

                    b.Navigation("Creator");

                    b.Navigation("Updater");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryVisa_Visa", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.CategoryVisa", "CategoryVisa")
                        .WithMany("CategoryVisa_Visas")
                        .HasForeignKey("CategoryVisaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("NgocMaiTravel.Data.Entities.Visa", "Visa")
                        .WithMany("CategoryVisa_Visas")
                        .HasForeignKey("VisaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CategoryVisa");

                    b.Navigation("Visa");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Contact", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "AppUser")
                        .WithMany("Contacts")
                        .HasForeignKey("UserCheck")
                        .HasConstraintName("FK_Contact_AppUsers");

                    b.Navigation("AppUser");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Hotel", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.CategoryHotel", "CategoryHotel")
                        .WithMany("Hotels")
                        .HasForeignKey("CategoryHotelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Hotels_CategoryHotels");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserApp")
                        .WithMany("Hotels")
                        .HasForeignKey("UserAppId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("CategoryHotel");

                    b.Navigation("UserApp");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.HotelBooking", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Room", "Room")
                        .WithMany("HotelBookings")
                        .HasForeignKey("RoomId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserApp")
                        .WithMany("HotelBookings")
                        .HasForeignKey("UserAppId");

                    b.Navigation("Room");

                    b.Navigation("UserApp");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.HotelImage", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Hotel", "Hotel")
                        .WithMany("Images")
                        .HasForeignKey("HotelID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Hotel");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.HotelRequest", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserUpadte")
                        .WithMany("HotelRequests")
                        .HasForeignKey("UserUpdateAppId");

                    b.Navigation("UserUpadte");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.News", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.CategoryNews", "Category")
                        .WithMany("News")
                        .HasForeignKey("CategoryId")
                        .HasConstraintName("FK_news_category_news");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "AppUser")
                        .WithMany("News")
                        .HasForeignKey("UserId")
                        .HasConstraintName("FK_news_app_users");

                    b.Navigation("AppUser");

                    b.Navigation("Category");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Order", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Tour", "Tour")
                        .WithMany("Orders")
                        .HasForeignKey("TourId")
                        .HasConstraintName("FK_Order_Tours");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserApp")
                        .WithMany("Orders")
                        .HasForeignKey("UserAppId")
                        .HasConstraintName("FK_Order_AppUsers");

                    b.Navigation("Tour");

                    b.Navigation("UserApp");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.OrderDetail", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Order", "Order")
                        .WithMany("OrderDetails")
                        .HasForeignKey("OrderId")
                        .HasConstraintName("FK_OrderDetails_Order");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserApp")
                        .WithMany("OrderDetails")
                        .HasForeignKey("UserAppId")
                        .HasConstraintName("FK_OrderDetails_AppUsers");

                    b.Navigation("Order");

                    b.Navigation("UserApp");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Promotion", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserApp")
                        .WithMany("Promotions")
                        .HasForeignKey("UserAppId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("UserApp");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Room", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Hotel", "Hotel")
                        .WithMany("Rooms")
                        .HasForeignKey("HotelID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserApp")
                        .WithMany("Rooms")
                        .HasForeignKey("UserAppId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Hotel");

                    b.Navigation("UserApp");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Screen", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Creator")
                        .WithMany("ScreenCreated")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("NgocMaiTravel.Data.Entities.Screen", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Updater")
                        .WithMany("ScreenUpdated")
                        .HasForeignKey("UpdatedBy")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Creator");

                    b.Navigation("Parent");

                    b.Navigation("Updater");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.ScreenRole", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppRole", "Role")
                        .WithMany("ScreenRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ScreenRole_Role");

                    b.HasOne("NgocMaiTravel.Data.Entities.Screen", "Screen")
                        .WithMany("ScreenRoles")
                        .HasForeignKey("ScreenId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ScreenRole_Screen");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Creator")
                        .WithMany("CreatedScreenRoles")
                        .HasForeignKey("UserCreate")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_ScreenRole_Creator");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Updater")
                        .WithMany("UpdatedScreenRoles")
                        .HasForeignKey("UserUpdate")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_ScreenRole_Updater");

                    b.Navigation("Creator");

                    b.Navigation("Role");

                    b.Navigation("Screen");

                    b.Navigation("Updater");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.ScreenUser", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Screen", "Screen")
                        .WithMany("ScreenUsers")
                        .HasForeignKey("ScreenId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ScreenUser_Screen");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Creator")
                        .WithMany("CreatedScreenUsers")
                        .HasForeignKey("UserCreate")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("FK_ScreenUser_Creator");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "User")
                        .WithMany("ScreenUsers")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_ScreenUser_User");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Updater")
                        .WithMany("UpdatedScreenUsers")
                        .HasForeignKey("UserUpdate")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("FK_ScreenUser_Updater");

                    b.Navigation("Creator");

                    b.Navigation("Screen");

                    b.Navigation("Updater");

                    b.Navigation("User");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Tour", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.CategoryTour", "CategoryTour")
                        .WithMany("Tours")
                        .HasForeignKey("CategoryTourId")
                        .HasConstraintName("FK_Tours_CategoryTours");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserApp")
                        .WithMany("Tours")
                        .HasForeignKey("UserAppId")
                        .HasConstraintName("FK_Tours_AppUsers");

                    b.Navigation("CategoryTour");

                    b.Navigation("UserApp");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourAddress", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Address", "Address")
                        .WithMany("TourAddresses")
                        .HasForeignKey("AddressId")
                        .IsRequired()
                        .HasConstraintName("FK_TourAddress_Address");

                    b.HasOne("NgocMaiTravel.Data.Entities.Tour", "Tour")
                        .WithMany("TourAddresses")
                        .HasForeignKey("TourId")
                        .IsRequired()
                        .HasConstraintName("FK_TourAddress_Tours");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserApp")
                        .WithMany("TourAddresses")
                        .HasForeignKey("UserAppId")
                        .HasConstraintName("FK_TourAddress_AppUsers");

                    b.Navigation("Address");

                    b.Navigation("Tour");

                    b.Navigation("UserApp");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourFeedback", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Tour", "Tour")
                        .WithMany("TourFeedbacks")
                        .HasForeignKey("TourId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tour");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourFeedbackImage", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.TourFeedback", "TourFeedback")
                        .WithMany("TourFeedbackImages")
                        .HasForeignKey("TourFeedbackId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TourFeedback");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourImage", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Tour", "Tour")
                        .WithMany("TourImages")
                        .HasForeignKey("TourId")
                        .IsRequired()
                        .HasConstraintName("FK_TourImage_Tours");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserApp")
                        .WithMany("TourImages")
                        .HasForeignKey("UserAppId")
                        .HasConstraintName("FK_TourImage_AppUsers");

                    b.Navigation("Tour");

                    b.Navigation("UserApp");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourPrice", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Tour", "Tour")
                        .WithMany("TourPrices")
                        .HasForeignKey("TourId")
                        .HasConstraintName("FK_TourPrice_Tours");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserApp")
                        .WithMany("TourPrices")
                        .HasForeignKey("UserAppId")
                        .HasConstraintName("FK_TourPrice_AppUsers");

                    b.Navigation("Tour");

                    b.Navigation("UserApp");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourTour", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Tour", "Tour")
                        .WithMany("TourTours")
                        .HasForeignKey("TourId")
                        .IsRequired()
                        .HasConstraintName("FK_TourTours_Tours");

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserApp")
                        .WithMany("TourTours")
                        .HasForeignKey("UserAppId");

                    b.Navigation("Tour");

                    b.Navigation("UserApp");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Translate", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Creator")
                        .WithMany("TranslateCreated")
                        .HasForeignKey("CreateBy")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("NgocMaiTravel.Data.Entities.Screen", "Screen")
                        .WithMany("Translates")
                        .HasForeignKey("ScreenID")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Updater")
                        .WithMany("TranslateUpdated")
                        .HasForeignKey("UpdateBy")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Creator");

                    b.Navigation("Screen");

                    b.Navigation("Updater");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Visa", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Creator")
                        .WithMany("VisaCreated")
                        .HasForeignKey("CreatedBy")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "Updater")
                        .WithMany("VisaUpdated")
                        .HasForeignKey("UpdatedBy");

                    b.Navigation("Creator");

                    b.Navigation("Updater");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.VisaRequest", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "UserReply")
                        .WithMany("VisaRequests")
                        .HasForeignKey("UserReplyId");

                    b.Navigation("UserReply");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.VisaTranslation", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.Visa", "Visa")
                        .WithMany("VisaTranslations")
                        .HasForeignKey("VisaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Visa");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldCity", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.WorldCountry", "WorldCountry")
                        .WithMany("WorldCities")
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("NgocMaiTravel.Data.Entities.WorldState", "WorldState")
                        .WithMany("WorldCities")
                        .HasForeignKey("StateId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("WorldCountry");

                    b.Navigation("WorldState");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldCountry", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.WorldRegion", "WorldRegion")
                        .WithMany("WorldCountries")
                        .HasForeignKey("Region_Id")
                        .HasConstraintName("FK_countries_regions");

                    b.HasOne("NgocMaiTravel.Data.Entities.WorldSubregion", "WorldSubregion")
                        .WithMany("WorldCountries")
                        .HasForeignKey("Subregion_Id")
                        .HasConstraintName("FK_countries_subregions");

                    b.Navigation("WorldRegion");

                    b.Navigation("WorldSubregion");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldState", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.WorldCountry", "WorldCountry")
                        .WithMany("WorldStates")
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_WorldStates_WorldCountries");

                    b.Navigation("WorldCountry");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldSubregion", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.WorldRegion", "WorldRegion")
                        .WithMany("WorldSubregions")
                        .HasForeignKey("Region_Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WorldRegion");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblLibHis", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.tblApiKeyLib", "ApiKey")
                        .WithMany()
                        .HasForeignKey("ApiKeyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApiKey");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblUserActivityLog", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.AppUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblUserGDS", b =>
                {
                    b.HasOne("NgocMaiTravel.Data.Entities.tblGDS", "GDS")
                        .WithMany("UserGDSs")
                        .HasForeignKey("GDSId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("NgocMaiTravel.Data.Entities.tblApiKeyLib", "ApiKey")
                        .WithMany()
                        .HasForeignKey("XapiID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApiKey");

                    b.Navigation("GDS");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Address", b =>
                {
                    b.Navigation("AddressTranslations");

                    b.Navigation("TourAddresses");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppGroupUser", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppRole", b =>
                {
                    b.Navigation("ScreenRoles");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.AppUser", b =>
                {
                    b.Navigation("Addresses");

                    b.Navigation("CategoryHotels");

                    b.Navigation("CategoryNews");

                    b.Navigation("CategoryTours");

                    b.Navigation("CategoryVisaCreated");

                    b.Navigation("CategoryVisaUpdated");

                    b.Navigation("Contacts");

                    b.Navigation("CreateAppRoles");

                    b.Navigation("CreateGroupUser");

                    b.Navigation("CreatedScreenRoles");

                    b.Navigation("CreatedScreenUsers");

                    b.Navigation("HotelBookings");

                    b.Navigation("HotelRequests");

                    b.Navigation("Hotels");

                    b.Navigation("News");

                    b.Navigation("OrderDetails");

                    b.Navigation("Orders");

                    b.Navigation("Promotions");

                    b.Navigation("Rooms");

                    b.Navigation("ScreenCreated");

                    b.Navigation("ScreenUpdated");

                    b.Navigation("ScreenUsers");

                    b.Navigation("TourAddresses");

                    b.Navigation("TourImages");

                    b.Navigation("TourPrices");

                    b.Navigation("TourTours");

                    b.Navigation("Tours");

                    b.Navigation("TranslateCreated");

                    b.Navigation("TranslateUpdated");

                    b.Navigation("UpdateGroupUser");

                    b.Navigation("UpdatedScreenRoles");

                    b.Navigation("UpdatedScreenUsers");

                    b.Navigation("UserHistoryLockCreators");

                    b.Navigation("UserHistoryLocks");

                    b.Navigation("VisaCreated");

                    b.Navigation("VisaRequests");

                    b.Navigation("VisaUpdated");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryAirport", b =>
                {
                    b.Navigation("Airports");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryHotel", b =>
                {
                    b.Navigation("Hotels");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryNews", b =>
                {
                    b.Navigation("News");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryTour", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("Tours");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.CategoryVisa", b =>
                {
                    b.Navigation("CategoryVisa_Visas");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Hotel", b =>
                {
                    b.Navigation("Images");

                    b.Navigation("Rooms");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Language", b =>
                {
                    b.Navigation("AddressTranslations");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Order", b =>
                {
                    b.Navigation("OrderDetails");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Room", b =>
                {
                    b.Navigation("HotelBookings");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Screen", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("ScreenRoles");

                    b.Navigation("ScreenUsers");

                    b.Navigation("Translates");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Tour", b =>
                {
                    b.Navigation("Orders");

                    b.Navigation("TourAddresses");

                    b.Navigation("TourFeedbacks");

                    b.Navigation("TourImages");

                    b.Navigation("TourPrices");

                    b.Navigation("TourTours");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.TourFeedback", b =>
                {
                    b.Navigation("TourFeedbackImages");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.Visa", b =>
                {
                    b.Navigation("CategoryVisa_Visas");

                    b.Navigation("VisaTranslations");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldCountry", b =>
                {
                    b.Navigation("WorldCities");

                    b.Navigation("WorldStates");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldRegion", b =>
                {
                    b.Navigation("WorldCountries");

                    b.Navigation("WorldSubregions");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldState", b =>
                {
                    b.Navigation("WorldCities");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.WorldSubregion", b =>
                {
                    b.Navigation("WorldCountries");
                });

            modelBuilder.Entity("NgocMaiTravel.Data.Entities.tblGDS", b =>
                {
                    b.Navigation("UserGDSs");
                });
#pragma warning restore 612, 618
        }
    }
}
