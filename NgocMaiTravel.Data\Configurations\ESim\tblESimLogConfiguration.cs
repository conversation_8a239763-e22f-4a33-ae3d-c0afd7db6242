using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NgocMaiTravel.Data.Entities.ESim;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Data.Configurations.ESim
{
    public class tblESimLogConfiguration : IEntityTypeConfiguration<tblESimLog>
    {
        public void Configure(EntityTypeBuilder<tblESimLog> builder)
        {
            builder.ToTable("tblESimLogs");
            
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).HasDefaultValueSql("NEWID()");
            
            // Log Information
            builder.Property(x => x.OrderId).HasMaxLength(100).IsRequired(false);
            builder.Property(x => x.PlanId).HasMaxLength(100).IsRequired(false);
            builder.Property(x => x.Action).IsRequired().HasMaxLength(100);
            builder.Property(x => x.CustomerEmail).HasMaxLength(200).IsRequired(false);
            builder.Property(x => x.CustomerPhone).HasMaxLength(20).IsRequired(false);
            
            // Request/Response Data
            builder.Property(x => x.RequestData).HasColumnType("ntext").IsRequired();
            builder.Property(x => x.ResponseData).HasColumnType("ntext").IsRequired(false);
            builder.Property(x => x.Status).IsRequired().HasMaxLength(50);
            builder.Property(x => x.ErrorMessage).HasColumnType("ntext").IsRequired(false);
            
            // Timestamp and Performance
            builder.Property(x => x.Timestamp).IsRequired().HasDefaultValueSql("GETDATE()");
            builder.Property(x => x.Duration).IsRequired(false);
            
            // Request Context
            builder.Property(x => x.IpAddress).HasMaxLength(50).IsRequired(false);
            builder.Property(x => x.UserAgent).HasMaxLength(500).IsRequired(false);
            builder.Property(x => x.ApiEndpoint).HasMaxLength(200).IsRequired(false);
            builder.Property(x => x.HttpStatusCode).IsRequired(false);
            builder.Property(x => x.OwnerID).IsRequired(false);
            builder.Property(x => x.SessionId).HasMaxLength(100).IsRequired(false);
            builder.Property(x => x.TraceId).HasMaxLength(100).IsRequired(false);
            
            // Indexes for performance
            builder.HasIndex(x => x.OrderId);
            builder.HasIndex(x => x.Action);
            builder.HasIndex(x => x.Status);
            builder.HasIndex(x => x.Timestamp);
            builder.HasIndex(x => x.CustomerEmail);
            builder.HasIndex(x => x.CustomerPhone);
            builder.HasIndex(x => x.OwnerID);
            builder.HasIndex(x => x.TraceId);
            
            // Composite indexes for common queries
            builder.HasIndex(x => new { x.Action, x.Status });
            builder.HasIndex(x => new { x.Timestamp, x.Action });
            builder.HasIndex(x => new { x.OrderId, x.Action });
            builder.HasIndex(x => new { x.CustomerEmail, x.Action });
        }
    }
}
