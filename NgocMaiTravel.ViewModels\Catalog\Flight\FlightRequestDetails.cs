﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.ViewModels.Catalog.Flight
{
    public class FlightRequestDetails
    {
        public string Id { get; set; }
        public string Depart { get; set; }
        public string Arrival { get; set; }
        public DateTime DepartDate { get; set; }
        public DateTime? ReturnDate { get; set; }
        public int Adult { get; set; }
        public int Child { get; set; }
        public int Infant { get; set; }
        public string CustomerName { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public string Note { get; set; }
        public int Status { get; set; }
        public DateTime TimeCreate { get; set; }
        public DateTime? TimeCompletion { get; set; }
        public string? PaymentMethod { get; set; }
        public string? Pnrs { get; set; }
        public string? Airlines { get; set; }
        public string? NoteUser { get; set; }
    }
}
