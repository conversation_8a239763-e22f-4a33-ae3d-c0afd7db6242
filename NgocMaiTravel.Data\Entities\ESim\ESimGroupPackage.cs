using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NgocMaiTravel.Data.Entities.ESim
{
    /// <summary>
    /// ESim Group-Package mapping entity (Many-to-Many)
    /// </summary>
    [Table("tblESimGroupPackage")]
    public class ESimGroupPackage
    {
        /// <summary>
        /// Primary key
        /// </summary>
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// Reference to ESimGroup
        /// </summary>
        [Required]
        public long GroupId { get; set; }

        /// <summary>
        /// Reference to ESimPackage
        /// </summary>
        [Required]
        public long PackageId { get; set; }

        /// <summary>
        /// Display order within group
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Whether the mapping is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created the record
        /// </summary>
        [StringLength(255)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// User who last updated the record
        /// </summary>
        [StringLength(255)]
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// Navigation property to Group
        /// </summary>
        [ForeignKey("GroupId")]
        public virtual ESimGroup Group { get; set; } = null!;

        /// <summary>
        /// Navigation property to Package
        /// </summary>
        [ForeignKey("PackageId")]
        public virtual ESimPackage Package { get; set; } = null!;
    }
}
