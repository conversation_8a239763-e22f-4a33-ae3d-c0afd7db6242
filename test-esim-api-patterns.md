# ESim API Patterns Test Guide

## Overview

ESimBlue API has two different response patterns:

### **Pattern 1: Always HTTP 200 (Object vs Array)**
- **APIs**: `redeem`, `usage`, `profile(?)`
- **Success**: JSON Object `{ ... }`
- **Error**: JSON Array `[{ "name": "...", "code": "...", "message": "..." }]`

### **Pattern 2: Standard HTTP Status**
- **APIs**: `balance`, `packages`, `orders`
- **Success**: HTTP 200 + JSON Object
- **Error**: HTTP 4xx/5xx + Error message

## Fixed APIs

### **1. Redeem API** ✅
- **Endpoint**: `POST /eip/partner/esim/{serial}/redeem`
- **Success**: `{ "orderPublicId": "...", "serial": "...", "esim": {...} }`
- **Error**: `[{ "name": "SERIAL", "code": "RESOURCE_NOT_FOUND", "message": "Serial not found" }]`

### **2. Usage API** ✅
- **Endpoint**: `GET /eip/partner/esim/{serial}/usage`
- **Success**: `{ "orderPublicId": "...", "serial": "...", "remaining": 0, "volume": 0, "expired_at": "...", "status": "FINISHED" }`
- **Error**: `[{ "name": "SERIAL", "code": "RESOURCE_NOT_FOUND", "message": "Serial not found" }]`

## Test Commands

### **Test Response Detection Logic**
```bash
# Test success response detection
curl "https://localhost:7001/api/TestRedis/test-response-detection?responseType=success"

# Test error response detection
curl "https://localhost:7001/api/TestRedis/test-response-detection?responseType=error"
```

### **Test API Format Examples**
```bash
# Test redeem API format
curl -X POST "https://localhost:7001/api/TestRedis/test-esim-redeem?serial=GX28D0X493"

# Test usage API format
curl -X POST "https://localhost:7001/api/TestRedis/test-esim-usage?serial=TL838TJMXS"

# Test order API format
curl -X POST "https://localhost:7001/api/TestRedis/test-esim-order?sku=BLC-01-JP-moshi-moshi-7days-1gb&quantity=1"

# Test all patterns summary
curl "https://localhost:7001/api/TestRedis/test-esim-patterns"
```

### **Test Actual APIs**
```bash
# Test actual redeem (requires valid serial)
curl -X POST "https://localhost:7001/api/ESim/redeem/VALID_SERIAL"

# Test actual usage (requires valid serial)
curl -X GET "https://localhost:7001/api/ESim/usage/VALID_SERIAL"

# Test order creation
curl -X POST "https://localhost:7001/api/ESim/orders" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "BLC-01-JP-moshi-moshi-7days-1gb",
    "customerEmail": "<EMAIL>",
    "customerPhone": "+84123456789"
  }'
```

## Expected Results

### **Success Response Detection:**
```json
{
  "success": true,
  "detectedFormat": "success_object",
  "detectionLogic": {
    "startsWithBracket": false,
    "startsWithBrace": true,
    "firstChar": "{"
  }
}
```

### **Error Response Detection:**
```json
{
  "success": true,
  "detectedFormat": "error_array",
  "detectionLogic": {
    "startsWithBracket": true,
    "startsWithBrace": false,
    "firstChar": "["
  }
}
```

### **Redeem Success:**
```json
{
  "isSuccessed": true,
  "resultObj": {
    "orderPublicId": "bb559e4f-6b70-498f-939a-9da767049ca6",
    "serial": "GX28D0X493",
    "esim": {
      "iccid": "894000000000058815",
      "ac": "LPA:1$lpa.airalo.com$TEST",
      "qrCodeUrl": "https://sandbox.airalo.com/qr?expires=...",
      "shortUrl": "https://esimsetup.apple.com/esim_qrcode_provisioning?carddata=..."
    }
  }
}
```

### **Usage Success:**
```json
{
  "isSuccessed": true,
  "resultObj": {
    "orderPublicId": "f1b15787-2082-456b-9d80-80a14e2aa3c2",
    "serial": "TL838TJMXS",
    "remaining": 0,
    "volume": 1073741824,
    "expiredAt": "2025-03-13 04:01:02",
    "status": "FINISHED"
  }
}
```

### **Error Response:**
```json
{
  "isSuccessed": false,
  "message": "Get usage failed: RESOURCE_NOT_FOUND: Serial not found"
}
```

## Verification Checklist

### **✅ Response Detection Working:**
- [ ] Success responses (starting with `{`) detected as `success_object`
- [ ] Error responses (starting with `[`) detected as `error_array`
- [ ] First character detection is accurate

### **✅ Redeem API Working:**
- [ ] Valid serials return complete ESim data with QR codes
- [ ] Invalid serials return proper error messages
- [ ] No dependency on HTTP status codes

### **✅ Usage API Working:**
- [ ] Valid serials return usage statistics
- [ ] Invalid serials return proper error messages
- [ ] Status values are meaningful (FINISHED, ACTIVE, etc.)

### **✅ Error Handling:**
- [ ] JSON parsing errors are caught and handled
- [ ] Network errors are properly propagated
- [ ] Unexpected formats return meaningful errors

## Common Issues & Solutions

### **Issue 1: Still Getting Parse Errors**
**Cause**: Response format not detected correctly
**Solution**: Check trimming and character detection logic

### **Issue 2: Success Treated as Error**
**Cause**: Response starts with whitespace
**Solution**: Ensure `Trim()` is called before character check

### **Issue 3: Error Details Missing**
**Cause**: Error array not parsed correctly
**Solution**: Verify EsimErrorResponse model matches API format

### **Issue 4: HTTP Status Dependency**
**Cause**: Still checking `IsSuccessStatusCode`
**Solution**: Remember API always returns 200, check content format instead

## API Status Summary

| API | Endpoint | Pattern | Status |
|-----|----------|---------|--------|
| Balance | `/eip/partner/company/balance` | Standard HTTP | ❓ Unknown |
| Packages | `/eip/partner/esim/packages` | Standard HTTP | ❓ Unknown |
| Orders | `/eip/partner/esim/orders` | Standard HTTP | ❓ Unknown |
| Profile | `/eip/partner/esim/{serial}/query` | Always 200? | ❓ Unknown |
| **Redeem** | `/eip/partner/esim/{serial}/redeem` | **Always 200** | ✅ **Fixed** |
| **Usage** | `/eip/partner/esim/{serial}/usage` | **Always 200** | ✅ **Fixed** |

## Next Steps

1. **Test Profile API**: Check if it follows the same pattern as redeem/usage
2. **Verify Standard APIs**: Confirm balance, packages, orders use normal HTTP status
3. **Monitor Logs**: Watch for any unexpected response formats
4. **Update Documentation**: Keep API patterns documented

## Monitoring

### **Log What to Watch**
- Response content format (starts with `[` or `{`)
- Parsing success/failure for both formats
- Error codes and messages from failed requests
- Complete data from successful requests

### **Metrics to Track**
- Success rate of each API operation
- Most common error codes
- Response parsing success rate
- API response times

This comprehensive test ensures all ESim APIs handle the ESimBlue response patterns correctly.
