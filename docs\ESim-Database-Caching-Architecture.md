# ESim Database Caching Architecture

## Overview

Implemented a comprehensive caching mechanism for ESim packages that combines database storage with intelligent API synchronization. The system provides fast responses while ensuring data freshness through background sync operations.

## Database Schema

### **Core Tables**

#### **1. tblESimGroup (Countries/Regions)**
```sql
CREATE TABLE tblESimGroup (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    GroupCode NVARCHAR(10) NOT NULL, -- 'VN', 'JP', 'US', 'ASIA', 'GLOBAL'
    GroupName NVARCHAR(255) NOT NULL, -- 'Vietnam', 'Japan', 'Asia Region'
    GroupType NVARCHAR(50) NOT NULL DEFAULT 'COUNTRY', -- 'COUNTRY', 'REGION', 'GLOBAL'
    Description NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    DisplayOrder INT NOT NULL DEFAULT 0,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);
```

#### **2. tblESimPackage (Individual Packages)**
```sql
CREATE TABLE tblESimPackage (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    PackageId NVARCHAR(50) NOT NULL, -- External package ID from API
    Sku NVARCHAR(100) NOT NULL, -- Package SKU
    PackageName NVARCHAR(255) NOT NULL,
    Description NVARCHAR(2000) NULL,
    DataAmount BIGINT NOT NULL, -- Data in bytes
    ValidityDays INT NOT NULL, -- Validity period in days
    Price DECIMAL(18,2) NOT NULL,
    Currency NVARCHAR(10) NOT NULL DEFAULT 'USD',
    OriginalPrice DECIMAL(18,2) NULL,
    DiscountPercent DECIMAL(5,2) NULL,
    PackageType NVARCHAR(50) NOT NULL DEFAULT 'DATA',
    NetworkType NVARCHAR(50) NULL, -- '4G', '5G', '3G'
    IsUnlimited BIT NOT NULL DEFAULT 0,
    IsTopUpSupported BIT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    ApiSource NVARCHAR(50) NOT NULL DEFAULT 'ESimBlue',
    ExternalData NVARCHAR(MAX) NULL, -- JSON data from external API
    LastSyncDate DATETIME2 NULL
);
```

#### **3. tblESimGroupPackage (Many-to-Many Mapping)**
```sql
CREATE TABLE tblESimGroupPackage (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    GroupId BIGINT NOT NULL,
    PackageId BIGINT NOT NULL,
    DisplayOrder INT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    
    CONSTRAINT FK_tblESimGroupPackage_GroupId FOREIGN KEY (GroupId) REFERENCES tblESimGroup(Id),
    CONSTRAINT FK_tblESimGroupPackage_PackageId FOREIGN KEY (PackageId) REFERENCES tblESimPackage(Id),
    CONSTRAINT UK_tblESimGroupPackage_GroupPackage UNIQUE (GroupId, PackageId)
);
```

#### **4. tblESimSyncLog (Sync Tracking)**
```sql
CREATE TABLE tblESimSyncLog (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    SyncType NVARCHAR(50) NOT NULL, -- 'FULL_SYNC', 'INCREMENTAL_SYNC', 'GROUP_SYNC'
    Status NVARCHAR(50) NOT NULL, -- 'PENDING', 'RUNNING', 'SUCCESS', 'FAILED'
    StartTime DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    EndTime DATETIME2 NULL,
    Duration INT NULL, -- Duration in milliseconds
    TotalPackages INT NULL,
    ProcessedPackages INT NULL,
    NewPackages INT NULL,
    UpdatedPackages INT NULL,
    ErrorMessage NVARCHAR(MAX) NULL,
    RequestedBy NVARCHAR(255) NULL
);
```

## Caching Strategy

### **Request Flow**

#### **First Request (No Data in DB):**
```
User Request → Check DB → No Data → Fetch from API → Return to User + Queue Sync to DB
```

#### **Subsequent Requests (Data in DB):**
```
User Request → Check DB → Has Data → Return from DB + Queue Background Sync
```

### **Background Sync Process:**
```
Background Service → Process Queue → Fetch from API → Update DB → Log Results
```

## Implementation Details

### **1. SearchPlansAsync Method (Updated)**
```csharp
public async Task<ApiResult<List<CountryEsimGroup>>> SearchPlansAsync(EsimPackagesRq rq)
{
    // Check if we have data in database
    var hasData = await _eSimRepository.HasDataAsync();
    
    if (!hasData)
    {
        // No data in DB - fetch from API and queue sync to DB
        var apiResult = await FetchFromApiAndQueueSync(rq);
        return apiResult;
    }
    else
    {
        // Data exists in DB - return from DB and queue background sync
        var dbResult = await GetFromDatabase(rq);
        await QueueBackgroundSync(rq);
        return dbResult;
    }
}
```

### **2. Background Sync Service**
```csharp
public class ESimSyncBackgroundService : BackgroundService
{
    private readonly ConcurrentQueue<ESimSyncQueueItem> _syncQueue;
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await ProcessQueueAsync(stoppingToken);
            await PeriodicSyncAsync(stoppingToken);
            await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
        }
    }
}
```

### **3. Entity Relationships**
```csharp
// One Group can have many Packages
public class ESimGroup
{
    public virtual ICollection<ESimGroupPackage> GroupPackages { get; set; }
    public virtual IEnumerable<ESimPackage> Packages => GroupPackages.Select(gp => gp.Package);
}

// One Package can belong to many Groups
public class ESimPackage
{
    public virtual ICollection<ESimGroupPackage> GroupPackages { get; set; }
    public virtual IEnumerable<ESimGroup> Groups => GroupPackages.Select(gp => gp.Group);
}
```

## Configuration

### **Sync Configuration**
```csharp
public class ESimSyncConfiguration
{
    public TimeSpan CacheExpiry { get; set; } = TimeSpan.FromHours(6);
    public TimeSpan BackgroundSyncInterval { get; set; } = TimeSpan.FromHours(1);
    public int MaxRetryAttempts { get; set; } = 3;
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromMinutes(5);
    public int BatchSize { get; set; } = 100;
    public bool EnableBackgroundSync { get; set; } = true;
    public bool EnableQueueProcessing { get; set; } = true;
}
```

## Benefits

### **Performance Benefits:**
1. **Fast Response Times**: Database queries are much faster than API calls
2. **Reduced API Load**: Fewer calls to external ESimBlue API
3. **Better User Experience**: Consistent response times
4. **Scalability**: Can handle more concurrent users

### **Reliability Benefits:**
1. **Fault Tolerance**: System works even if API is down
2. **Data Consistency**: Background sync ensures data freshness
3. **Retry Logic**: Failed syncs are automatically retried
4. **Monitoring**: Comprehensive sync logging

### **Flexibility Benefits:**
1. **Many-to-Many Relationships**: Packages can belong to multiple groups
2. **Extensible Schema**: Easy to add new fields and relationships
3. **Multiple Group Types**: Support for countries, regions, and global packages
4. **Configurable Sync**: Adjustable sync intervals and strategies

## Testing

### **Test Caching Mechanism**
```bash
# Test caching overview
curl "https://localhost:7001/api/TestRedis/test-esim-caching"

# Test actual search (first time - should fetch from API)
curl "https://localhost:7001/api/ESim/search?locationCode=VN&type=data"

# Test again (should return from DB)
curl "https://localhost:7001/api/ESim/search?locationCode=VN&type=data"
```

### **Monitor Sync Status**
```bash
# Check sync status
curl "https://localhost:7001/api/ESim/sync-status"

# Force full sync
curl -X POST "https://localhost:7001/api/ESim/force-sync"

# Get cache statistics
curl "https://localhost:7001/api/ESim/cache-stats"
```

## Monitoring and Maintenance

### **Key Metrics to Monitor:**
1. **Cache Hit Rate**: Percentage of requests served from DB
2. **Sync Success Rate**: Percentage of successful sync operations
3. **API Response Times**: Monitor external API performance
4. **Database Performance**: Query execution times
5. **Queue Length**: Number of pending sync operations

### **Maintenance Tasks:**
1. **Regular Cleanup**: Remove old sync logs
2. **Index Optimization**: Monitor and optimize database indexes
3. **Configuration Tuning**: Adjust sync intervals based on usage patterns
4. **Error Analysis**: Review failed sync operations

This architecture provides a robust, scalable, and maintainable solution for ESim package management with optimal performance and reliability.
