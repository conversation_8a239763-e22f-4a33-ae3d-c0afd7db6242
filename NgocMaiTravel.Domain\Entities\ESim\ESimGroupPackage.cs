using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NgocMaiTravel.Domain.Entities.ESim
{
    /// <summary>
    /// ESim Group Package Mapping Entity (Many-to-Many)
    /// </summary>
    [Table("tblESimGroupPackage")]
    public class ESimGroupPackage
    {
        [Key]
        public long Id { get; set; }

        [Required]
        public long GroupId { get; set; }

        [Required]
        public long PackageId { get; set; }

        public int DisplayOrder { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;

        [StringLength(255)]
        public string? CreatedBy { get; set; }

        [StringLength(255)]
        public string? UpdatedBy { get; set; }

        // Navigation properties
        [ForeignKey("GroupId")]
        public virtual ESimGroup Group { get; set; } = null!;

        [ForeignKey("PackageId")]
        public virtual ESimPackage Package { get; set; } = null!;
    }
}
