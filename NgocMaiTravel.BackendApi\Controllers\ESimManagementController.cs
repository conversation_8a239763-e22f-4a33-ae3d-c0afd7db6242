using Microsoft.AspNetCore.Mvc;
using NgocMaiTravel.Application.Catalog.ESim.Models;
using NgocMaiTravel.Application.Catalog.ESim.Repositories;
using NgocMaiTravel.Application.Catalog.ESim.Services;
using NgocMaiTravel.Data.EF;
using Microsoft.EntityFrameworkCore;

namespace NgocMaiTravel.BackendApi.Controllers
{
    /// <summary>
    /// ESim Management Controller for monitoring and controlling sync operations
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ESimManagementController : ControllerBase
    {
        private readonly IESimRepository _repository;
        private readonly IESimSyncService _syncService;
        private readonly NgocMaiTravelDbContext _context;
        private readonly ILogger<ESimManagementController> _logger;

        public ESimManagementController(
            IESimRepository repository,
            IESimSyncService syncService,
            NgocMaiTravelDbContext context,
            ILogger<ESimManagementController> logger)
        {
            _repository = repository;
            _syncService = syncService;
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Get ESim cache statistics
        /// </summary>
        [HttpGet("cache-stats")]
        public async Task<IActionResult> GetCacheStats()
        {
            try
            {
                var groupCount = await _repository.GetGroupCountAsync();
                var packageCount = await _repository.GetPackageCountAsync();
                var lastSyncDate = await _repository.GetLastSyncDateAsync();

                var recentSyncs = await _context.ESimSyncLogs
                    .Where(l => l.StartTime >= DateTime.UtcNow.AddDays(-7))
                    .OrderByDescending(l => l.StartTime)
                    .Take(10)
                    .Select(l => new
                    {
                        l.SyncType,
                        l.Status,
                        l.StartTime,
                        l.EndTime,
                        l.Duration,
                        l.ProcessedPackages,
                        l.RequestedBy
                    })
                    .ToListAsync();

                var syncStats = await _context.ESimSyncLogs
                    .Where(l => l.StartTime >= DateTime.UtcNow.AddDays(-30))
                    .GroupBy(l => l.Status)
                    .Select(g => new { Status = g.Key, Count = g.Count() })
                    .ToListAsync();

                return Ok(new
                {
                    Success = true,
                    CacheStats = new
                    {
                        TotalGroups = groupCount,
                        TotalPackages = packageCount,
                        LastSyncDate = lastSyncDate,
                        HasData = groupCount > 0 && packageCount > 0
                    },
                    SyncStats = new
                    {
                        Last30Days = syncStats,
                        RecentSyncs = recentSyncs
                    },
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache stats");
                return BadRequest(new { Success = false, Error = ex.Message });
            }
        }

        /// <summary>
        /// Get sync status
        /// </summary>
        [HttpGet("sync-status")]
        public async Task<IActionResult> GetSyncStatus()
        {
            try
            {
                var syncNeeded = await _syncService.IsSyncNeededAsync(TimeSpan.FromHours(6));
                var lastSyncDate = await _repository.GetLastSyncDateAsync();

                var runningSyncs = await _context.ESimSyncLogs
                    .Where(l => l.Status == ESimSyncStatus.Running)
                    .OrderByDescending(l => l.StartTime)
                    .Select(l => new
                    {
                        l.Id,
                        l.SyncType,
                        l.StartTime,
                        l.ProcessedPackages,
                        l.TotalPackages,
                        l.RequestedBy,
                        DurationSoFar = DateTime.UtcNow - l.StartTime
                    })
                    .ToListAsync();

                var pendingSyncs = await _context.ESimSyncLogs
                    .Where(l => l.Status == ESimSyncStatus.Pending)
                    .OrderBy(l => l.StartTime)
                    .Select(l => new
                    {
                        l.Id,
                        l.SyncType,
                        l.StartTime,
                        l.RequestedBy
                    })
                    .ToListAsync();

                return Ok(new
                {
                    Success = true,
                    SyncStatus = new
                    {
                        SyncNeeded = syncNeeded,
                        LastSyncDate = lastSyncDate,
                        RunningSyncs = runningSyncs,
                        PendingSyncs = pendingSyncs,
                        HasRunningSyncs = runningSyncs.Any(),
                        HasPendingSyncs = pendingSyncs.Any()
                    },
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sync status");
                return BadRequest(new { Success = false, Error = ex.Message });
            }
        }

        /// <summary>
        /// Trigger full sync manually
        /// </summary>
        [HttpPost("force-full-sync")]
        public async Task<IActionResult> ForceFullSync()
        {
            try
            {
                var requestedBy = $"Manual_{User?.Identity?.Name ?? "Anonymous"}";
                await _syncService.QueueFullSyncAsync(requestedBy);

                _logger.LogInformation("Manual full sync triggered by: {RequestedBy}", requestedBy);

                return Ok(new
                {
                    Success = true,
                    Message = "Full sync has been queued",
                    RequestedBy = requestedBy,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error triggering full sync");
                return BadRequest(new { Success = false, Error = ex.Message });
            }
        }

        /// <summary>
        /// Trigger incremental sync manually
        /// </summary>
        [HttpPost("force-incremental-sync")]
        public async Task<IActionResult> ForceIncrementalSync()
        {
            try
            {
                var requestedBy = $"Manual_{User?.Identity?.Name ?? "Anonymous"}";
                await _syncService.QueueIncrementalSyncAsync(requestedBy);

                _logger.LogInformation("Manual incremental sync triggered by: {RequestedBy}", requestedBy);

                return Ok(new
                {
                    Success = true,
                    Message = "Incremental sync has been queued",
                    RequestedBy = requestedBy,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error triggering incremental sync");
                return BadRequest(new { Success = false, Error = ex.Message });
            }
        }

        /// <summary>
        /// Get sync logs with pagination
        /// </summary>
        [HttpGet("sync-logs")]
        public async Task<IActionResult> GetSyncLogs(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? status = null,
            [FromQuery] string? syncType = null)
        {
            try
            {
                var query = _context.ESimSyncLogs.AsQueryable();

                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(l => l.Status == status);
                }

                if (!string.IsNullOrEmpty(syncType))
                {
                    query = query.Where(l => l.SyncType == syncType);
                }

                var totalCount = await query.CountAsync();
                var logs = await query
                    .OrderByDescending(l => l.StartTime)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(l => new
                    {
                        l.Id,
                        l.SyncType,
                        l.Status,
                        l.StartTime,
                        l.EndTime,
                        l.Duration,
                        l.TotalPackages,
                        l.ProcessedPackages,
                        l.NewPackages,
                        l.UpdatedPackages,
                        l.ErrorMessage,
                        l.RequestedBy
                    })
                    .ToListAsync();

                return Ok(new
                {
                    Success = true,
                    Data = logs,
                    Pagination = new
                    {
                        Page = page,
                        PageSize = pageSize,
                        TotalCount = totalCount,
                        TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                    },
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sync logs");
                return BadRequest(new { Success = false, Error = ex.Message });
            }
        }

        /// <summary>
        /// Get groups and packages summary
        /// </summary>
        [HttpGet("data-summary")]
        public async Task<IActionResult> GetDataSummary()
        {
            try
            {
                var groups = await _context.ESimGroups
                    .Where(g => g.IsActive)
                    .Select(g => new
                    {
                        g.GroupCode,
                        g.GroupName,
                        g.GroupType,
                        PackageCount = g.GroupPackages.Count(gp => gp.IsActive && gp.Package.IsActive)
                    })
                    .OrderBy(g => g.GroupName)
                    .ToListAsync();

                var packageStats = await _context.ESimPackages
                    .Where(p => p.IsActive)
                    .GroupBy(p => p.PackageType)
                    .Select(g => new { PackageType = g.Key, Count = g.Count() })
                    .ToListAsync();

                return Ok(new
                {
                    Success = true,
                    Summary = new
                    {
                        Groups = groups,
                        PackagesByType = packageStats,
                        TotalGroups = groups.Count,
                        TotalPackages = groups.Sum(g => g.PackageCount)
                    },
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting data summary");
                return BadRequest(new { Success = false, Error = ex.Message });
            }
        }
    }
}
