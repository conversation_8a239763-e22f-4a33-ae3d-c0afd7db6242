# ESim File-based Logging System

## 📋 Overview

This logging system stores ESim API call information in the database while saving request/response content to files. This approach prevents database overload while maintaining detailed audit trails.

## 🏗️ Architecture

### **Database Storage (tblESimLog):**
- Log metadata (action, status, timestamp, duration, etc.)
- File paths to request/response data
- Customer information
- Error messages

### **File Storage:**
- Request/response JSON content
- Organized by date: `Logs/ESim/YYYY/MM/DD/`
- Filename format: `{logId}_{type}_{timestamp}.json`

## 📊 Database Schema

```sql
CREATE TABLE tblESimLogs (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    OrderId NVARCHAR(100),
    Action NVARCHAR(100) NOT NULL,
    CustomerEmail NVARCHAR(200),
    CustomerPhone NVARCHAR(20),
    RequestData NVARCHAR(MAX),  -- File path
    ResponseData NVARCHAR(MAX), -- File path
    Status NVARCHAR(50) NOT NULL,
    ErrorMessage NTEXT,
    Timestamp DATETIME2 NOT NULL,
    Duration TIME,
    IpAddress NVARCHAR(50),
    UserAgent NVARCHAR(500),
    ApiEndpoint NVARCHAR(200),
    HttpStatusCode INT,
    TraceId NVARCHAR(100),
    OwnerID UNIQUEIDENTIFIER
);
```

## 🔧 Usage Examples

### **1. Basic Logging in API Client**

```csharp
public class ESimBlueAPIClient
{
    private readonly IESimFileLogger _fileLogger;
    
    public async Task<ApiResult<T>> CallApiAsync<T>(string action, string endpoint, object request)
    {
        var traceId = Guid.NewGuid().ToString("N");
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Log request
            var requestJson = JsonSerializer.Serialize(request);
            var logId = await _fileLogger.LogApiCallAsync(
                action: action,
                endpoint: endpoint,
                requestData: requestJson,
                status: "pending",
                traceId: traceId
            );
            
            // Make API call
            var response = await _httpClient.PostAsync(endpoint, content);
            var responseJson = await response.Content.ReadAsStringAsync();
            
            stopwatch.Stop();
            
            // Update log with response
            await _fileLogger.UpdateLogAsync(
                logId: logId,
                responseData: responseJson,
                status: response.IsSuccessStatusCode ? "success" : "error",
                httpStatusCode: (int)response.StatusCode,
                duration: stopwatch.Elapsed
            );
            
            return ParseResponse<T>(responseJson);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            await _fileLogger.UpdateLogAsync(
                logId: logId,
                status: "error",
                duration: stopwatch.Elapsed,
                errorMessage: ex.Message
            );
            throw;
        }
    }
}
```

### **2. Reading Log Data**

```csharp
// Get log metadata
var logs = await _context.tblESimLogs
    .Where(x => x.Action == "get_all_data_packages")
    .OrderByDescending(x => x.Timestamp)
    .ToListAsync();

// Read request content from file
var requestData = await _fileLogger.ReadRequestDataAsync(logId);

// Read response content from file
var responseData = await _fileLogger.ReadResponseDataAsync(logId);
```

### **3. Admin API Usage**

```javascript
// Search logs
const searchLogs = async (filters) => {
    const response = await fetch('/api/ESimLog/search', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Api-Key': 'your-api-key',
            'Authorization': 'Bearer admin-token'
        },
        body: JSON.stringify({
            action: filters.action,
            status: filters.status,
            fromDate: filters.fromDate,
            toDate: filters.toDate,
            pageIndex: 1,
            pageSize: 50
        })
    });
    
    return await response.json();
};

// Get request data
const getRequestData = async (logId) => {
    const response = await fetch(`/api/ESimLog/${logId}/request`, {
        headers: {
            'X-Api-Key': 'your-api-key',
            'Authorization': 'Bearer admin-token'
        }
    });
    
    return await response.json();
};

// Get response data
const getResponseData = async (logId) => {
    const response = await fetch(`/api/ESimLog/${logId}/response`, {
        headers: {
            'X-Api-Key': 'your-api-key',
            'Authorization': 'Bearer admin-token'
        }
    });
    
    
    return await response.json();
};
```

## 📁 File Organization

```
C:\Logs\ESim\
├── 2024\
│   ├── 12\
│   │   ├── 30\
│   │   │   ├── abc123_request_20241230_143022.json
│   │   │   ├── abc123_response_20241230_143025.json
│   │   │   ├── def456_request_20241230_144011.json
│   │   │   └── def456_response_20241230_144015.json
│   │   └── 31\
│   └── 01\
└── 2025\
```

## 🔍 Log Analysis Examples

### **Performance Analysis**
```sql
-- Average response times by action
SELECT 
    Action,
    AVG(DATEDIFF(MILLISECOND, '00:00:00', Duration)) as AvgResponseTimeMs,
    COUNT(*) as RequestCount
FROM tblESimLogs 
WHERE Status = 'success' 
    AND Duration IS NOT NULL
    AND Timestamp >= DATEADD(day, -7, GETDATE())
GROUP BY Action
ORDER BY AvgResponseTimeMs DESC;
```

### **Error Analysis**
```sql
-- Most common errors
SELECT 
    Action,
    ErrorMessage,
    COUNT(*) as ErrorCount
FROM tblESimLogs 
WHERE Status = 'error'
    AND Timestamp >= DATEADD(day, -7, GETDATE())
GROUP BY Action, ErrorMessage
ORDER BY ErrorCount DESC;
```

### **Usage Patterns**
```sql
-- Requests by hour
SELECT 
    DATEPART(HOUR, Timestamp) as Hour,
    COUNT(*) as RequestCount
FROM tblESimLogs 
WHERE Timestamp >= DATEADD(day, -1, GETDATE())
GROUP BY DATEPART(HOUR, Timestamp)
ORDER BY Hour;
```

## 🧹 Maintenance

### **Automatic Cleanup**
```csharp
// Clean up logs older than 30 days
await _fileLogger.CleanupOldLogsAsync(30);
```

### **Manual Cleanup**
```csharp
// Clean up specific date range
var oldLogs = await _context.tblESimLogs
    .Where(x => x.Timestamp < DateTime.Now.AddDays(-60))
    .ToListAsync();

foreach (var log in oldLogs)
{
    // Delete files
    if (File.Exists(log.RequestData)) File.Delete(log.RequestData);
    if (File.Exists(log.ResponseData)) File.Delete(log.ResponseData);
}

// Remove database entries
_context.tblESimLogs.RemoveRange(oldLogs);
await _context.SaveChangesAsync();
```

## ⚙️ Configuration

### **appsettings.json**
```json
{
  "ESimLogging": {
    "BasePath": "C:\\Logs\\ESim",
    "MaxFileSize": "10MB",
    "RetentionDays": 30
  }
}
```

### **Dependency Injection**
```csharp
// Program.cs
builder.Services.AddTransient<IESimFileLogger, ESimFileLogger>();
```

## 🚨 Error Handling

### **File System Errors**
- Automatic directory creation
- Graceful handling of disk space issues
- Fallback to system logs if file operations fail

### **Database Errors**
- Transaction rollback on failures
- Retry mechanisms for temporary issues
- Detailed error logging

## 📈 Benefits

1. **Database Performance**: Keeps database lean by storing only metadata
2. **Detailed Audit Trail**: Full request/response data available when needed
3. **Easy Analysis**: Structured data in database for queries
4. **Scalable**: File-based storage scales better than large database fields
5. **Maintenance**: Automatic cleanup prevents storage bloat
6. **Security**: Sensitive data in files can have different access controls

## 🔐 Security Considerations

1. **File Permissions**: Restrict access to log directories
2. **Data Encryption**: Consider encrypting sensitive log files
3. **Access Control**: Admin-only APIs for log access
4. **Data Retention**: Automatic cleanup of old logs
5. **Audit Trail**: Log access to log files themselves

This file-based logging system provides comprehensive ESim API monitoring while maintaining optimal database performance.
