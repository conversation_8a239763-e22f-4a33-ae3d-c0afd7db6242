-- ESim Group Table (Countries/Regions)
CREATE TABLE tblESimGroup (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    GroupCode NVARCHAR(10) NOT NULL, -- Country code like 'VN', 'JP', 'US'
    GroupName NVARCHAR(255) NOT NULL, -- Country name like 'Vietnam', 'Japan', 'United States'
    GroupType NVARCHAR(50) NOT NULL DEFAULT 'COUNTRY', -- 'COUNTRY', 'REGION', 'GLOBAL'
    Description NVARCHAR(1000) NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    DisplayOrder INT NOT NULL DEFAULT 0,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy NVARCHAR(255) NULL,
    UpdatedBy NVARCHAR(255) NULL,
    
    CONSTRAINT UK_tblESimGroup_GroupCode UNIQUE (GroupCode),
    INDEX IX_tblESimGroup_GroupType (GroupType),
    INDEX IX_tblESimGroup_IsActive (IsActive),
    INDEX IX_tblESimGroup_DisplayOrder (DisplayOrder)
);

-- ESim Package Table (Individual packages)
CREATE TABLE tblESimPackage (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    PackageId NVARCHAR(50) NOT NULL, -- External package ID from API
    Sku NVARCHAR(100) NOT NULL, -- Package SKU
    PackageName NVARCHAR(255) NOT NULL,
    Description NVARCHAR(2000) NULL,
    DataAmount BIGINT NOT NULL, -- Data in bytes
    ValidityDays INT NOT NULL, -- Validity period in days
    Price DECIMAL(18,2) NOT NULL,
    Currency NVARCHAR(10) NOT NULL DEFAULT 'USD',
    OriginalPrice DECIMAL(18,2) NULL, -- Original price before discount
    DiscountPercent DECIMAL(5,2) NULL, -- Discount percentage
    PackageType NVARCHAR(50) NOT NULL DEFAULT 'DATA', -- 'DATA', 'VOICE', 'SMS', 'COMBO'
    NetworkType NVARCHAR(50) NULL, -- '4G', '5G', '3G'
    IsUnlimited BIT NOT NULL DEFAULT 0,
    IsTopUpSupported BIT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    ApiSource NVARCHAR(50) NOT NULL DEFAULT 'ESimBlue', -- Source API
    ExternalData NVARCHAR(MAX) NULL, -- JSON data from external API
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    LastSyncDate DATETIME2 NULL, -- Last sync from API
    CreatedBy NVARCHAR(255) NULL,
    UpdatedBy NVARCHAR(255) NULL,
    
    CONSTRAINT UK_tblESimPackage_PackageId UNIQUE (PackageId),
    CONSTRAINT UK_tblESimPackage_Sku UNIQUE (Sku),
    INDEX IX_tblESimPackage_IsActive (IsActive),
    INDEX IX_tblESimPackage_PackageType (PackageType),
    INDEX IX_tblESimPackage_Price (Price),
    INDEX IX_tblESimPackage_DataAmount (DataAmount),
    INDEX IX_tblESimPackage_ValidityDays (ValidityDays),
    INDEX IX_tblESimPackage_LastSyncDate (LastSyncDate)
);

-- ESim Group Package Mapping Table (Many-to-Many relationship)
CREATE TABLE tblESimGroupPackage (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    GroupId BIGINT NOT NULL,
    PackageId BIGINT NOT NULL,
    DisplayOrder INT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy NVARCHAR(255) NULL,
    UpdatedBy NVARCHAR(255) NULL,
    
    CONSTRAINT FK_tblESimGroupPackage_GroupId FOREIGN KEY (GroupId) REFERENCES tblESimGroup(Id) ON DELETE CASCADE,
    CONSTRAINT FK_tblESimGroupPackage_PackageId FOREIGN KEY (PackageId) REFERENCES tblESimPackage(Id) ON DELETE CASCADE,
    CONSTRAINT UK_tblESimGroupPackage_GroupPackage UNIQUE (GroupId, PackageId),
    INDEX IX_tblESimGroupPackage_GroupId (GroupId),
    INDEX IX_tblESimGroupPackage_PackageId (PackageId),
    INDEX IX_tblESimGroupPackage_IsActive (IsActive),
    INDEX IX_tblESimGroupPackage_DisplayOrder (DisplayOrder)
);

-- ESim Sync Log Table (Track sync operations)
CREATE TABLE tblESimSyncLog (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    SyncType NVARCHAR(50) NOT NULL, -- 'FULL_SYNC', 'INCREMENTAL_SYNC', 'GROUP_SYNC'
    Status NVARCHAR(50) NOT NULL, -- 'PENDING', 'RUNNING', 'SUCCESS', 'FAILED'
    StartTime DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    EndTime DATETIME2 NULL,
    Duration INT NULL, -- Duration in milliseconds
    TotalPackages INT NULL,
    ProcessedPackages INT NULL,
    NewPackages INT NULL,
    UpdatedPackages INT NULL,
    ErrorMessage NVARCHAR(MAX) NULL,
    RequestedBy NVARCHAR(255) NULL,
    ApiSource NVARCHAR(50) NOT NULL DEFAULT 'ESimBlue',
    
    INDEX IX_tblESimSyncLog_SyncType (SyncType),
    INDEX IX_tblESimSyncLog_Status (Status),
    INDEX IX_tblESimSyncLog_StartTime (StartTime)
);

-- Insert default groups (common countries)
INSERT INTO tblESimGroup (GroupCode, GroupName, GroupType, Description, DisplayOrder) VALUES
('VN', 'Vietnam', 'COUNTRY', 'Vietnam eSIM packages', 1),
('JP', 'Japan', 'COUNTRY', 'Japan eSIM packages', 2),
('KR', 'South Korea', 'COUNTRY', 'South Korea eSIM packages', 3),
('TH', 'Thailand', 'COUNTRY', 'Thailand eSIM packages', 4),
('SG', 'Singapore', 'COUNTRY', 'Singapore eSIM packages', 5),
('US', 'United States', 'COUNTRY', 'United States eSIM packages', 6),
('GB', 'United Kingdom', 'COUNTRY', 'United Kingdom eSIM packages', 7),
('FR', 'France', 'COUNTRY', 'France eSIM packages', 8),
('DE', 'Germany', 'COUNTRY', 'Germany eSIM packages', 9),
('AU', 'Australia', 'COUNTRY', 'Australia eSIM packages', 10),
('ASIA', 'Asia Region', 'REGION', 'Asia regional eSIM packages', 20),
('EUROPE', 'Europe Region', 'REGION', 'Europe regional eSIM packages', 21),
('GLOBAL', 'Global', 'GLOBAL', 'Global eSIM packages', 30);
