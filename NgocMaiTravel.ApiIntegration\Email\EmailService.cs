﻿using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Configuration;
using MimeKit;
using System.Reflection;
using System.Text;
using Newtonsoft.Json.Linq;
using NgocMaiTravel.Data.Entities;
using NgocMaiTravel.ViewModels.Common;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.ViewModels.Catalog.World;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using NgocMaiTravel.ViewModels.Catalog.Flight;
using Microsoft.IdentityModel.Tokens;


namespace NgocMaiTravel.ApiIntegration.Email
{
    public class EmailService: IEmailService
    {
        private readonly IConfiguration _configuration;
        private readonly NgocMaiTravelDbContext _context;

        public EmailService(IConfiguration configuration, NgocMaiTravelDbContext context)
        {
            _configuration = configuration;
            _context = context;
        }

        public async Task SendBookTripResultAsync(string toEmail, string subject, FlightRequest request)
        {
            var dataCart = JObject.Parse(request.Note);
            #region email setting default 
            var _senderName = "Ngoc Mai Travel";
            var _senderEmail = _configuration["EmailSettings:SenderEmail"];
            var _smtpServer = _configuration["EmailSettings:SmtpServer"];
            var _port = int.Parse(_configuration["EmailSettings:Port"]);
            var _password = _configuration["EmailSettings:Password"];

            if(request.OwnerID != null && request.OwnerID != Guid.Empty)
            {
                //get email setting from db
                var apikeySetting = await _context.tblApiKeyLibs.FirstOrDefaultAsync(t => t.Id == request.OwnerID);
                if (apikeySetting != null)
                {
                    _senderName = apikeySetting.Name;
                    _senderEmail = apikeySetting.SenderEmail;
                    _smtpServer = apikeySetting.SmtpServer;
                    _port = apikeySetting.EmailPort.HasValue ? apikeySetting.EmailPort.Value : _port;
                    _password = apikeySetting.EmailPass;
                }
            }
            #endregion

            var emailMessage = new MimeMessage();
            emailMessage.From.Add(new MailboxAddress(_senderName, _senderEmail));
            emailMessage.To.Add(new MailboxAddress("", toEmail));
            emailMessage.Subject = subject;

            //LOAD FILE HTML
            string htmlContent = ReadEmbeddedHtmlFile("BookTripResult.html");
            var bodyBuilder = new BodyBuilder { HtmlBody = htmlContent };

            //replace placeholders in the email template with actual values
            bodyBuilder.HtmlBody = bodyBuilder.HtmlBody.Replace("{{CONTACT_NAME}}", request.CustomerName);
            bodyBuilder.HtmlBody = bodyBuilder.HtmlBody.Replace("{{CONTACT_PHONE}}", request.PhoneNumber);
            bodyBuilder.HtmlBody = bodyBuilder.HtmlBody.Replace("{{CONTACT_MAIL}}", request.Email);


            // Replace placeholders in the email template with actual values
            var passengersHtml = GeneratePassengerHtml(dataCart);
            var ticketHtml = await GenerateTicketHtml(dataCart);
            var noteResultHtml = await GenerateResultNoteHtml(request.OwnerID);
            var supportEndMail = GenerateSupportEndEmail(request.OwnerID);

            bodyBuilder.HtmlBody = bodyBuilder.HtmlBody.Replace("{{PASSENGER_CONTENT}}", passengersHtml);
            bodyBuilder.HtmlBody = bodyBuilder.HtmlBody.Replace("{{TICKET_CONTENT}}", ticketHtml);
            bodyBuilder.HtmlBody = bodyBuilder.HtmlBody.Replace("{{NOTE_RESULT}}", noteResultHtml);
            bodyBuilder.HtmlBody = bodyBuilder.HtmlBody.Replace("{{SUPPORT_ENDMAIL}}", supportEndMail);


            emailMessage.Body = bodyBuilder.ToMessageBody();

            using (var client = new SmtpClient())
            {
                try
                {
                    await client.ConnectAsync(_smtpServer, _port,SecureSocketOptions.StartTls);
                    await client.AuthenticateAsync(_senderEmail, _password);
                    await client.SendAsync(emailMessage);
                    await client.DisconnectAsync(true);

                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error sending email: {ex.Message}");
                }
            }
        }

        public async Task<ApiResult<bool>> SentEmailBookTrip(string OrderCode, bool sendAdmin)
        {
            var flightRq = await _context.FlightRequests.FirstOrDefaultAsync(x => x.Id == OrderCode);
            if (flightRq == null)
            {
                return new ApiResult<bool>
                {
                    IsSuccessed = false,
                    Message = "Không tìm thấy thông tin đặt chuyến bay"
                };
            }

            var ownerId = flightRq.OwnerID;
            var featureCheck = sendAdmin ? "SendEmailToAdmin" : "SendEmail";
            var isSendEmail = await _context.tblFeatures
                .Where(t => t.FeatureName == featureCheck && t.OwnerID == ownerId)
                .Select(t => t.IsUnlocked)
                .FirstOrDefaultAsync();

            if (!isSendEmail)
            {
                return new ApiErrorResult<bool>("Feature Send Email is turn off");
            }

            var title = flightRq.Status == 0 ? "Xác nhận đặt chuyến bay" : "Thông tin chuyến bay";

            if (sendAdmin)
            {
                title =  "Thông báo đơn hàng mới" ;
            }
            var toEmail = flightRq.Email;
            if (sendAdmin)
            {
                if(flightRq.OwnerID == null)
                {
                    toEmail = _configuration["EmailReciveAdminDefault"] ?? "<EMAIL>";
                }
                else
                {
                    toEmail = await _context.tblApiKeyLibs.Where(x => x.Id == flightRq.OwnerID).Select(x => x.SenderEmail).FirstOrDefaultAsync();
                }
            }
            await SendBookTripResultAsync(toEmail, title, flightRq);

            return new ApiSuccessResult<bool>();
        }

        #region support html
        private string ReadEmbeddedHtmlFile(string fileName)
        {
            var assembly = Assembly.GetExecutingAssembly();
            var resourceName = $"{assembly.GetName().Name}.Resources.{fileName}"; // Ví dụ: YourLibraryProject.Resources.email-template.html

            using Stream stream = assembly.GetManifestResourceStream(resourceName);
            if (stream == null) throw new FileNotFoundException($"Embedded file '{fileName}' not found.");

            using StreamReader reader = new StreamReader(stream);
            var html= reader.ReadToEnd();
            //return ConvertToInlineCss(html);
            return html;
        }

        public string GeneratePassengerHtml(JObject data)
        {
            if (data == null) return string.Empty;
            var listPax = data["paxList"] as JArray;
            if (listPax == null)
            {
                throw new InvalidOperationException("ListPax is missing or invalid.");
            }

            var passengers = listPax.Select(pax =>
            {
                var paxObj = pax as JObject;
                if (paxObj == null) return null;

                return new Passenger
                {
                    Type = paxObj["type"]?.ToString(),
                    FullName = paxObj["fullname"]?.ToString(),
                    Birthday = paxObj["birthdaytString"]?.ToString(),
                    Baggages = paxObj["baggages"] is JArray baggages ? baggages
                                .Select(b => new Baggage
                                {
                                    Type = b["type"]?.ToString(),
                                    WeightBag = b["WeightBag"]?.ToString(),
                                    SsrCode = b["SsrCode"]?.ToString()
                                }).ToList() : new List<Baggage>(),
                    Gender = paxObj["gender"]?.ToString()
                };
            }).Where(p => p != null).ToList();

            var adults = passengers.Where(p => p.Type != "infant").ToList();
            var infants = passengers.Where(p => p.Type == "infant").ToList();

            for (int i = 0; i < Math.Min(adults.Count, infants.Count); i++)
            {
                adults[i].WithInfant = infants[i];
            }
            passengers = adults;


            StringBuilder html = new StringBuilder();

            html.Append(@"
        <table width='600' border='0' cellpadding='0' cellspacing='0'
                    style='border-collapse: collapse; margin-bottom: 20px; background: #f8f8f8; border-radius: 8px;'>
                    <tr>
                        <td style='padding: 15px 20px;'>
                            <h2 style='margin: 0 0 15px 0; color: #fb6340; font-size: 18px;'>Danh Sách Khách</h2>
                            <table width='100%' border='0' cellpadding='0' cellspacing='0'
                                style='border-collapse: collapse;'>
                                <tr>
                                    <th style='padding: 10px; text-align: left;'>Họ và tên</th>
                                    <th style='padding: 10px; text-align: left;'>Ngày sinh</th>
                                    <th style='padding: 10px; text-align: left;'>Giới tính</th>
                                </tr>");

            foreach (var pax in passengers)
            {
                html.Append($@"
                <tr style='border-bottom: 1px solid #ddd;'>
                    <td style='padding: 10px;'>
                        <div>{pax.FullName}</div>");

                if (pax.WithInfant != null)
                {
                    html.Append($@"
                        <div style='font-size: 12px; color: red;'>
                            * Em bé: {pax.WithInfant.FullName} - {pax.WithInfant.Birthday} -
                            {(pax.WithInfant.Gender == "MSTR" ? "Bé trai" : pax.WithInfant.Gender == "MISS" ? "Bé gái" : "Khác")}
                        </div>");
                }

                if (pax.Baggages != null && pax.Baggages.Any(b => !string.IsNullOrEmpty(b.SsrCode)))
                {
                    foreach (var baggage in pax.Baggages.Where(b => !string.IsNullOrEmpty(b.SsrCode)))
                    {
                        html.Append($@"
                        <div style='font-size: 12px; color: gray;'>
                            {baggage.Type} - {baggage.WeightBag} KG
                        </div>");
                    }
                }

                html.Append($@"
                    </td>
                    <td style='padding: 10px;'>{(pax.Birthday )}</td>
                    <td style='padding: 10px;'>{GetGenderText(pax.Gender)}</td>
                </tr>");
            }

            html.Append(@"
                    </table>
                </td>
            </tr>
        </table>");

            return html.ToString();
        }

        private string GetGenderText(string gender)
        {
            return gender switch
            {
                "MR" => "Nam",
                "MRS" or "MS" => "Nữ",
                _ => "Khác"
            };
        }

        public async Task<string> GenerateTicketHtml(JObject data)
        {
            if (data == null) return string.Empty;
            var fullData = data["full"];
            
            if (fullData == null)
            {
                throw new InvalidOperationException("fullData is missing or invalid.");
            }
            //var InventoriesSelected = fullData["InventoriesSelected"] as JArray;
            //convert to list object InventorySelected
            var inventoriesSelectedArray = fullData["InventoriesSelected"] as JArray;
            if (inventoriesSelectedArray == null)
            {
                throw new InvalidOperationException("InventoriesSelected is missing or invalid.");
            }

            var listInventoriesSelected = inventoriesSelectedArray
                .Select(item => item.ToObject<InventorySelected>())
                .ToList();

            var airportsCode = listInventoriesSelected.SelectMany(x => x.segment.Legs.SelectMany(l => new[] { l.DepartureCode, l.ArrivalCode })).Distinct().ToList();

            var airports = await _context.tblFlightAirports
                .Where(x => airportsCode.Contains(x.IataCode))
                .ToDictionaryAsync(x => x.IataCode, x => new WorldAirportVM()
                {
                    Name =  x.NameVN,
                    CityName =  x.MunicipalityVN
                });

            string ticketNormal = ReadEmbeddedHtmlFile("Ticket.html");
            var html = new StringBuilder();
            foreach (var ticket in listInventoriesSelected)
            {
                var index = 0;
                foreach (var leg in ticket.segment.Legs)
                {
                    var ticketItem = ticketNormal;
                    ticketItem = ticketItem.Replace("{{DEPART_ARRIVAL}}", $"{airports[leg.DepartureCode]?.CityName} - {airports[leg.ArrivalCode]?.CityName}");
                    ticketItem = ticketItem.Replace("{{DEPART_DATETIME}}", $"{GetDayInWeek(leg.DepartureDate)} - {FormatddMMyyyy(leg.DepartureDate)}");
                    ticketItem = ticketItem.Replace("{{AIRLINE}}", $"{leg.OperatingAirlines}");
                    ticketItem = ticketItem.Replace("{{AIRLINE_NAME}}", $"{leg.OperatingAirlinesName}");
                    ticketItem = ticketItem.Replace("{{FLIGHT}}", $"{leg.OperatingAirlines}{leg.FlightNumber}");


                    ticketItem = ticketItem.Replace("{{DEPART_TIME}}", $"{GetTimeFromDateTime(leg.DepartureDate)}");
                    ticketItem = ticketItem.Replace("{{DEPART_DAY}}", $"{FormatddMMyyyy(leg.DepartureDate)}");
                    ticketItem = ticketItem.Replace("{{DEPART_AIRPORT}}", $"{leg.DepartureCode} - {airports[leg.DepartureCode]?.CityName}");

                    ticketItem = ticketItem.Replace("{{EQUIPMENT}}", $"{leg.Equipment}");
                    ticketItem = ticketItem.Replace("{{DURATION}}", $"{GetDuration(leg)}");


                    ticketItem = ticketItem.Replace("{{ARRIVAL_TIME}}", $"{GetTimeFromDateTime(leg.ArrivalDate)}");
                    ticketItem = ticketItem.Replace("{{ARRIVAL_DAY}}", $"{FormatddMMyyyy(leg.ArrivalDate)}");
                    ticketItem = ticketItem.Replace("{{ARRIVAL_AIRPORT}}", $"{airports[leg.ArrivalCode]?.CityName} - {leg.ArrivalCode}");

                    ticketItem = ticketItem.Replace("{{DEPART_TERMINAL}}", $"{leg.DepartureTerminal}");
                    ticketItem = ticketItem.Replace("{{ARRIVAL_TERMINAL}}", $"{leg.ArrivalTerminal}");

                    var handBagge = "";
                    if (ticket.inventorySelected?.BookingInfos.ElementAt(index)?.HandBaggage > 1 && ticket.inventorySelected?.BookingInfos.ElementAt(index)?.HandWeightBag != 0)
                    {
                        handBagge += ticket.inventorySelected?.BookingInfos.ElementAt(index)?.HandBaggage + "x";
                    }
                    if (ticket.inventorySelected?.BookingInfos.ElementAt(index)?.HandWeightBag == 0)
                    {
                        handBagge = "Không bao gồm";
                    }
                    else
                    {
                        handBagge += $" {ticket.inventorySelected?.BookingInfos.ElementAt(index)?.HandWeightBag} kg";
                    }
                    ticketItem = ticketItem.Replace("{{HAND_BAGGE}}", $"{handBagge}");

                    var checkedBagge = "";
                    if (ticket.inventorySelected?.BookingInfos.ElementAt(index)?.BagPieces > 1 && ticket.inventorySelected?.BookingInfos.ElementAt(index)?.WeightBag != 0)
                    {
                        checkedBagge += ticket.inventorySelected?.BookingInfos.ElementAt(index)?.BagPieces + "x";
                    }
                    if (ticket.inventorySelected?.BookingInfos.ElementAt(index)?.WeightBag == 0)
                    {
                        checkedBagge = "Không bao gồm";
                    }
                    else
                    {
                        checkedBagge += $" {ticket.inventorySelected?.BookingInfos.ElementAt(index)?.WeightBag} kg";
                    }
                    ticketItem = ticketItem.Replace("{{WEIGHT_BAG}}", $"{checkedBagge}");

                    html.Append(ticketItem);
                    index++;
                }
            }

            return html.ToString();
        }

        public async Task<string> GenerateResultNoteHtml(Guid? OwnerID)
        {
            if (OwnerID != Guid.Empty)
            {
                try
                {
                    var features = await (from f in _context.tblFeatures
                                          where f.OwnerID == OwnerID && f.FeatureName == "note-result"
                                          select f.Note).FirstOrDefaultAsync();

                    if (features != null)
                    {
                        string htmlNoteContent = ReadEmbeddedHtmlFile("NoteResult.html");

                        var dataNote = JObject.Parse(features);
                        htmlNoteContent = htmlNoteContent.Replace("{{DOMESTIC}}", $"{dataNote["TimeCheckIn"]?["Domestic"]}");
                        htmlNoteContent = htmlNoteContent.Replace("{{INTERNATIONAL}}", $"{dataNote["TimeCheckIn"]?["International"]}");

                        // INDENTITY_DOCUMENT
                        var IdentityDocuments = "";
                        if (dataNote["IdentityDocuments"] is JArray identites && identites.Count > 0)
                        {
                            foreach (var item in identites)
                            {
                                IdentityDocuments += $"<p style='margin: 0; font-size: 14px; line-height: 1.5;'>{item?["value"]}</p>";
                            }
                        }
                        htmlNoteContent = htmlNoteContent.Replace("{{INDENTITY_DOCUMENT}}", IdentityDocuments);

                        // SpecialRules
                        var SpecialRules = "";
                        if (dataNote["SpecialRules"] is JArray specialRulesList && specialRulesList.Count > 0)
                        {
                            foreach (var item in specialRulesList)
                            {
                                SpecialRules += $"<li style='margin-bottom: 5px;'>{item?["value"]}</li>";
                            }
                        }
                        htmlNoteContent = htmlNoteContent.Replace("{{SPECIAL_RULES}}", SpecialRules);

                        return htmlNoteContent;
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception as needed
                }
            }

            return ReadEmbeddedHtmlFile("MainNoteResult.html");
        }

        public  string GenerateSupportEndEmail(Guid? OwnerID)
        {
            if(OwnerID == Guid.Empty || OwnerID == null)
            {
                return ReadEmbeddedHtmlFile("SupportEndMail.html");
            }
            return "";
        }

        public static string GetDuration(SearchItineraryAirItineraryAirSegmentLegModel leg)
        {
            if (leg == null) return "";
            var duration = leg.ArrivalDate - leg.DepartureDate;
            return $"{duration.Hours}h{duration.Minutes:D2}";
        }
        public static string GetDayInWeek(DateTimeOffset date)
        {
            if(date == null)
            {
                return "";
            }
            string[] days = { "Chủ nhật", "Thứ 2", "Thứ 3", "Thứ 4", "Thứ 5", "Thứ 6", "Thứ 7" };

            return days[(int)date.DayOfWeek];
        }
        public static string FormatddMMyyyy(DateTimeOffset date)
        {
            if(date == null)
            {
                return "";
            }
            return date.ToString("dd/MM/yyyy");
        }

        public static string GetTimeFromDateTime(DateTimeOffset date)
        {
            if (date == null)
            {
                return "";
            }
            return date.ToString("HH:mm");
        }
        #endregion

    }



public class InventorySelected
    {
        public string airCodeRef { get; set; }
        public string bookGDS { get; set; }
        public bool combine { get; set; }
        public SearchItineraryAirItineraryFareModel inventorySelected { get; set; }
        public SearchItineraryAirItineraryAirSegmentModel segment { get; set; }
    }

    public class Passenger
    {
        public string Type { get; set; }
        public string FullName { get; set; }
        public string Birthday { get; set; }
        public string Gender { get; set; }
        public List<Baggage> Baggages { get; set; } = new();
        public Passenger WithInfant { get; set; } // Em bé đi kèm
    }

    public class Baggage
    {
        public string Type { get; set; }
        public string WeightBag { get; set; }
        public string SsrCode { get; set; }
    }

}
