using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NgocMaiTravel.Application.Common.Diagnostics;
using NgocMaiTravel.ViewModels.Common;
using System.Net;
using System.Threading.Tasks;

namespace NgocMaiTravel.BackendApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DiagnosticsController : ControllerBase
    {
        /// <summary>
        /// Test network connectivity to NMBooking API
        /// </summary>
        [HttpGet("network-test")]
        public async Task<ApiResult<string>> TestNetworkConnectivity()
        {
            try
            {
                var apiUrl = "https://prod-api-b2b.ngocmaitravel.vn";
                var diagnostics = await NetworkDiagnostics.DiagnoseApiConnectivityAsync(apiUrl);
                
                return new ApiSuccessResult<string>(diagnostics);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<string>($"Diagnostics failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Test connectivity to a specific host and port
        /// </summary>
        [HttpGet("test-connectivity")]
        public async Task<ApiResult<bool>> TestConnectivity(string host, int port = 443)
        {
            try
            {
                var result = await NetworkDiagnostics.TestConnectivityAsync(host, port);
                return new ApiSuccessResult<bool>(result);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<bool>($"Connectivity test failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Test DNS resolution for a hostname
        /// </summary>
        [HttpGet("test-dns")]
        public async Task<ApiResult<bool>> TestDnsResolution(string hostname)
        {
            try
            {
                var result = await NetworkDiagnostics.TestDnsResolutionAsync(hostname);
                return new ApiSuccessResult<bool>(result);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<bool>($"DNS test failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Ping a host
        /// </summary>
        [HttpGet("ping")]
        public async Task<ApiResult<bool>> PingHost(string host)
        {
            try
            {
                var result = await NetworkDiagnostics.PingHostAsync(host);
                return new ApiSuccessResult<bool>(result);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<bool>($"Ping failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Test HTTP connectivity with detailed diagnostics
        /// </summary>
        [HttpGet("http-test")]
        public async Task<ApiResult<string>> TestHttpConnectivity(string url = "https://prod-api-b2b.ngocmaitravel.vn")
        {
            try
            {
                var result = await NgocMaiTravel.Application.Common.Diagnostics.HttpClientDiagnostics.TestHttpConnectivityAsync(url);
                return new ApiSuccessResult<string>(result);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<string>($"HTTP test failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Get ServicePointManager settings
        /// </summary>
        [HttpGet("servicepoint-info")]
        public ApiResult<object> GetServicePointInfo()
        {
            try
            {
                NgocMaiTravel.Application.Common.Diagnostics.HttpClientDiagnostics.LogServicePointManagerSettings();
                NgocMaiTravel.Application.Common.Diagnostics.HttpClientDiagnostics.LogNetworkPerformanceMetrics();

                var info = new
                {
                    DefaultConnectionLimit = ServicePointManager.DefaultConnectionLimit,
                    Expect100Continue = ServicePointManager.Expect100Continue,
                    UseNagleAlgorithm = ServicePointManager.UseNagleAlgorithm,
                    EnableDnsRoundRobin = ServicePointManager.EnableDnsRoundRobin,
                    DnsRefreshTimeout = ServicePointManager.DnsRefreshTimeout,
                    MaxServicePointIdleTime = ServicePointManager.MaxServicePointIdleTime,
                    MaxServicePoints = ServicePointManager.MaxServicePoints
                };

                return new ApiSuccessResult<object>(info);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<object>($"ServicePoint info failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Test detailed TCP connectivity
        /// </summary>
        [HttpGet("tcp-detailed")]
        public async Task<ApiResult<string>> TestTcpConnectivityDetailed(string host = "prod-api-b2b.ngocmaitravel.vn", int port = 443)
        {
            try
            {
                var (success, details) = await NetworkDiagnostics.TestConnectivityDetailedAsync(host, port, 15000);
                var result = $"TCP Test Result: {(success ? "SUCCESS" : "FAILED")}\n\nDetails:\n{details}";
                return new ApiSuccessResult<string>(result);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<string>($"TCP test failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Diagnose proxy settings and connectivity
        /// </summary>
        [HttpGet("proxy-test")]
        public async Task<ApiResult<string>> TestProxyConnectivity(string url = "https://prod-api-b2b.ngocmaitravel.vn")
        {
            try
            {
                var result = await NgocMaiTravel.Application.Common.Diagnostics.ProxyDiagnostics.DiagnoseProxySettingsAsync(url);
                return new ApiSuccessResult<string>(result);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<string>($"Proxy test failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Get proxy configuration summary
        /// </summary>
        [HttpGet("proxy-config")]
        public ApiResult<string> GetProxyConfiguration()
        {
            try
            {
                var result = NgocMaiTravel.Application.Common.Diagnostics.ProxyDiagnostics.GetProxyConfigurationSummary();
                return new ApiSuccessResult<string>(result);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<string>($"Proxy config failed: {ex.Message}");
            }
        }
    }
}
