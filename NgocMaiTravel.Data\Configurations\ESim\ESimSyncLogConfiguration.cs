using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NgocMaiTravel.Data.Entities.ESim;

namespace NgocMaiTravel.Data.Configurations.ESim
{
    /// <summary>
    /// Entity configuration for ESimSyncLog
    /// </summary>
    public class ESimSyncLogConfiguration : IEntityTypeConfiguration<ESimSyncLog>
    {
        public void Configure(EntityTypeBuilder<ESimSyncLog> builder)
        {
            // Table name
            builder.ToTable("tblESimSyncLog");

            // Primary key
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasComment("Primary key");

            // Sync operation details
            builder.Property(e => e.SyncType)
                .IsRequired()
                .HasMaxLength(50)
                .HasComment("Sync type: FULL_SYNC, INCREMENTAL_SYNC, GROUP_SYNC");

            builder.Property(e => e.Status)
                .IsRequired()
                .HasMaxLength(50)
                .HasComment("Status: PENDING, RUNNING, SUCCESS, FAILED");

            // Timing information
            builder.Property(e => e.StartTime)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Sync start timestamp");

            builder.Property(e => e.EndTime)
                .HasComment("Sync end timestamp");

            builder.Property(e => e.Duration)
                .HasComment("Duration in milliseconds");

            // Statistics
            builder.Property(e => e.TotalPackages)
                .HasComment("Total packages to process");

            builder.Property(e => e.ProcessedPackages)
                .HasComment("Number of packages processed");

            builder.Property(e => e.NewPackages)
                .HasComment("Number of new packages added");

            builder.Property(e => e.UpdatedPackages)
                .HasComment("Number of packages updated");

            // Error information
            builder.Property(e => e.ErrorMessage)
                .HasColumnType("NVARCHAR(MAX)")
                .HasComment("Error message if sync failed");

            // Metadata
            builder.Property(e => e.RequestedBy)
                .HasMaxLength(255)
                .HasComment("User or system that requested the sync");

            builder.Property(e => e.ApiSource)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("ESimBlue")
                .HasComment("Source API provider");

            // Indexes for performance and querying
            builder.HasIndex(e => e.SyncType)
                .HasDatabaseName("IX_tblESimSyncLog_SyncType");

            builder.HasIndex(e => e.Status)
                .HasDatabaseName("IX_tblESimSyncLog_Status");

            builder.HasIndex(e => e.StartTime)
                .HasDatabaseName("IX_tblESimSyncLog_StartTime");

            builder.HasIndex(e => new { e.SyncType, e.Status })
                .HasDatabaseName("IX_tblESimSyncLog_SyncType_Status");

            builder.HasIndex(e => new { e.StartTime, e.Status })
                .HasDatabaseName("IX_tblESimSyncLog_StartTime_Status");
        }
    }
}
