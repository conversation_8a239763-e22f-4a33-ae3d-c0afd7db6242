# ESim Cancel API Format - ESimBlue Integration

## Overview

Added ESim cancel API following the same pattern as redeem and usage APIs. The cancel API also returns HTTP 200 but uses different response body formats to indicate success or failure.

## API Format

### **Cancel Endpoint**

**Endpoint:** `POST /eip/partner/esim/cancel`

**Request Body:**
```json
{
    "iccid": "",
    "serial": "GX28D0X493"
}
```

### **Success Response Format**
```
true
```
(Simple boolean value)

### **Error Response Format**
```json
[
    {
        "name": "ESIM",
        "code": "NOT_ACCEPTABLE",
        "message": "eSIM is not in a refundable state."
    }
]
```

## Model Updates

### **EsimCancelRq (Request Model)**
```csharp
public class EsimCancelRq
{
    public string Iccid { get; set; } = string.Empty;
    public string Serial { get; set; } = string.Empty;
}
```

### **Response Handling**
- **Success**: Simple boolean `true`
- **Error**: Array of `EsimErrorResponse` objects (same as redeem/usage)

## Implementation

### **ESimBlueAPIClient.CancelEsimAsync**
```csharp
public async Task<ApiResult<bool>> CancelEsimAsync(string iccid, string serial)
{
    var endpoint = "/eip/partner/esim/cancel";
    
    try
    {
        var cancelRequest = new EsimCancelRq
        {
            Iccid = iccid,
            Serial = serial
        };

        var request = new HttpRequestMessage(HttpMethod.Post, endpoint);
        var requestData = JsonSerializer.Serialize(cancelRequest);
        request.Content = new StringContent(requestData, Encoding.UTF8, "application/json");

        var response = await _httpClient.SendAsync(request);
        var responseContent = await response.Content.ReadAsStringAsync();

        // API always returns 200 OK, but response format differs
        if (response.IsSuccessStatusCode)
        {
            var trimmedContent = responseContent.Trim();
            
            if (trimmedContent.StartsWith('['))
            {
                // Response is an error array
                var errorArray = JsonSerializer.Deserialize<List<EsimErrorResponse>>(responseContent);
                if (errorArray != null && errorArray.Count > 0)
                {
                    var errorMessage = string.Join("; ", errorArray.Select(e => $"{e.Code}: {e.Message}"));
                    return new ApiErrorResult<bool>($"Cancel failed: {errorMessage}");
                }
            }
            else if (trimmedContent.Equals("true", StringComparison.OrdinalIgnoreCase))
            {
                // Response is success (true)
                return new ApiSuccessResult<bool>(true);
            }
            else
            {
                // Try to parse as boolean
                if (bool.TryParse(trimmedContent, out bool result))
                {
                    return new ApiSuccessResult<bool>(result);
                }
            }
        }

        return new ApiErrorResult<bool>($"Unexpected response format: {response.StatusCode} - {responseContent}");
    }
    catch (Exception ex)
    {
        return new ApiErrorResult<bool>($"Cancel request failed: {ex.Message}");
    }
}
```

## Usage Examples

### **Frontend Integration**
```javascript
// Cancel ESim
async function cancelESim(serial, iccid = "") {
    try {
        const response = await fetch('/api/ESim/cancel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                serial: serial,
                iccid: iccid
            })
        });

        const result = await response.json();
        
        if (result.isSuccessed && result.resultObj === true) {
            console.log('ESim cancelled successfully');
            return true;
        } else {
            throw new Error(result.message || 'Cancel failed');
        }
    } catch (error) {
        console.error('Cancel error:', error);
        throw error;
    }
}
```

### **Backend Service Usage**
```csharp
// In ESimService
public async Task<ApiResult<bool>> CancelEsimAsync(string iccid, string serial)
{
    var stopwatch = Stopwatch.StartNew();
    var traceId = Guid.NewGuid().ToString("N");

    try
    {
        var logId = Guid.NewGuid();
        var requestData = JsonConvert.SerializeObject(new { iccid, serial });

        await _fileLogger.LogApiCallAsync(
            action: "cancel_esim",
            endpoint: "/eip/partner/esim/cancel",
            requestData: requestData,
            status: "pending",
            traceId: traceId,
            logId: logId
        );

        var result = await _eSimBlueClient.CancelEsimAsync(iccid, serial);
        stopwatch.Stop();

        await _fileLogger.UpdateLogAsync(
            logId: logId,
            responseData: JsonConvert.SerializeObject(result.ResultObj),
            status: result.IsSuccessed ? "success" : "error",
            duration: stopwatch.Elapsed
        );

        return result;
    }
    catch (Exception ex)
    {
        stopwatch.Stop();
        _logger.LogError(ex, "CancelEsim API call exception for serial: {Serial}, iccid: {Iccid}", serial, iccid);
        return new ApiErrorResult<bool>($"Cancel ESim failed: {ex.Message}");
    }
}
```

## Testing

### **Test Cancel API Format**
```bash
# Test the cancel API format
curl -X POST "https://localhost:7001/api/TestRedis/test-esim-cancel?serial=GX28D0X493&iccid="
```

### **Test Actual Cancel**
```bash
# Test actual cancel
curl -X POST "https://localhost:7001/api/ESim/cancel" \
  -H "Content-Type: application/json" \
  -d '{
    "serial": "GX28D0X493",
    "iccid": ""
  }'
```

### **Expected Test Results**

#### **Success Response:**
```json
{
  "isSuccessed": true,
  "message": "Cancel successful",
  "resultObj": true
}
```

#### **Error Response:**
```json
{
  "isSuccessed": false,
  "message": "Cancel failed: NOT_ACCEPTABLE: eSIM is not in a refundable state."
}
```

## Common Error Codes

### **Error Code Meanings**
- `NOT_ACCEPTABLE` - eSIM is not in a refundable state
- `RESOURCE_NOT_FOUND` - Serial or ICCID not found
- `ALREADY_CANCELLED` - eSIM has already been cancelled
- `INVALID_STATE` - eSIM cannot be cancelled in current state

### **Error Handling**
```csharp
if (!result.IsSuccessed)
{
    // Parse error details from response
    if (result.Message.Contains("NOT_ACCEPTABLE"))
    {
        return BadRequest("This eSIM cannot be cancelled as it's not in a refundable state");
    }
    else if (result.Message.Contains("RESOURCE_NOT_FOUND"))
    {
        return NotFound("eSIM serial or ICCID not found");
    }
    else if (result.Message.Contains("ALREADY_CANCELLED"))
    {
        return BadRequest("This eSIM has already been cancelled");
    }
    else
    {
        return BadRequest($"Cancel failed: {result.Message}");
    }
}
```

## API Pattern Consistency

### **All ESimBlue APIs Now Follow Same Pattern:**

| API | Success Response | Error Response | Pattern |
|-----|------------------|----------------|---------|
| **Redeem** | JSON Object `{}` | JSON Array `[]` | Always 200 ✅ |
| **Usage** | JSON Object `{}` | JSON Array `[]` | Always 200 ✅ |
| **Cancel** | Boolean `true` | JSON Array `[]` | Always 200 ✅ |

### **Detection Logic:**
```csharp
var trimmedContent = responseContent.Trim();

if (trimmedContent.StartsWith('['))
{
    // Error response: Parse as error array
}
else if (trimmedContent.StartsWith('{'))
{
    // Success object response (redeem, usage)
}
else if (trimmedContent.Equals("true", StringComparison.OrdinalIgnoreCase))
{
    // Success boolean response (cancel)
}
```

## Benefits

1. **Consistent Pattern**: Same error handling as redeem/usage APIs
2. **Simple Success Response**: Clear boolean result for cancel operations
3. **Detailed Error Information**: Specific error codes and messages
4. **Comprehensive Logging**: Full request/response logging
5. **Robust Error Handling**: Handles all response formats correctly

## Complete ESim Workflow

1. **Search Plans** → `GET /api/ESim/search`
2. **Create Order** → `POST /api/ESim/orders`
3. **Redeem ESim** → `POST /api/ESim/redeem/{serial}` ✅
4. **Check Usage** → `GET /api/ESim/usage/{serial}` ✅
5. **Cancel ESim** → `POST /api/ESim/cancel` ✅

All core ESim management APIs now properly handle the ESimBlue response patterns!
