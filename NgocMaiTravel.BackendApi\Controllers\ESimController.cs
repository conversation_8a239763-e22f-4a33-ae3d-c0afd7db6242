using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NgocMaiTravel.Application.Catalog.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using System;
using System.Threading.Tasks;

namespace NgocMaiTravel.BackendApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ESimController : ControllerBase
    {
        private readonly IESimService _eSimService;

        public ESimController(IESimService eSimService)
        {
            _eSimService = eSimService ?? throw new ArgumentNullException(nameof(eSimService));
        }

        /// <summary>
        /// ESim Balance
        /// </summary>
        /// <param name="rq"></param>
        /// <returns>Return list packages esim </returns>
        [HttpPost("search")]
        public async Task<IActionResult> SearchPlans([FromBody] EsimPackagesRq rq)
        {
            var result = await _eSimService.SearchPlansAsync(rq);
            return Ok(result);
        }

        /// <summary>
        /// Get ESim profile by serial number
        /// </summary>
        /// <param name="serial"></param>
        /// <returns>esim profile</returns>
        [HttpGet("profile")]
        public async Task<IActionResult> GetEsimProfile(string serial)
        {
            var result = await _eSimService.GetEsimProflieAsync(serial);
            return Ok(result);
        }

        ///// <summary>
        ///// Get available countries
        ///// </summary>
        ///// <returns>List of countries with ESim coverage</returns>
        //[HttpGet("countries")]
        //public async Task<IActionResult> GetCountries()
        //{
        //    var result = await _eSimService.GetCountriesAsync();
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Get plan details by ID
        ///// </summary>
        ///// <param name="planId">Plan ID</param>
        ///// <returns>Plan details</returns>
        //[HttpGet("plans/{planId}")]
        //public async Task<IActionResult> GetPlanDetails(string planId)
        //{
        //    var result = await _eSimService.GetPlanDetailsAsync(planId);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Create new ESim order
        ///// </summary>
        ///// <param name="request">Order details</param>
        ///// <returns>Created order information</returns>
        //[HttpPost("orders")]
        //public async Task<IActionResult> CreateOrder([FromBody] ESimOrderRequest request)
        //{
        //    var result = await _eSimService.CreateOrderAsync(request);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Get order by order ID
        ///// </summary>
        ///// <param name="orderId">Order ID</param>
        ///// <returns>Order details</returns>
        //[HttpGet("orders/{orderId}")]
        //public async Task<IActionResult> GetOrder(string orderId)
        //{
        //    var result = await _eSimService.GetOrderAsync(orderId);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Get order by internal ID
        ///// </summary>
        ///// <param name="id">Internal order ID</param>
        ///// <returns>Order details</returns>
        //[HttpGet("orders/internal/{id}")]
        //public async Task<IActionResult> GetOrderById(Guid id)
        //{
        //    var result = await _eSimService.GetOrderByIdAsync(id);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Cancel order
        ///// </summary>
        ///// <param name="orderId">Order ID to cancel</param>
        ///// <returns>Cancellation result</returns>
        //[HttpPost("orders/{orderId}/cancel")]
        //public async Task<IActionResult> CancelOrder(string orderId)
        //{
        //    var result = await _eSimService.CancelOrderAsync(orderId);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Get orders with filtering
        ///// </summary>
        ///// <param name="request">Filter criteria</param>
        ///// <returns>Filtered orders</returns>
        //[HttpPost("orders/search")]
        //public async Task<IActionResult> GetOrders([FromBody] ESimLogSearchRequest request)
        //{
        //    var result = await _eSimService.GetOrdersAsync(request);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Activate ESim
        ///// </summary>
        ///// <param name="request">Activation details</param>
        ///// <returns>Activation result</returns>
        //[HttpPost("activate")]
        //public async Task<IActionResult> ActivateESim([FromBody] ESimActivationRequest request)
        //{
        //    var result = await _eSimService.ActivateESimAsync(request);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Get activation status
        ///// </summary>
        ///// <param name="orderId">Order ID</param>
        ///// <returns>Activation status</returns>
        //[HttpGet("orders/{orderId}/activation")]
        //public async Task<IActionResult> GetActivationStatus(string orderId)
        //{
        //    var result = await _eSimService.GetActivationStatusAsync(orderId);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Create payment URL for order
        ///// </summary>
        ///// <param name="orderId">Order ID</param>
        ///// <param name="paymentMethod">Payment method</param>
        ///// <returns>Payment URL</returns>
        //[HttpPost("orders/{orderId}/payment")]
        //public async Task<IActionResult> CreatePaymentUrl(string orderId, [FromBody] string paymentMethod)
        //{
        //    var result = await _eSimService.CreatePaymentUrlAsync(orderId, paymentMethod);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Confirm payment for order
        ///// </summary>
        ///// <param name="orderId">Order ID</param>
        ///// <param name="transactionId">Transaction ID</param>
        ///// <returns>Confirmation result</returns>
        //[HttpPost("orders/{orderId}/payment/confirm")]
        //public async Task<IActionResult> ConfirmPayment(string orderId, [FromBody] string transactionId)
        //{
        //    var result = await _eSimService.ConfirmPaymentAsync(orderId, transactionId);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Handle payment callback
        ///// </summary>
        ///// <param name="orderId">Order ID</param>
        ///// <param name="status">Payment status</param>
        ///// <param name="transactionId">Transaction ID</param>
        ///// <returns>Callback handling result</returns>
        //[HttpPost("orders/{orderId}/payment/callback")]
        //public async Task<IActionResult> HandlePaymentCallback(string orderId, [FromQuery] string status, [FromQuery] string? transactionId = null)
        //{
        //    var result = await _eSimService.HandlePaymentCallbackAsync(orderId, status, transactionId);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Get ESim logs for admin
        ///// </summary>
        ///// <param name="request">Log search criteria</param>
        ///// <returns>ESim operation logs</returns>
        //[HttpPost("logs")]
        //[Authorize(Roles = "admin")]
        //public async Task<IActionResult> GetLogs([FromBody] ESimLogSearchRequest request)
        //{
        //    var result = await _eSimService.GetLogsAsync(request);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Get ESim statistics for admin
        ///// </summary>
        ///// <param name="fromDate">Start date</param>
        ///// <param name="toDate">End date</param>
        ///// <returns>ESim statistics</returns>
        //[HttpGet("stats")]
        //[Authorize(Roles = "admin")]
        //public async Task<IActionResult> GetStats([FromQuery] DateTime fromDate, [FromQuery] DateTime toDate)
        //{
        //    var result = await _eSimService.GetStatsAsync(fromDate, toDate);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Update order status (admin only)
        ///// </summary>
        ///// <param name="orderId">Order ID</param>
        ///// <param name="status">New status</param>
        ///// <returns>Update result</returns>
        //[HttpPut("orders/{orderId}/status")]
        //[Authorize(Roles = "admin")]
        //public async Task<IActionResult> UpdateOrderStatus(string orderId, [FromBody] string status)
        //{
        //    var result = await _eSimService.UpdateOrderStatusAsync(orderId, status);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Sync order status with ESimBlue
        ///// </summary>
        ///// <param name="orderId">Order ID</param>
        ///// <returns>Sync result</returns>
        //[HttpPost("orders/{orderId}/sync")]
        //public async Task<IActionResult> SyncOrderStatus(string orderId)
        //{
        //    var result = await _eSimService.SyncOrderStatusAsync(orderId);
        //    return Ok(result);
        //}

        ///// <summary>
        ///// Get expired orders for cleanup
        ///// </summary>
        ///// <returns>List of expired orders</returns>
        //[HttpGet("orders/expired")]
        //[Authorize(Roles = "admin")]
        //public async Task<IActionResult> GetExpiredOrders()
        //{
        //    var result = await _eSimService.GetExpiredOrdersAsync();
        //    return Ok(result);
        //}
    }
}
