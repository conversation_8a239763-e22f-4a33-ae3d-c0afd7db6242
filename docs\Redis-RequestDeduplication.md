# Redis-Based Request Deduplication Service

## Overview

The `RequestDeduplicationService` has been updated to use <PERSON><PERSON> as the caching backend instead of in-memory storage. This provides better scalability, persistence, and distributed caching capabilities.

## Key Features

### 🚀 **Redis Integration**
- **Distributed Caching**: Cache shared across multiple application instances
- **Persistence**: <PERSON><PERSON> survives application restarts
- **Automatic Expiration**: <PERSON><PERSON> handles TTL automatically
- **High Performance**: Optimized for concurrent access

### 🔄 **Request Deduplication**
- **Concurrent Request Handling**: Multiple identical requests return same cached result
- **Semaphore-based Locking**: Prevents duplicate execution (in-memory for performance)
- **Configurable Cache Duration**: Flexible TTL per request type

### 📊 **Monitoring & Statistics**
- **Performance Metrics**: Track cache hits, misses, and deduplication rates
- **Redis Statistics**: Monitor active cache entries
- **Comprehensive Logging**: Detailed operation logs

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   ESimService   │───▶│ RequestDedup     │───▶│     Redis       │
│                 │    │ Service          │    │                 │
│ - SearchPlans   │    │ - ExecuteAsync   │    │ - Cache Storage │
│ - <PERSON><PERSON><PERSON>rder   │    │ - ClearCache     │    │ - TTL Management│
│ - GetProfile    │    │ - GetStats       │    │ - Persistence   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Configuration

### Redis Connection (Program.cs)
```csharp
// Redis configuration
var redisConfiguration = builder.Configuration.GetSection("Redis:Configuration").Value;
var options = ConfigurationOptions.Parse(redisConfiguration);
options.AbortOnConnectFail = false;
options.ConnectTimeout = 15000;
options.SyncTimeout = 15000;
options.KeepAlive = 180;

// Register Redis services
builder.Services.AddSingleton<IConnectionMultiplexer>(ConnectionMultiplexer.Connect(options));
builder.Services.AddSingleton<IDatabase>(sp => sp.GetRequiredService<IConnectionMultiplexer>().GetDatabase());

// Register deduplication service
builder.Services.AddSingleton<IRequestDeduplicationService, RequestDeduplicationService>();
```

### appsettings.json
```json
{
  "Redis": {
    "Configuration": "localhost:6379",
    "InstanceName": "NgocMaiTravel"
  }
}
```

## Usage Examples

### Basic Request Deduplication
```csharp
public async Task<ApiResult<EsimPackagesRp>> SearchPlansAsync(EsimPackagesRq rq)
{
    var requestKey = ESimRequestKeyGenerator.GenerateSearchPlansKey(rq);
    
    var result = await _deduplicationService.ExecuteAsync(
        key: requestKey,
        factory: () => ExecuteSearchPlansInternalAsync(rq),
        cacheDurationSeconds: 30
    );
    
    return result;
}
```

### Custom Cache Duration
```csharp
// Short-term cache for frequently changing data
await _deduplicationService.ExecuteAsync(key, factory, cacheDurationSeconds: 10);

// Long-term cache for stable data
await _deduplicationService.ExecuteAsync(key, factory, cacheDurationSeconds: 300);
```

## Redis Key Structure

### Cache Keys
- **Pattern**: `esim:dedup:cache:{request-key}`
- **Example**: `esim:dedup:cache:search_plans_VN_data_1234567890`
- **TTL**: Configurable per request (default: 30 seconds)

### Statistics Keys
- **Pattern**: `esim:dedup:stats:current`
- **Content**: JSON with performance metrics
- **TTL**: 24 hours

## API Endpoints for Testing

### Test Deduplication
```http
GET /api/TestRedis/test-deduplication?key=test-key&delay=2000
```

### Test Concurrent Requests
```http
GET /api/TestRedis/test-concurrent?key=concurrent-test
```

### Get Cache Statistics
```http
GET /api/TestRedis/stats
```

### Clear Cache
```http
DELETE /api/TestRedis/clear/{key}
DELETE /api/TestRedis/clear-all
```

### Test Redis Connection
```http
GET /api/TestRedis/test-connection
```

## Performance Benefits

### Before (In-Memory)
- ❌ Cache lost on application restart
- ❌ No sharing between instances
- ❌ Memory usage grows with cache size
- ❌ Limited scalability

### After (Redis)
- ✅ Persistent cache across restarts
- ✅ Shared cache between instances
- ✅ Dedicated Redis memory management
- ✅ Horizontal scalability
- ✅ Built-in monitoring tools

## Monitoring

### Cache Statistics
```json
{
  "TotalRequests": 1250,
  "CacheHits": 875,
  "Deduplications": 125,
  "CacheHitRate": 70.0,
  "DeduplicationRate": 10.0,
  "ActiveSemaphores": 5,
  "RedisStats": {
    "ActiveCacheEntries": 45,
    "StoredStats": { ... }
  }
}
```

### Key Metrics
- **Cache Hit Rate**: Percentage of requests served from cache
- **Deduplication Rate**: Percentage of requests that were deduplicated
- **Active Cache Entries**: Number of keys currently in Redis
- **Active Semaphores**: Number of in-memory locks

## Error Handling

### Redis Connection Failures
- **Fallback**: Direct execution without caching
- **Logging**: Warning logged for Redis errors
- **Graceful Degradation**: Service continues to function

### Serialization Errors
- **JSON Serialization**: Newtonsoft.Json for reliable serialization
- **Type Safety**: Generic type preservation
- **Error Recovery**: Fallback to direct execution

## Best Practices

### 1. Key Generation
```csharp
// Use consistent, unique keys
var key = $"search_plans_{locationCode}_{type}_{timestamp}";
```

### 2. Cache Duration
```csharp
// Short for dynamic data
cacheDurationSeconds: 10

// Medium for semi-static data  
cacheDurationSeconds: 60

// Long for static data
cacheDurationSeconds: 300
```

### 3. Error Handling
```csharp
try
{
    return await _deduplicationService.ExecuteAsync(key, factory);
}
catch (RedisException ex)
{
    _logger.LogWarning(ex, "Redis error, falling back to direct execution");
    return await factory();
}
```

## Maintenance

### Cleanup Operations
```csharp
// Cleanup expired semaphores
_deduplicationService.CleanupExpiredSemaphores();

// Clear specific cache
_deduplicationService.ClearCache("specific-key");

// Clear all cache
_deduplicationService.ClearAllCache();
```

### Redis Maintenance
- **Memory Management**: Redis handles automatic expiration
- **Key Monitoring**: Use Redis CLI or monitoring tools
- **Performance Tuning**: Adjust connection pool settings

## Troubleshooting

### Common Issues

1. **Redis Connection Timeout**
   - Check Redis server status
   - Verify connection string
   - Increase timeout values

2. **High Memory Usage**
   - Monitor cache key count
   - Reduce cache duration
   - Implement cache size limits

3. **Serialization Errors**
   - Check object serializability
   - Verify JSON compatibility
   - Handle circular references

### Debugging Commands

```bash
# Connect to Redis CLI
redis-cli

# List all deduplication keys
KEYS esim:dedup:cache:*

# Get cache statistics
GET esim:dedup:stats:current

# Monitor Redis operations
MONITOR
```

## Migration Notes

### From In-Memory to Redis
1. **No Code Changes**: Existing ESimService code works unchanged
2. **Configuration Update**: Add Redis connection settings
3. **Dependency Injection**: Update service registration
4. **Testing**: Use TestRedisController to verify functionality

### Backward Compatibility
- All existing interfaces remain unchanged
- Same method signatures and behavior
- Transparent upgrade path
