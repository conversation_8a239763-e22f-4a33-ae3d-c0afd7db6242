﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace NgocMaiTravel.Data.Migrations
{
    /// <inheritdoc />
    public partial class addEsimTableCaching : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("92524a24-1d5d-44f4-a385-56065626dc8f"));

            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("c3f4ea5f-6961-4422-926a-9daee6a14a22"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedDate",
                table: "AppLockUserHistories",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 10, 9, 27, 44, 97, DateTimeKind.Local).AddTicks(8020),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 7, 7, 13, 39, 37, 433, DateTimeKind.Local).AddTicks(4442));

            migrationBuilder.CreateTable(
                name: "tblESimGroup",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false, comment: "Primary key")
                        .Annotation("SqlServer:Identity", "1, 1"),
                    GroupCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, comment: "Country/Region code like 'VN', 'JP', 'ASIA'"),
                    GroupName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false, comment: "Display name like 'Vietnam', 'Japan', 'Asia Region'"),
                    GroupType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "COUNTRY", comment: "Type: COUNTRY, REGION, GLOBAL"),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true, comment: "Optional description"),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true, comment: "Whether the group is active"),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0, comment: "Display order for sorting"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Creation timestamp"),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Last update timestamp"),
                    CreatedBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true, comment: "User who created the record"),
                    UpdatedBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true, comment: "User who last updated the record")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tblESimGroup", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "tblESimPackage",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false, comment: "Primary key")
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PackageId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "External package ID from API"),
                    Sku = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false, comment: "Package SKU"),
                    PackageName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false, comment: "Package display name"),
                    Description = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true, comment: "Package description"),
                    DataAmount = table.Column<long>(type: "bigint", nullable: false, comment: "Data amount in bytes"),
                    ValidityDays = table.Column<int>(type: "int", nullable: false, comment: "Validity period in days"),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: false, comment: "Package price"),
                    Currency = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, defaultValue: "USD", comment: "Price currency"),
                    OriginalPrice = table.Column<decimal>(type: "decimal(18,2)", nullable: true, comment: "Original price before discount"),
                    DiscountPercent = table.Column<decimal>(type: "decimal(5,2)", nullable: true, comment: "Discount percentage"),
                    PackageType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "DATA", comment: "Package type: DATA, VOICE, SMS, COMBO"),
                    NetworkType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true, comment: "Network type: 3G, 4G, 5G"),
                    IsUnlimited = table.Column<bool>(type: "bit", nullable: false, defaultValue: false, comment: "Whether data is unlimited"),
                    IsTopUpSupported = table.Column<bool>(type: "bit", nullable: false, defaultValue: false, comment: "Whether top-up is supported"),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true, comment: "Whether the package is active"),
                    ApiSource = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "ESimBlue", comment: "Source API provider"),
                    ExternalData = table.Column<string>(type: "NVARCHAR(MAX)", nullable: true, comment: "JSON data from external API"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Creation timestamp"),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Last update timestamp"),
                    LastSyncDate = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "Last sync from API timestamp"),
                    CreatedBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true, comment: "User who created the record"),
                    UpdatedBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true, comment: "User who last updated the record")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tblESimPackage", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "tblESimSyncLog",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false, comment: "Primary key")
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SyncType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "Sync type: FULL_SYNC, INCREMENTAL_SYNC, GROUP_SYNC"),
                    Status = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "Status: PENDING, RUNNING, SUCCESS, FAILED"),
                    StartTime = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Sync start timestamp"),
                    EndTime = table.Column<DateTime>(type: "datetime2", nullable: true, comment: "Sync end timestamp"),
                    Duration = table.Column<int>(type: "int", nullable: true, comment: "Duration in milliseconds"),
                    TotalPackages = table.Column<int>(type: "int", nullable: true, comment: "Total packages to process"),
                    ProcessedPackages = table.Column<int>(type: "int", nullable: true, comment: "Number of packages processed"),
                    NewPackages = table.Column<int>(type: "int", nullable: true, comment: "Number of new packages added"),
                    UpdatedPackages = table.Column<int>(type: "int", nullable: true, comment: "Number of packages updated"),
                    ErrorMessage = table.Column<string>(type: "NVARCHAR(MAX)", nullable: true, comment: "Error message if sync failed"),
                    RequestedBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true, comment: "User or system that requested the sync"),
                    ApiSource = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "ESimBlue", comment: "Source API provider")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tblESimSyncLog", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ESimGroupESimPackage",
                columns: table => new
                {
                    GroupsId = table.Column<long>(type: "bigint", nullable: false),
                    PackagesId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ESimGroupESimPackage", x => new { x.GroupsId, x.PackagesId });
                    table.ForeignKey(
                        name: "FK_ESimGroupESimPackage_tblESimGroup_GroupsId",
                        column: x => x.GroupsId,
                        principalTable: "tblESimGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ESimGroupESimPackage_tblESimPackage_PackagesId",
                        column: x => x.PackagesId,
                        principalTable: "tblESimPackage",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "tblESimGroupPackage",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false, comment: "Primary key")
                        .Annotation("SqlServer:Identity", "1, 1"),
                    GroupId = table.Column<long>(type: "bigint", nullable: false, comment: "Reference to ESimGroup"),
                    PackageId = table.Column<long>(type: "bigint", nullable: false, comment: "Reference to ESimPackage"),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false, defaultValue: 0, comment: "Display order within group"),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true, comment: "Whether the mapping is active"),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Creation timestamp"),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()", comment: "Last update timestamp"),
                    CreatedBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true, comment: "User who created the record"),
                    UpdatedBy = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true, comment: "User who last updated the record")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tblESimGroupPackage", x => x.Id);
                    table.ForeignKey(
                        name: "FK_tblESimGroupPackage_GroupId",
                        column: x => x.GroupId,
                        principalTable: "tblESimGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_tblESimGroupPackage_PackageId",
                        column: x => x.PackageId,
                        principalTable: "tblESimPackage",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "AppRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "Description", "Name", "NormalizedName", "TimeCreate", "UserCreateID" },
                values: new object[,]
                {
                    { new Guid("295e21cf-8bc0-4a98-930f-01f8aa5699ba"), null, "Client role", "client", "client", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null },
                    { new Guid("bf4167d8-946d-40b5-9cc0-bcabf3147a32"), null, "Employee role", "employee", "employee", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null }
                });

            migrationBuilder.UpdateData(
                table: "AppUsers",
                keyColumn: "Id",
                keyValue: new Guid("69bd714f-9576-45ba-b5b7-f00649be00de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "b26235dd-49e9-4a92-b5f9-60d3dda8d7b1", "AQAAAAIAAYagAAAAEHQv5BcwAa7v6ptaNT/sP+irMcCm4xXkeyhSW07IBG5+v6bhWTCtN0ordCRznnq+0w==" });

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab53"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 10, 9, 27, 44, 146, DateTimeKind.Local).AddTicks(4448));

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab54"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 10, 9, 27, 44, 146, DateTimeKind.Local).AddTicks(4485));

            migrationBuilder.CreateIndex(
                name: "IX_ESimGroupESimPackage_PackagesId",
                table: "ESimGroupESimPackage",
                column: "PackagesId");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimGroup_DisplayOrder",
                table: "tblESimGroup",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimGroup_GroupType",
                table: "tblESimGroup",
                column: "GroupType");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimGroup_IsActive",
                table: "tblESimGroup",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "UK_tblESimGroup_GroupCode",
                table: "tblESimGroup",
                column: "GroupCode",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_tblESimGroupPackage_DisplayOrder",
                table: "tblESimGroupPackage",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimGroupPackage_GroupId",
                table: "tblESimGroupPackage",
                column: "GroupId");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimGroupPackage_IsActive",
                table: "tblESimGroupPackage",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimGroupPackage_PackageId",
                table: "tblESimGroupPackage",
                column: "PackageId");

            migrationBuilder.CreateIndex(
                name: "UK_tblESimGroupPackage_GroupPackage",
                table: "tblESimGroupPackage",
                columns: new[] { "GroupId", "PackageId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_tblESimPackage_DataAmount",
                table: "tblESimPackage",
                column: "DataAmount");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimPackage_IsActive",
                table: "tblESimPackage",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimPackage_LastSyncDate",
                table: "tblESimPackage",
                column: "LastSyncDate");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimPackage_PackageType",
                table: "tblESimPackage",
                column: "PackageType");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimPackage_Price",
                table: "tblESimPackage",
                column: "Price");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimPackage_ValidityDays",
                table: "tblESimPackage",
                column: "ValidityDays");

            migrationBuilder.CreateIndex(
                name: "UK_tblESimPackage_PackageId",
                table: "tblESimPackage",
                column: "PackageId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "UK_tblESimPackage_Sku",
                table: "tblESimPackage",
                column: "Sku",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_tblESimSyncLog_StartTime",
                table: "tblESimSyncLog",
                column: "StartTime");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimSyncLog_StartTime_Status",
                table: "tblESimSyncLog",
                columns: new[] { "StartTime", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_tblESimSyncLog_Status",
                table: "tblESimSyncLog",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimSyncLog_SyncType",
                table: "tblESimSyncLog",
                column: "SyncType");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimSyncLog_SyncType_Status",
                table: "tblESimSyncLog",
                columns: new[] { "SyncType", "Status" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ESimGroupESimPackage");

            migrationBuilder.DropTable(
                name: "tblESimGroupPackage");

            migrationBuilder.DropTable(
                name: "tblESimSyncLog");

            migrationBuilder.DropTable(
                name: "tblESimGroup");

            migrationBuilder.DropTable(
                name: "tblESimPackage");

            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("295e21cf-8bc0-4a98-930f-01f8aa5699ba"));

            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("bf4167d8-946d-40b5-9cc0-bcabf3147a32"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedDate",
                table: "AppLockUserHistories",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 7, 13, 39, 37, 433, DateTimeKind.Local).AddTicks(4442),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 7, 10, 9, 27, 44, 97, DateTimeKind.Local).AddTicks(8020));

            migrationBuilder.InsertData(
                table: "AppRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "Description", "Name", "NormalizedName", "TimeCreate", "UserCreateID" },
                values: new object[,]
                {
                    { new Guid("92524a24-1d5d-44f4-a385-56065626dc8f"), null, "Client role", "client", "client", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null },
                    { new Guid("c3f4ea5f-6961-4422-926a-9daee6a14a22"), null, "Employee role", "employee", "employee", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null }
                });

            migrationBuilder.UpdateData(
                table: "AppUsers",
                keyColumn: "Id",
                keyValue: new Guid("69bd714f-9576-45ba-b5b7-f00649be00de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "5e0d8223-a1e4-4e91-857b-861468dcdfdc", "AQAAAAIAAYagAAAAENmDL7DRsrLNduhJeGpvC8ZPMSWnpdTEm94RpLbOSXglArD2lygFYy017qtHL6FoRw==" });

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab53"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 7, 13, 39, 37, 481, DateTimeKind.Local).AddTicks(4342));

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab54"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 7, 13, 39, 37, 481, DateTimeKind.Local).AddTicks(4373));
        }
    }
}
