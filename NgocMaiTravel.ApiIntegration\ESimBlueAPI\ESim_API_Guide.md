# ESim API Integration Guide

## 📋 Overview

This guide provides comprehensive documentation for the ESim API integration with ESimBlue provider. The system includes complete logging, order management, and payment integration.

## 🔄 ESim Order Flow

```mermaid
graph TD
    A[User searches ESim plans] --> B[Display available plans]
    B --> C[User selects plan]
    C --> D[Create order]
    D --> E{Payment Method?}
    E -->|Online| F[Create payment URL]
    E -->|Offline| G[Order pending payment]
    F --> H[User pays]
    G --> I[User pays later]
    H --> J{Payment Success?}
    I --> J
    J -->|Yes| K[Activate ESim]
    J -->|No| L[Order failed]
    K --> M[Send QR Code & Instructions]
    L --> N[Cancel order]
```

## 🚀 API Endpoints

### **1. Search ESim Plans**

**Endpoint:** `POST /api/ESim/search`

**Headers:**
```
X-Api-Key: your-api-key
Content-Type: application/json
```

**Request Body:**
```json
{
  "country": "Vietnam",
  "region": "Asia",
  "dataAmount": 10240,
  "validityDays": 30,
  "minPrice": 100000,
  "maxPrice": 1000000,
  "provider": "ESimBlue",
  "isUnlimited": false,
  "sortBy": "price",
  "sortOrder": "asc",
  "pageIndex": 1,
  "pageSize": 20
}
```

**Response:**
```json
{
  "isSuccessed": true,
  "message": "Success",
  "resultObj": {
    "items": [
      {
        "planId": "plan_001",
        "planName": "Vietnam 30 Days 10GB",
        "country": "Vietnam",
        "countryCode": "VN",
        "region": "Asia",
        "dataAmount": 10240,
        "isUnlimited": false,
        "validityDays": 30,
        "price": 500000,
        "currency": "VND",
        "provider": "ESimBlue",
        "description": "High-speed data plan for Vietnam",
        "features": ["4G/5G", "Hotspot", "No roaming charges"],
        "coverageArea": "Nationwide",
        "networkType": "4G/5G",
        "isAvailable": true
      }
    ],
    "totalRecords": 15,
    "pageIndex": 1,
    "pageSize": 20
  }
}
```

### **2. Get Available Countries**

**Endpoint:** `GET /api/ESim/countries`

**Headers:**
```
X-Api-Key: your-api-key
```

**Response:**
```json
{
  "isSuccessed": true,
  "message": "Success",
  "resultObj": [
    {
      "countryCode": "VN",
      "countryName": "Vietnam",
      "region": "Asia",
      "availablePlans": 15,
      "minPrice": 200000,
      "maxPrice": 1000000,
      "currency": "VND",
      "isPopular": true
    }
  ]
}
```

### **3. Get Plan Details**

**Endpoint:** `GET /api/ESim/plans/{planId}`

**Headers:**
```
X-Api-Key: your-api-key
```

**Response:**
```json
{
  "isSuccessed": true,
  "message": "Success",
  "resultObj": {
    "planId": "plan_001",
    "planName": "Vietnam 30 Days 10GB",
    "country": "Vietnam",
    "countryCode": "VN",
    "region": "Asia",
    "dataAmount": 10240,
    "isUnlimited": false,
    "validityDays": 30,
    "price": 500000,
    "currency": "VND",
    "provider": "ESimBlue",
    "description": "High-speed data plan for Vietnam with nationwide coverage",
    "features": ["4G/5G", "Hotspot", "No roaming charges", "Instant activation"],
    "coverageArea": "Nationwide including major cities and tourist areas",
    "networkType": "4G/5G",
    "isAvailable": true
  }
}
```

### **4. Create Order**

**Endpoint:** `POST /api/ESim/orders`

**Headers:**
```
X-Api-Key: your-api-key
Content-Type: application/json
```

**Request Body:**
```json
{
  "planId": "plan_001",
  "customerName": "Nguyen Van A",
  "customerEmail": "<EMAIL>",
  "customerPhone": "+***********",
  "notes": "Business trip to Vietnam",
  "paymentMethod": "online"
}
```

**Response:**
```json
{
  "isSuccessed": true,
  "message": "Order created successfully",
  "resultObj": {
    "orderId": "ESM_20241230_001",
    "planId": "plan_001",
    "planName": "Vietnam 30 Days 10GB",
    "customerName": "Nguyen Van A",
    "customerEmail": "<EMAIL>",
    "customerPhone": "+***********",
    "totalAmount": 500000,
    "currency": "VND",
    "status": "pending",
    "createdAt": "2024-12-30T10:00:00Z",
    "paymentUrl": "https://payment.gateway.com/pay/ESM_20241230_001",
    "planDetails": {
      "planId": "plan_001",
      "planName": "Vietnam 30 Days 10GB",
      "country": "Vietnam",
      "countryCode": "VN",
      "dataAmount": 10240,
      "validityDays": 30,
      "price": 500000
    }
  }
}
```

### **5. Get Order Status**

**Endpoint:** `GET /api/ESim/orders/{orderId}`

**Headers:**
```
X-Api-Key: your-api-key
```

**Response:**
```json
{
  "isSuccessed": true,
  "message": "Success",
  "resultObj": {
    "orderId": "ESM_20241230_001",
    "status": "activated",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "activationCode": "LPA:1$rsp.truphone.com$TRU-...",
    "activatedAt": "2024-12-30T11:00:00Z",
    "expiresAt": "2025-01-29T11:00:00Z",
    "instructions": "1. Scan QR code with your device\n2. Follow setup instructions\n3. Enable data roaming"
  }
}
```

### **6. Activate ESim**

**Endpoint:** `POST /api/ESim/activate`

**Headers:**
```
X-Api-Key: your-api-key
Content-Type: application/json
```

**Request Body:**
```json
{
  "orderId": "ESM_20241230_001",
  "deviceInfo": "iPhone 14 Pro - iOS 16.1",
  "activationCode": "LPA:1$rsp.truphone.com$TRU-..."
}
```

**Response:**
```json
{
  "isSuccessed": true,
  "message": "ESim activated successfully",
  "resultObj": {
    "orderId": "ESM_20241230_001",
    "status": "success",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "activationCode": "LPA:1$rsp.truphone.com$TRU-...",
    "instructions": "ESim has been activated. Data will be available within 5 minutes.",
    "activatedAt": "2024-12-30T11:00:00Z",
    "expiresAt": "2025-01-29T11:00:00Z"
  }
}
```

## 📊 Admin APIs

### **7. Get Logs**

**Endpoint:** `POST /api/ESim/logs`

**Headers:**
```
X-Api-Key: your-api-key
Authorization: Bearer admin-token
Content-Type: application/json
```

**Request Body:**
```json
{
  "orderId": "ESM_20241230_001",
  "customerEmail": "<EMAIL>",
  "action": "search",
  "status": "success",
  "fromDate": "2024-12-01T00:00:00Z",
  "toDate": "2024-12-31T23:59:59Z",
  "pageIndex": 1,
  "pageSize": 50
}
```

**Response:**
```json
{
  "isSuccessed": true,
  "message": "Success",
  "resultObj": {
    "items": [
      {
        "id": "log-uuid",
        "orderId": "ESM_20241230_001",
        "action": "search_plans",
        "customerEmail": "<EMAIL>",
        "requestData": "{\"country\":\"Vietnam\"}",
        "responseData": "{\"plans\":[...]}",
        "status": "success",
        "timestamp": "2024-12-30T10:00:00Z",
        "duration": "00:00:02.150",
        "apiEndpoint": "/api/v1/plans",
        "ipAddress": "*************"
      }
    ],
    "totalRecords": 100,
    "pageIndex": 1,
    "pageSize": 50
  }
}
```

### **8. Get Statistics**

**Endpoint:** `GET /api/ESim/stats?fromDate=2024-12-01&toDate=2024-12-31`

**Headers:**
```
X-Api-Key: your-api-key
Authorization: Bearer admin-token
```

**Response:**
```json
{
  "isSuccessed": true,
  "message": "Success",
  "resultObj": {
    "totalOrders": 150,
    "activeOrders": 120,
    "completedOrders": 100,
    "cancelledOrders": 30,
    "totalRevenue": 75000000,
    "currency": "VND",
    "fromDate": "2024-12-01T00:00:00Z",
    "toDate": "2024-12-31T23:59:59Z",
    "dailyStats": [
      {
        "date": "2024-12-30",
        "orders": 5,
        "revenue": 2500000,
        "activations": 4
      }
    ]
  }
}
```

## 🔧 Error Handling

### Common Error Responses:

**Plan Not Found:**
```json
{
  "isSuccessed": false,
  "message": "Plan not found: plan_invalid"
}
```

**Order Creation Failed:**
```json
{
  "isSuccessed": false,
  "message": "Failed to create order: Insufficient balance"
}
```

**Payment Failed:**
```json
{
  "isSuccessed": false,
  "message": "Payment failed: Transaction declined"
}
```

**Activation Failed:**
```json
{
  "isSuccessed": false,
  "message": "Activation failed: Device not compatible"
}
```

## 📝 Logging System

The system automatically logs all operations:

- **Search requests** - User search criteria and results
- **Order creation** - Order details and customer information  
- **Payment processing** - Payment status and transaction IDs
- **Activation attempts** - Device info and activation results
- **API calls** - All external API calls to ESimBlue
- **Errors** - Detailed error information for debugging

### Log Levels:
- **INFO**: Normal operations
- **WARN**: Non-critical issues
- **ERROR**: Failed operations
- **DEBUG**: Detailed debugging information

## 🔐 Security

- All APIs require valid `X-Api-Key` header
- Admin APIs require additional JWT authentication
- Sensitive data (payment info, activation codes) are encrypted
- Request/response logging excludes sensitive fields
- Rate limiting applied to prevent abuse

## 📱 Integration Examples

### Frontend JavaScript Example:
```javascript
// Search ESim plans
async function searchESimPlans(searchCriteria) {
    const response = await fetch('/api/ESim/search', {
        method: 'POST',
        headers: {
            'X-Api-Key': 'your-api-key',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(searchCriteria)
    });
    
    const result = await response.json();
    return result;
}

// Create order
async function createESimOrder(orderData) {
    const response = await fetch('/api/ESim/orders', {
        method: 'POST',
        headers: {
            'X-Api-Key': 'your-api-key',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
    });
    
    const result = await response.json();
    
    if (result.isSuccessed && result.resultObj.paymentUrl) {
        // Redirect to payment
        window.location.href = result.resultObj.paymentUrl;
    }
    
    return result;
}
```

This comprehensive ESim API system provides full integration with ESimBlue, complete logging for admin monitoring, and a seamless user experience from search to activation.
