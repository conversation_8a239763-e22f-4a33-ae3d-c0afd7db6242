﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI
{

    public partial class EsimProflieRp
    {
        [JsonProperty("orderPublicId")]
        public Guid OrderPublicId { get; set; }

        [JsonProperty("serial")]
        public string Serial { get; set; }

        [JsonProperty("status")]
        public long Status { get; set; }

        [JsonProperty("esim")]
        public Esim Esim { get; set; }
    }

    public partial class Esim
    {
        [JsonProperty("packageId")]
        public long PackageId { get; set; }

        [JsonProperty("sku")]
        public string Sku { get; set; }

        [JsonProperty("packageName")]
        public string PackageName { get; set; }

        [JsonProperty("model")]
        public string Model { get; set; }

        [JsonProperty("slug")]
        public string Slug { get; set; }

        [JsonProperty("volume")]
        public string Volume { get; set; }

        [JsonProperty("duration")]
        public long Duration { get; set; }

        [JsonProperty("durationUnit")]
        public string DurationUnit { get; set; }

        [JsonProperty("location")]
        public string Location { get; set; }

        [JsonProperty("activeType")]
        public long ActiveType { get; set; }

        [JsonProperty("supportTopUpType")]
        public long SupportTopUpType { get; set; }

        [JsonProperty("imsi")]
        public object Imsi { get; set; }

        [JsonProperty("iccid")]
        public string Iccid { get; set; }

        [JsonProperty("ac")]
        public string Ac { get; set; }

        [JsonProperty("qrCodeUrl")]
        public Uri QrCodeUrl { get; set; }

        [JsonProperty("shortUrl")]
        public Uri ShortUrl { get; set; }

        [JsonProperty("apn")]
        public string Apn { get; set; }
    }

    public partial class EsimProflieRp
    {
        public static EsimProflieRp FromJson(string json) => JsonConvert.DeserializeObject<EsimProflieRp>(json, EsimProflieRpConverter.Settings);
    }

    public static class EsimProflieRpSerialize
    {
        public static string ToJson(this EsimProflieRp self) => JsonConvert.SerializeObject(self, EsimProflieRpConverter.Settings);
    }

    internal static class EsimProflieRpConverter
    {
        public static readonly JsonSerializerSettings Settings = new JsonSerializerSettings
        {
            MetadataPropertyHandling = MetadataPropertyHandling.Ignore,
            DateParseHandling = DateParseHandling.None,
            Converters =
            {
                new IsoDateTimeConverter { DateTimeStyles = DateTimeStyles.AssumeUniversal }
            },
        };
    }
}

