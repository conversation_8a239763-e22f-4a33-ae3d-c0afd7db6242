using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NgocMaiTravel.Application.Catalog.ESim.Models;
using System.Collections.Concurrent;

namespace NgocMaiTravel.Application.Catalog.ESim.Services
{
    /// <summary>
    /// Background service to process ESim sync queue
    /// </summary>
    public class ESimSyncBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<ESimSyncBackgroundService> _logger;
        private readonly ESimSyncConfiguration _config;
        private readonly ConcurrentQueue<ESimSyncQueueItem> _syncQueue;
        private readonly SemaphoreSlim _semaphore;

        public ESimSyncBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<ESimSyncBackgroundService> logger,
            IOptions<ESimSyncConfiguration> config)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _config = config.Value;
            _syncQueue = new ConcurrentQueue<ESimSyncQueueItem>();
            _semaphore = new SemaphoreSlim(1, 1); // Only one sync at a time
        }

        /// <summary>
        /// Queue sync item
        /// </summary>
        public void QueueSync(ESimSyncQueueItem item)
        {
            if (_config.EnableQueueProcessing)
            {
                _syncQueue.Enqueue(item);
                _logger.LogInformation("Queued ESim sync: {SyncType} for {GroupCode}", item.SyncType, item.GroupCode ?? "ALL");
            }
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("ESim Sync Background Service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // Process queue items
                    await ProcessQueueAsync(stoppingToken);

                    // Periodic background sync
                    if (_config.EnableBackgroundSync)
                    {
                        await PeriodicSyncAsync(stoppingToken);
                    }

                    // Wait before next iteration
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in ESim sync background service");
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken); // Wait longer on error
                }
            }

            _logger.LogInformation("ESim Sync Background Service stopped");
        }

        private async Task ProcessQueueAsync(CancellationToken cancellationToken)
        {
            var processedItems = 0;
            var maxItemsPerBatch = 10;

            while (_syncQueue.TryDequeue(out var item) && processedItems < maxItemsPerBatch && !cancellationToken.IsCancellationRequested)
            {
                await _semaphore.WaitAsync(cancellationToken);
                try
                {
                    await ProcessSyncItemAsync(item, cancellationToken);
                    processedItems++;
                }
                finally
                {
                    _semaphore.Release();
                }
            }
        }

        private async Task ProcessSyncItemAsync(ESimSyncQueueItem item, CancellationToken cancellationToken)
        {
            using var scope = _serviceProvider.CreateScope();
            var syncService = scope.ServiceProvider.GetRequiredService<IESimSyncService>();

            try
            {
                _logger.LogInformation("Processing ESim sync: {SyncType} for {GroupCode}", item.SyncType, item.GroupCode ?? "ALL");

                switch (item.SyncType)
                {
                    case ESimSyncTypes.FullSync:
                        await syncService.ExecuteFullSyncAsync(item.RequestedBy);
                        break;

                    case ESimSyncTypes.IncrementalSync:
                        await syncService.ExecuteIncrementalSyncAsync(item.RequestedBy);
                        break;

                    case ESimSyncTypes.GroupSync:
                        if (!string.IsNullOrEmpty(item.GroupCode))
                        {
                            await syncService.ExecuteGroupSyncAsync(item.GroupCode, item.RequestedBy);
                        }
                        break;

                    case ESimSyncTypes.ManualSync:
                        await syncService.ExecuteFullSyncAsync(item.RequestedBy);
                        break;

                    case ESimSyncTypes.ScheduledSync:
                        await syncService.ExecuteIncrementalSyncAsync(item.RequestedBy);
                        break;

                    default:
                        _logger.LogWarning("Unknown sync type: {SyncType}", item.SyncType);
                        break;
                }

                _logger.LogInformation("Completed ESim sync: {SyncType} for {GroupCode}", item.SyncType, item.GroupCode ?? "ALL");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process ESim sync: {SyncType} for {GroupCode}", item.SyncType, item.GroupCode ?? "ALL");

                // Retry logic
                if (item.Priority < _config.MaxRetryAttempts)
                {
                    item.Priority++;
                    item.QueuedAt = DateTime.UtcNow.Add(_config.RetryDelay);
                    _syncQueue.Enqueue(item);
                    _logger.LogInformation("Requeued ESim sync for retry: {SyncType}, attempt {Attempt}", item.SyncType, item.Priority);
                }
            }
        }

        private async Task PeriodicSyncAsync(CancellationToken cancellationToken)
        {
            using var scope = _serviceProvider.CreateScope();
            var syncService = scope.ServiceProvider.GetRequiredService<IESimSyncService>();

            try
            {
                var syncNeeded = await syncService.IsSyncNeededAsync(_config.BackgroundSyncInterval);
                if (syncNeeded)
                {
                    _logger.LogInformation("Starting periodic ESim sync");
                    
                    // Queue incremental sync
                    QueueSync(new ESimSyncQueueItem
                    {
                        SyncType = ESimSyncTypes.IncrementalSync,
                        RequestedBy = "PeriodicSync",
                        Priority = 1
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in periodic sync check");
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping ESim Sync Background Service...");

            // Process remaining queue items with timeout
            var timeout = TimeSpan.FromMinutes(2);
            using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            timeoutCts.CancelAfter(timeout);

            try
            {
                while (_syncQueue.TryDequeue(out var item) && !timeoutCts.Token.IsCancellationRequested)
                {
                    await ProcessSyncItemAsync(item, timeoutCts.Token);
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Timeout while processing remaining sync items during shutdown");
            }

            await base.StopAsync(cancellationToken);
            _logger.LogInformation("ESim Sync Background Service stopped");
        }

        /// <summary>
        /// Get queue status
        /// </summary>
        public QueueStatus GetQueueStatus()
        {
            return new QueueStatus
            {
                QueueLength = _syncQueue.Count,
                IsProcessing = _semaphore.CurrentCount == 0,
                LastProcessedAt = DateTime.UtcNow // This would need to be tracked properly
            };
        }
    }

    /// <summary>
    /// Queue status information
    /// </summary>
    public class QueueStatus
    {
        public int QueueLength { get; set; }
        public bool IsProcessing { get; set; }
        public DateTime? LastProcessedAt { get; set; }
    }
}
