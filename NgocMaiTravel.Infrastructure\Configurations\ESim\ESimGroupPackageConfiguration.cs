using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NgocMaiTravel.Domain.Entities.ESim;

namespace NgocMaiTravel.Infrastructure.Configurations.ESim
{
    /// <summary>
    /// Entity configuration for ESimGroupPackage (Many-to-Many mapping)
    /// </summary>
    public class ESimGroupPackageConfiguration : IEntityTypeConfiguration<ESimGroupPackage>
    {
        public void Configure(EntityTypeBuilder<ESimGroupPackage> builder)
        {
            // Table name
            builder.ToTable("tblESimGroupPackage");

            // Primary key
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasComment("Primary key");

            // Foreign keys
            builder.Property(e => e.GroupId)
                .IsRequired()
                .HasComment("Reference to ESimGroup");

            builder.Property(e => e.PackageId)
                .IsRequired()
                .HasComment("Reference to ESimPackage");

            // Unique constraint for group-package combination
            builder.HasIndex(e => new { e.GroupId, e.PackageId })
                .IsUnique()
                .HasDatabaseName("UK_tblESimGroupPackage_GroupPackage");

            // Display order
            builder.Property(e => e.DisplayOrder)
                .IsRequired()
                .HasDefaultValue(0)
                .HasComment("Display order within group");

            // Status
            builder.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValue(true)
                .HasComment("Whether the mapping is active");

            // Audit fields
            builder.Property(e => e.CreatedDate)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Creation timestamp");

            builder.Property(e => e.UpdatedDate)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Last update timestamp");

            builder.Property(e => e.CreatedBy)
                .HasMaxLength(255)
                .HasComment("User who created the record");

            builder.Property(e => e.UpdatedBy)
                .HasMaxLength(255)
                .HasComment("User who last updated the record");

            // Indexes for performance
            builder.HasIndex(e => e.GroupId)
                .HasDatabaseName("IX_tblESimGroupPackage_GroupId");

            builder.HasIndex(e => e.PackageId)
                .HasDatabaseName("IX_tblESimGroupPackage_PackageId");

            builder.HasIndex(e => e.IsActive)
                .HasDatabaseName("IX_tblESimGroupPackage_IsActive");

            builder.HasIndex(e => e.DisplayOrder)
                .HasDatabaseName("IX_tblESimGroupPackage_DisplayOrder");

            // Foreign key relationships
            builder.HasOne(e => e.Group)
                .WithMany(g => g.GroupPackages)
                .HasForeignKey(e => e.GroupId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_tblESimGroupPackage_GroupId");

            builder.HasOne(e => e.Package)
                .WithMany(p => p.GroupPackages)
                .HasForeignKey(e => e.PackageId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_tblESimGroupPackage_PackageId");
        }
    }
}
