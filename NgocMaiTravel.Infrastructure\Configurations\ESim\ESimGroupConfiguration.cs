using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NgocMaiTravel.Domain.Entities.ESim;

namespace NgocMaiTravel.Infrastructure.Configurations.ESim
{
    /// <summary>
    /// Entity configuration for ESimGroup
    /// </summary>
    public class ESimGroupConfiguration : IEntityTypeConfiguration<ESimGroup>
    {
        public void Configure(EntityTypeBuilder<ESimGroup> builder)
        {
            // Table name
            builder.ToTable("tblESimGroup");

            // Primary key
            builder.HasKey(e => e.Id);
            builder.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasComment("Primary key");

            // GroupCode - unique identifier
            builder.Property(e => e.GroupCode)
                .IsRequired()
                .HasMaxLength(10)
                .HasComment("Country/Region code like 'VN', 'JP', 'ASIA'");

            builder.HasIndex(e => e.GroupCode)
                .IsUnique()
                .HasDatabaseName("UK_tblESimGroup_GroupCode");

            // GroupName
            builder.Property(e => e.GroupName)
                .IsRequired()
                .HasMaxLength(255)
                .HasComment("Display name like 'Vietnam', 'Japan', 'Asia Region'");

            // GroupType
            builder.Property(e => e.GroupType)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("COUNTRY")
                .HasComment("Type: COUNTRY, REGION, GLOBAL");

            // Description
            builder.Property(e => e.Description)
                .HasMaxLength(1000)
                .HasComment("Optional description");

            // IsActive
            builder.Property(e => e.IsActive)
                .IsRequired()
                .HasDefaultValue(true)
                .HasComment("Whether the group is active");

            // DisplayOrder
            builder.Property(e => e.DisplayOrder)
                .IsRequired()
                .HasDefaultValue(0)
                .HasComment("Display order for sorting");

            // Audit fields
            builder.Property(e => e.CreatedDate)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Creation timestamp");

            builder.Property(e => e.UpdatedDate)
                .IsRequired()
                .HasDefaultValueSql("GETUTCDATE()")
                .HasComment("Last update timestamp");

            builder.Property(e => e.CreatedBy)
                .HasMaxLength(255)
                .HasComment("User who created the record");

            builder.Property(e => e.UpdatedBy)
                .HasMaxLength(255)
                .HasComment("User who last updated the record");

            // Indexes
            builder.HasIndex(e => e.GroupType)
                .HasDatabaseName("IX_tblESimGroup_GroupType");

            builder.HasIndex(e => e.IsActive)
                .HasDatabaseName("IX_tblESimGroup_IsActive");

            builder.HasIndex(e => e.DisplayOrder)
                .HasDatabaseName("IX_tblESimGroup_DisplayOrder");

            // Navigation properties
            builder.HasMany(e => e.GroupPackages)
                .WithOne(gp => gp.Group)
                .HasForeignKey(gp => gp.GroupId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
