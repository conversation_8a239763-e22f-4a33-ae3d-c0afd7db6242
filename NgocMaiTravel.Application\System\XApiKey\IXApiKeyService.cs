﻿using Microsoft.AspNetCore.Http;
using NgocMaiTravel.ViewModels.Common;
using NgocMaiTravel.ViewModels.System.XApiKey;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.System.XApiKey
{
    public interface IXApiKeyService
    {
        Task<ApiResult<string>> CreateApiKey(CreateApiKeyRequest request);
        Task<ApiResult<PagedResult<PagedApiKeyModel>>> Paging(XapiKeyPagingRequest request);
        Task<ApiResult<bool>> ValiDate(HttpContext content);
        Task<ApiResult<bool>> ToggleApikey(ApiKeyToggleRequest request);
        Task<ApiResult<bool>> ToggleApikey(Guid id,ApiKeyToggleRequest request);
        Task<ApiResult<List<XapiKeyItem>>> GetItems();

        Task<ApiResult<PartnerEmailSetting>> GetPartnerEmailSetting();
        Task<ApiResult<PartnerEmailSetting>> GetPartnerEmailSetting(Guid id);

        Task<ApiResult<bool>> SavePartnerEmailSetting(PartnerEmailSetting request);
        Task<ApiResult<bool>> SavePartnerEmailSetting(PartnerEmailSetting request, Guid id);

        Task<ApiResult<ApiKeySetting>> GetApiSetting();
        Task<ApiResult<ApiKeySetting>> GetApiSetting(Guid id);
        Task<ApiResult<string>> UpdateApiKey(UpdateApiKeyRequest request);
        Task<ApiResult<string>> UpdateApiKey(Guid id, ApikeyUpdateSettingModelRq request);
        Task<bool> UpdateRequestHis(string Apikey, string path, string domain);
        Task<string> GetEmailReciveGDS(string? apikey);

    }
}
