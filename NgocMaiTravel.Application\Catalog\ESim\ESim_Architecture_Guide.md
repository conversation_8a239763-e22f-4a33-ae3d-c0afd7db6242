# ESim Architecture Guide - Separation of Concerns

## 📋 Overview

Hệ thống ESim đã được tái cấu trúc để tách biệt rõ ràng trách nhiệm:

- **ESimBlueAPIClient**: Chỉ gọi API bên thứ 3, không ghi log
- **ESimService**: Gọi ESimBlueAPIClient và ghi log tại đây
- **Request Deduplication**: Chỉ áp dụng cho SearchPlansAsync

## 🏗️ Architecture Layers

### **1. ESimBlueAPIClient (API Layer)**
```
┌─────────────────────────────────┐
│        ESimBlueAPIClient        │
├─────────────────────────────────┤
│ ✅ Call third-party APIs        │
│ ✅ Handle HTTP requests         │
│ ✅ Parse responses              │
│ ❌ NO logging                   │
│ ❌ NO business logic            │
└─────────────────────────────────┘
```

**Responsibilities:**
- Make HTTP calls to ESimBlue API
- Handle request/response serialization
- Return ApiResult<T> with success/error status
- Simple, focused, no dependencies

### **2. ESimService (Business Logic Layer)**
```
┌─────────────────────────────────┐
│           ESimService           │
├─────────────────────────────────┤
│ ✅ Business logic               │
│ ✅ File-based logging           │
│ ✅ Request deduplication        │
│ ✅ Error handling               │
│ ✅ Performance tracking         │
└─────────────────────────────────┘
```

**Responsibilities:**
- Call ESimBlueAPIClient methods
- Log requests/responses to files
- Handle request deduplication (SearchPlansAsync only)
- Business logic and validation
- Performance monitoring

## 🔄 Request Flow

### **Normal Flow:**
```
Controller → ESimService → ESimBlueAPIClient → Third-party API
                ↓
            File Logger
```

### **Deduplication Flow (SearchPlansAsync only):**
```
Multiple Requests → ESimService → Request Deduplication Service
                                        ↓
                                 Single Request → ESimBlueAPIClient → Third-party API
                                        ↓
                                 Shared Response ← File Logger
```

## 📊 Code Examples

### **ESimBlueAPIClient (Simple API calls):**
```csharp
public class ESimBlueAPIClient : IESimBlueAPIClient
{
    private readonly HttpClient _httpClient;

    public async Task<ApiResult<EsimPackagesRp>> GetAllDataPackagesAsync(EsimPackagesRq rq)
    {
        var endpoint = $"/eip/partner/esim/packages?{queryString}";
        return await CallApiAsync<EsimPackagesRp>(HttpMethod.Get, endpoint);
    }

    private async Task<ApiResult<TResponse>> CallApiAsync<TResponse>(
        HttpMethod method, string endpoint, object? body = null)
    {
        try
        {
            var request = new HttpRequestMessage(method, endpoint);
            if (body != null)
            {
                var requestData = JsonSerializer.Serialize(body);
                request.Content = new StringContent(requestData, Encoding.UTF8, "application/json");
            }

            var response = await _httpClient.SendAsync(request);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (response.IsSuccessStatusCode)
            {
                var result = JsonSerializer.Deserialize<TResponse>(responseContent);
                return new ApiSuccessResult<TResponse>(result);
            }

            return new ApiErrorResult<TResponse>($"API call failed: {response.StatusCode}");
        }
        catch (Exception ex)
        {
            return new ApiErrorResult<TResponse>($"API call exception: {ex.Message}");
        }
    }
}
```

### **ESimService (Business logic + Logging):**
```csharp
public class ESimService : IESimService
{
    private readonly IESimBlueAPIClient _eSimBlueClient;
    private readonly IESimFileLogger _fileLogger;
    private readonly IRequestDeduplicationService _deduplicationService;

    // SearchPlansAsync with deduplication
    public async Task<ApiResult<EsimPackagesRp>> SearchPlansAsync(EsimPackagesRq rq)
    {
        var requestKey = ESimRequestKeyGenerator.GenerateSearchPlansKey(rq);
        
        return await _deduplicationService.ExecuteAsync(
            key: requestKey,
            factory: () => ExecuteSearchPlansInternalAsync(rq),
            cacheDurationSeconds: 30
        );
    }

    private async Task<ApiResult<EsimPackagesRp>> ExecuteSearchPlansInternalAsync(EsimPackagesRq rq)
    {
        var stopwatch = Stopwatch.StartNew();
        var traceId = Guid.NewGuid().ToString("N");
        
        try
        {
            // Log request
            var logId = await _fileLogger.LogApiCallAsync(
                action: "search_plans",
                endpoint: "/eip/partner/esim/packages",
                requestData: JsonConvert.SerializeObject(rq),
                status: "pending",
                traceId: traceId
            );

            // Call API client
            var result = await _eSimBlueClient.GetAllDataPackagesAsync(rq);
            stopwatch.Stop();

            // Log response
            if (result.IsSuccessed)
            {
                await _fileLogger.UpdateLogAsync(
                    logId: logId,
                    responseData: JsonConvert.SerializeObject(result.ResultObj),
                    status: "success",
                    duration: stopwatch.Elapsed
                );
            }
            else
            {
                await _fileLogger.UpdateLogAsync(
                    logId: logId,
                    responseData: result.Message,
                    status: "error",
                    duration: stopwatch.Elapsed,
                    errorMessage: result.Message
                );
            }

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            await _fileLogger.LogApiCallAsync(
                action: "search_plans",
                endpoint: "/eip/partner/esim/packages",
                requestData: JsonConvert.SerializeObject(rq),
                status: "error",
                duration: stopwatch.Elapsed,
                errorMessage: ex.Message,
                traceId: traceId
            );
            
            return new ApiErrorResult<EsimPackagesRp>($"Search failed: {ex.Message}");
        }
    }

    // Other methods without deduplication
    public async Task<ApiResult<BalanceRp>> GetBalanceAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        var traceId = Guid.NewGuid().ToString("N");
        
        try
        {
            var logId = await _fileLogger.LogApiCallAsync(
                action: "get_balance",
                endpoint: "/eip/partner/company/balance",
                requestData: "{}",
                status: "pending",
                traceId: traceId
            );

            var result = await _eSimBlueClient.GetBalanceAsync();
            stopwatch.Stop();

            await _fileLogger.UpdateLogAsync(
                logId: logId,
                responseData: result.IsSuccessed ? JsonConvert.SerializeObject(result.ResultObj) : result.Message,
                status: result.IsSuccessed ? "success" : "error",
                duration: stopwatch.Elapsed,
                errorMessage: result.IsSuccessed ? null : result.Message
            );
            
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            await _fileLogger.LogApiCallAsync(
                action: "get_balance",
                endpoint: "/eip/partner/company/balance",
                status: "error",
                duration: stopwatch.Elapsed,
                errorMessage: ex.Message,
                traceId: traceId
            );
            
            return new ApiErrorResult<BalanceRp>($"Get balance failed: {ex.Message}");
        }
    }
}
```

## 🔧 Dependency Injection

### **Program.cs:**
```csharp
// ESim Services
builder.Services.AddTransient<IESimService, ESimService>();
builder.Services.AddTransient<IESimFileLogger, ESimFileLogger>();
builder.Services.AddSingleton<IRequestDeduplicationService, RequestDeduplicationService>();

// ESimBlue API Client - simplified without logging dependencies
builder.Services.AddHttpClient<IESimBlueAPIClient, ESimBlueAPIClient>();
```

## ✅ Benefits of This Architecture

### **1. Separation of Concerns:**
- **API Client**: Pure HTTP communication
- **Service Layer**: Business logic and logging
- **Clear boundaries** between layers

### **2. Testability:**
- **Easy to mock** ESimBlueAPIClient
- **Unit test** business logic separately
- **Integration test** API calls separately

### **3. Maintainability:**
- **Single responsibility** per class
- **Easy to modify** logging without affecting API calls
- **Clear error handling** at appropriate layers

### **4. Performance:**
- **Request deduplication** only where needed
- **Minimal overhead** in API client
- **Efficient logging** with file storage

## 🚨 Important Notes

### **1. Logging Location:**
- ❌ **NOT in ESimBlueAPIClient**
- ✅ **ONLY in ESimService**

### **2. Deduplication Scope:**
- ✅ **SearchPlansAsync only**
- ❌ **Not for other methods**

### **3. Error Handling:**
- **API Client**: Return ApiResult with error status
- **Service**: Log errors and add business context

### **4. Dependencies:**
- **ESimBlueAPIClient**: Minimal (HttpClient + Configuration)
- **ESimService**: Full (API Client + File Logger + Deduplication)

This architecture ensures clean separation of concerns while maintaining all the advanced features like logging and request deduplication where they're most appropriate.
