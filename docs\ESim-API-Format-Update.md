# ESim API Format Update - ESimBlue Integration

## Overview

Updated ESim order API to match the actual ESimBlue API specification based on the provided request/response format.

## API Format Changes

### **1. Order Creation Endpoint**

**Endpoint:** `POST /eip/partner/esim/orders`

#### **Request Format (Updated)**
```json
{
    "requestId": "0000001919314432",
    "packageInfoList": [
        {
            "sku": "BLC-01-JP-moshi-moshi-7days-1gb",
            "quantity": 1
        }
    ]
}
```

#### **Response Format (Updated)**
```json
{
    "orderPublicId": "167b2807-0bf4-45f1-85dd-3e2d186b4bab",
    "serialList": [
        {
            "serial": "TMU7TE2ZFI",
            "productId": 20,
            "status": 0,
            "esimPublicId": "5e9d40a2-3c6d-40f1-912f-dc89a356b633"
        }
    ]
}
```

### **2. Model Updates**

#### **EsimOrderRq (Request Model)**
```csharp
public class EsimOrderRq
{
    [Required]
    public string RequestId { get; set; } = string.Empty;

    [Required]
    public List<PackageInfo> PackageInfoList { get; set; } = [];

    // Additional fields for internal use (not sent to API)
    [EmailAddress]
    public string? CustomerEmail { get; set; }
    public string? CustomerPhone { get; set; }
    public string? CustomerName { get; set; }
    public string? Notes { get; set; }
}

public class PackageInfo
{
    [Required]
    public string Sku { get; set; } = string.Empty;

    [Required]
    [Range(1, 100)]
    public int Quantity { get; set; } = 1;
}
```

#### **EsimOrderRp (Response Model)**
```csharp
public class EsimOrderRp
{
    public string OrderPublicId { get; set; } = string.Empty;
    public List<SerialInfo> SerialList { get; set; } = [];
}

public class SerialInfo
{
    public string Serial { get; set; } = string.Empty;
    public int ProductId { get; set; }
    public int Status { get; set; } // 0 = Created, 1 = Activated, etc.
    public string EsimPublicId { get; set; } = string.Empty;
}
```

### **3. Status Codes**

#### **ESim Status Values**
- `0` = Created/Pending
- `1` = Activated
- `2` = In Use
- `3` = Suspended
- `4` = Expired
- `5` = Cancelled

## Implementation Changes

### **1. ESimBlueAPIClient Updates**
```csharp
public async Task<ApiResult<EsimOrderRp>> CreateOrderAsync(EsimOrderRq orderRequest)
{
    var endpoint = "/eip/partner/esim/orders"; // Updated endpoint
    return await CallApiAsync<EsimOrderRp>(HttpMethod.Post, endpoint, orderRequest);
}
```

### **2. ESimService Updates**
```csharp
public async Task<ApiResult<string>> CreateOrderAsync(string planId, string customerEmail, string customerPhone, string? orderId = null)
{
    var requestId = orderId ?? Guid.NewGuid().ToString("N");

    // Create order request in ESimBlue API format
    var orderRequest = new EsimOrderRq
    {
        RequestId = requestId,
        PackageInfoList = new List<PackageInfo>
        {
            new PackageInfo
            {
                Sku = planId, // Use planId as SKU
                Quantity = 1
            }
        },
        CustomerEmail = customerEmail,
        CustomerPhone = customerPhone
    };

    var apiResult = await _eSimBlueClient.CreateOrderAsync(orderRequest);
    
    if (apiResult.IsSuccessed && apiResult.ResultObj != null)
    {
        return new ApiSuccessResult<string>(apiResult.ResultObj.OrderPublicId);
    }
    
    return new ApiErrorResult<string>($"Order creation failed: {apiResult.Message}");
}
```

### **3. Controller Integration**
```csharp
[HttpPost("orders")]
public async Task<IActionResult> CreateOrder([FromBody] CreateESimOrderRequest request)
{
    var result = await _eSimService.CreateOrderAsync(
        request.PlanId,      // Maps to SKU
        request.CustomerEmail,
        request.CustomerPhone,
        request.OrderId      // Maps to RequestId
    );
    return Ok(result);
}
```

## Testing

### **1. Test API Format**
```bash
# Test the new API format
curl -X POST "https://localhost:7001/api/TestRedis/test-esim-order?sku=BLC-01-JP-moshi-moshi-7days-1gb&quantity=1"
```

### **2. Test Order Creation**
```bash
curl -X POST "https://localhost:7001/api/ESim/orders" \
  -H "Content-Type: application/json" \
  -d '{
    "planId": "BLC-01-JP-moshi-moshi-7days-1gb",
    "customerEmail": "<EMAIL>",
    "customerPhone": "+84123456789",
    "customerName": "Test Customer",
    "orderId": "test-order-001"
  }'
```

### **3. Expected Response**
```json
{
  "isSuccessed": true,
  "message": "Order created successfully",
  "resultObj": "167b2807-0bf4-45f1-85dd-3e2d186b4bab"
}
```

## Migration Notes

### **Breaking Changes**
1. **Request Format**: Changed from simple fields to structured `packageInfoList`
2. **Response Format**: Returns `orderPublicId` and `serialList` instead of simple order details
3. **Endpoint**: Updated from `/eip/partner/esim/order` to `/eip/partner/esim/orders`

### **Backward Compatibility**
- Controller interface remains the same (`CreateESimOrderRequest`)
- Service method signature unchanged
- Only internal API communication format updated

### **Data Mapping**
- `planId` → `packageInfoList[0].sku`
- `orderId` → `requestId`
- Response `orderPublicId` → returned as order ID
- `serialList[0].serial` → available for activation

## Benefits

### **1. Accurate API Integration**
- Matches actual ESimBlue API specification
- Proper request/response handling
- Correct data structures

### **2. Enhanced Functionality**
- Support for multiple packages in single order
- Proper serial management
- Status tracking with numeric codes

### **3. Better Error Handling**
- Structured response parsing
- Proper error propagation
- Detailed logging

## Next Steps

1. **Test Integration**: Verify with actual ESimBlue API
2. **Update Documentation**: Reflect new format in API docs
3. **Monitor Logs**: Check for proper request/response logging
4. **Validate Responses**: Ensure all fields are properly mapped

## Example Usage

### **Frontend Integration**
```javascript
// Create ESim order
const orderData = {
    planId: "BLC-01-JP-moshi-moshi-7days-1gb",
    customerEmail: "<EMAIL>",
    customerPhone: "+84123456789",
    customerName: "John Doe",
    orderId: "web-order-" + Date.now()
};

const response = await fetch('/api/ESim/orders', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(orderData)
});

const result = await response.json();
if (result.isSuccessed) {
    console.log('Order created:', result.resultObj); // orderPublicId
}
```

### **Backend Processing**
```csharp
// The service automatically converts to ESimBlue format:
// Input: planId, customerEmail, customerPhone
// Output: ESimBlue API request with packageInfoList
// Response: orderPublicId for tracking
```

This update ensures proper integration with the ESimBlue API while maintaining backward compatibility for existing client applications.
