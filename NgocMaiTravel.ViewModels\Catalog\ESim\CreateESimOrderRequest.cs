using System.ComponentModel.DataAnnotations;

namespace NgocMaiTravel.ViewModels.Catalog.ESim
{
    /// <summary>
    /// Request model for creating ESim order
    /// </summary>
    public class CreateESimOrderRequest
    {
        /// <summary>
        /// ESim plan ID to order
        /// </summary>
        [Required]
        [StringLength(100)]
        public string PlanId { get; set; } = string.Empty;

        /// <summary>
        /// Customer email address
        /// </summary>
        [Required]
        [EmailAddress]
        [StringLength(200)]
        public string CustomerEmail { get; set; } = string.Empty;

        /// <summary>
        /// Customer phone number
        /// </summary>
        [Required]
        [StringLength(20)]
        public string CustomerPhone { get; set; } = string.Empty;

        /// <summary>
        /// Optional order ID (will be generated if not provided)
        /// </summary>
        [StringLength(100)]
        public string? OrderId { get; set; }

        /// <summary>
        /// Customer name
        /// </summary>
        [StringLength(200)]
        public string? CustomerName { get; set; }

        /// <summary>
        /// Additional notes or requirements
        /// </summary>
        [StringLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// Payment method preference
        /// </summary>
        [StringLength(50)]
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// Source of the order (web, mobile, api, etc.)
        /// </summary>
        [StringLength(50)]
        public string? Source { get; set; }
    }
}
