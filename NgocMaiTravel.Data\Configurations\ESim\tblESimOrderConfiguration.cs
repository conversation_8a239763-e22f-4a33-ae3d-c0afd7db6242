using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NgocMaiTravel.Data.Entities.ESim;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Data.Configurations.ESim
{
    public class tblESimOrderConfiguration : IEntityTypeConfiguration<tblESimOrder>
    {
        public void Configure(EntityTypeBuilder<tblESimOrder> builder)
        {
            builder.ToTable("tblESimOrders");
            
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).HasDefaultValueSql("NEWID()");
            
            // Order Information
            builder.Property(x => x.OrderId).IsRequired().HasMaxLength(100);
            builder.Property(x => x.PlanId).IsRequired().HasMaxLength(100);
            builder.Property(x => x.PlanName).IsRequired().HasMaxLength(200);
            builder.Property(x => x.Country).IsRequired().HasMaxLength(100);
            builder.Property(x => x.CountryCode).IsRequired().HasMaxLength(10);
            builder.Property(x => x.Region).HasMaxLength(100);
            builder.Property(x => x.DataAmount).IsRequired();
            builder.Property(x => x.IsUnlimited).IsRequired().HasDefaultValue(false);
            builder.Property(x => x.ValidityDays).IsRequired();
            builder.Property(x => x.Price).HasColumnType("decimal(18,2)").IsRequired();
            builder.Property(x => x.Currency).HasMaxLength(10).HasDefaultValue("VND");
            builder.Property(x => x.Provider).HasMaxLength(100);
            
            // Customer Information
            builder.Property(x => x.CustomerName).IsRequired().HasMaxLength(200);
            builder.Property(x => x.CustomerEmail).IsRequired().HasMaxLength(200);
            builder.Property(x => x.CustomerPhone).IsRequired().HasMaxLength(20);
            
            // Status and Dates
            builder.Property(x => x.Status).IsRequired().HasMaxLength(50).HasDefaultValue("pending");
            builder.Property(x => x.CreatedAt).IsRequired().HasDefaultValueSql("GETDATE()");
            builder.Property(x => x.PaidAt).IsRequired(false);
            builder.Property(x => x.ActivatedAt).IsRequired(false);
            builder.Property(x => x.ExpiresAt).IsRequired(false);
            
            // Activation Details
            builder.Property(x => x.QrCode).HasColumnType("ntext").IsRequired(false);
            builder.Property(x => x.ActivationCode).HasMaxLength(500).IsRequired(false);
            builder.Property(x => x.Instructions).HasColumnType("ntext").IsRequired(false);
            
            // Payment Information
            builder.Property(x => x.PaymentMethod).IsRequired().HasMaxLength(50);
            builder.Property(x => x.PaymentTransactionId).HasMaxLength(200).IsRequired(false);
            builder.Property(x => x.PaymentUrl).HasMaxLength(1000).IsRequired(false);
            builder.Property(x => x.TotalAmount).HasColumnType("decimal(18,2)").IsRequired();
            
            // Additional Information
            builder.Property(x => x.Notes).HasColumnType("ntext").IsRequired(false);
            builder.Property(x => x.DeviceInfo).HasMaxLength(500).IsRequired(false);
            builder.Property(x => x.ErrorMessage).HasColumnType("ntext").IsRequired(false);
            
            // Audit Fields
            builder.Property(x => x.CreatedBy).IsRequired(false);
            builder.Property(x => x.UpdatedBy).IsRequired(false);
            builder.Property(x => x.UpdatedAt).IsRequired(false);
            builder.Property(x => x.OwnerID).IsRequired(false);
            
            // Indexes
            builder.HasIndex(x => x.OrderId).IsUnique();
            builder.HasIndex(x => x.CustomerEmail);
            builder.HasIndex(x => x.CustomerPhone);
            builder.HasIndex(x => x.Status);
            builder.HasIndex(x => x.CreatedAt);
            builder.HasIndex(x => x.OwnerID);
            builder.HasIndex(x => new { x.CustomerEmail, x.Status });
            builder.HasIndex(x => new { x.CreatedAt, x.Status });
        }
    }
}
