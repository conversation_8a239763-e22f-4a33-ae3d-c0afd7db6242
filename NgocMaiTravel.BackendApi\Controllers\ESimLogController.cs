using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NgocMaiTravel.Application.Common.Logging;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace NgocMaiTravel.BackendApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "admin")]
    public class ESimLogController : ControllerBase
    {
        private readonly NgocMaiTravelDbContext _context;
        private readonly IESimFileLogger _fileLogger;

        public ESimLogController(
            NgocMaiTravelDbContext context,
            IESimFileLogger fileLogger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _fileLogger = fileLogger ?? throw new ArgumentNullException(nameof(fileLogger));
        }

        /// <summary>
        /// Get ESim logs with filtering and pagination
        /// </summary>
        /// <param name="request">Log search criteria</param>
        /// <returns>Paginated ESim logs</returns>
        [HttpPost("search")]
        public async Task<IActionResult> GetLogs([FromBody] ESimLogSearchRequest request)
        {
            try
            {
                var query = _context.tblESimLogs.AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(request.OrderId))
                    query = query.Where(x => x.OrderId.Contains(request.OrderId));

                if (!string.IsNullOrEmpty(request.CustomerEmail))
                    query = query.Where(x => x.CustomerEmail.Contains(request.CustomerEmail));

                if (!string.IsNullOrEmpty(request.CustomerPhone))
                    query = query.Where(x => x.CustomerPhone.Contains(request.CustomerPhone));

                if (!string.IsNullOrEmpty(request.Action))
                    query = query.Where(x => x.Action.Contains(request.Action));

                if (!string.IsNullOrEmpty(request.Status))
                    query = query.Where(x => x.Status == request.Status);

                if (request.FromDate.HasValue)
                    query = query.Where(x => x.Timestamp >= request.FromDate.Value);

                if (request.ToDate.HasValue)
                    query = query.Where(x => x.Timestamp <= request.ToDate.Value);

                // Get total count
                var totalRecords = await query.CountAsync();

                // Apply sorting
                if (request.SortBy?.ToLower() == "action")
                    query = request.SortOrder?.ToLower() == "desc" ? query.OrderByDescending(x => x.Action) : query.OrderBy(x => x.Action);
                else if (request.SortBy?.ToLower() == "status")
                    query = request.SortOrder?.ToLower() == "desc" ? query.OrderByDescending(x => x.Status) : query.OrderBy(x => x.Status);
                else
                    query = request.SortOrder?.ToLower() == "desc" ? query.OrderByDescending(x => x.Timestamp) : query.OrderBy(x => x.Timestamp);

                // Apply pagination
                var logs = await query
                    .Skip((request.PageIndex - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .Select(x => new ESimLogVM
                    {
                        Id = x.Id,
                        OrderId = x.OrderId,
                        Action = x.Action,
                        CustomerEmail = x.CustomerEmail,
                        CustomerPhone = x.CustomerPhone,
                        RequestData = x.RequestData, // File path
                        ResponseData = x.ResponseData, // File path
                        Status = x.Status,
                        ErrorMessage = x.ErrorMessage,
                        Timestamp = x.Timestamp,
                        Duration = x.Duration,
                        IpAddress = x.IpAddress,
                        UserAgent = x.UserAgent,
                        ApiEndpoint = x.ApiEndpoint
                    })
                    .ToListAsync();

                var result = new PagedResult<ESimLogVM>
                {
                    Items = logs,
                    TotalRecords = totalRecords,
                    PageIndex = request.PageIndex,
                    PageSize = request.PageSize
                };

                return Ok(new ApiSuccessResult<PagedResult<ESimLogVM>>(result));
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiErrorResult<PagedResult<ESimLogVM>>($"Error retrieving logs: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get request data content from file
        /// </summary>
        /// <param name="logId">Log entry ID</param>
        /// <returns>Request data content</returns>
        [HttpGet("{logId}/request")]
        public async Task<IActionResult> GetRequestData(Guid logId)
        {
            try
            {
                var requestData = await _fileLogger.ReadRequestDataAsync(logId);
                if (requestData == null)
                {
                    return NotFound(new ApiErrorResult<string>("Request data not found"));
                }

                return Ok(new ApiSuccessResult<string>(requestData));
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiErrorResult<string>($"Error reading request data: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get response data content from file
        /// </summary>
        /// <param name="logId">Log entry ID</param>
        /// <returns>Response data content</returns>
        [HttpGet("{logId}/response")]
        public async Task<IActionResult> GetResponseData(Guid logId)
        {
            try
            {
                var responseData = await _fileLogger.ReadResponseDataAsync(logId);
                if (responseData == null)
                {
                    return NotFound(new ApiErrorResult<string>("Response data not found"));
                }

                return Ok(new ApiSuccessResult<string>(responseData));
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiErrorResult<string>($"Error reading response data: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get log statistics
        /// </summary>
        /// <param name="fromDate">Start date</param>
        /// <param name="toDate">End date</param>
        /// <returns>Log statistics</returns>
        [HttpGet("stats")]
        public async Task<IActionResult> GetLogStats([FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
        {
            try
            {
                var from = fromDate ?? DateTime.Today.AddDays(-30);
                var to = toDate ?? DateTime.Now;

                var query = _context.tblESimLogs.Where(x => x.Timestamp >= from && x.Timestamp <= to);

                var stats = new
                {
                    TotalRequests = await query.CountAsync(),
                    SuccessfulRequests = await query.CountAsync(x => x.Status == "success"),
                    FailedRequests = await query.CountAsync(x => x.Status == "error"),
                    PendingRequests = await query.CountAsync(x => x.Status == "pending"),
                    AverageResponseTime = await query.Where(x => x.Duration.HasValue)
                        .AverageAsync(x => x.Duration.Value.TotalMilliseconds),
                    TopActions = await query.GroupBy(x => x.Action)
                        .Select(g => new { Action = g.Key, Count = g.Count() })
                        .OrderByDescending(x => x.Count)
                        .Take(10)
                        .ToListAsync(),
                    ErrorsByAction = await query.Where(x => x.Status == "error")
                        .GroupBy(x => x.Action)
                        .Select(g => new { Action = g.Key, Count = g.Count() })
                        .OrderByDescending(x => x.Count)
                        .ToListAsync(),
                    RequestsByDay = await query.GroupBy(x => x.Timestamp.Date)
                        .Select(g => new { Date = g.Key, Count = g.Count() })
                        .OrderBy(x => x.Date)
                        .ToListAsync()
                };

                return Ok(new ApiSuccessResult<object>(stats));
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiErrorResult<object>($"Error retrieving stats: {ex.Message}"));
            }
        }

        /// <summary>
        /// Clean up old log files
        /// </summary>
        /// <param name="olderThanDays">Delete logs older than specified days</param>
        /// <returns>Cleanup result</returns>
        [HttpPost("cleanup")]
        public async Task<IActionResult> CleanupLogs([FromQuery] int olderThanDays = 30)
        {
            try
            {
                await _fileLogger.CleanupOldLogsAsync(olderThanDays);
                return Ok(new ApiSuccessResult<string>($"Successfully cleaned up logs older than {olderThanDays} days"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiErrorResult<string>($"Error during cleanup: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get log details by ID
        /// </summary>
        /// <param name="logId">Log entry ID</param>
        /// <returns>Detailed log information</returns>
        [HttpGet("{logId}")]
        public async Task<IActionResult> GetLogDetails(Guid logId)
        {
            try
            {
                var log = await _context.tblESimLogs.FirstOrDefaultAsync(x => x.Id == logId);
                if (log == null)
                {
                    return NotFound(new ApiErrorResult<ESimLogVM>("Log not found"));
                }

                var logVM = new ESimLogVM
                {
                    Id = log.Id,
                    OrderId = log.OrderId,
                    Action = log.Action,
                    CustomerEmail = log.CustomerEmail,
                    CustomerPhone = log.CustomerPhone,
                    RequestData = log.RequestData,
                    ResponseData = log.ResponseData,
                    Status = log.Status,
                    ErrorMessage = log.ErrorMessage,
                    Timestamp = log.Timestamp,
                    Duration = log.Duration,
                    IpAddress = log.IpAddress,
                    UserAgent = log.UserAgent,
                    ApiEndpoint = log.ApiEndpoint
                };

                return Ok(new ApiSuccessResult<ESimLogVM>(logVM));
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiErrorResult<ESimLogVM>($"Error retrieving log details: {ex.Message}"));
            }
        }
    }
}
