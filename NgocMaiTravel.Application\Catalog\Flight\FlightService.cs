﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NgocMaiTravel.ApiIntegration;
using NgocMaiTravel.ApiIntegration.Common.Logger;
using NgocMaiTravel.ApiIntegration.Email;
using NgocMaiTravel.ApiIntegration.NMBookingAPI;
using NgocMaiTravel.ApiIntegration.Payment.MoMo;
using NgocMaiTravel.ApiIntegration.Payment.VNPay;
using NgocMaiTravel.ApiIntegration.Queue;
using NgocMaiTravel.Application.Catalog.Flight.ValidatorBooking;
using NgocMaiTravel.Application.Catalog.Voucher;
using NgocMaiTravel.Application.System.AntiDoubleClick;
using NgocMaiTravel.Application.System.Origin;
using NgocMaiTravel.Application.System.XApiKey;
using NgocMaiTravel.Application.Utilities;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities;
using NgocMaiTravel.ViewModels.Catalog.Flight;
using NgocMaiTravel.ViewModels.Common;
using NgocMaiTravel.ViewModels.Utilities.Payment;
using System.Globalization;
using System.Security.Cryptography;

namespace NgocMaiTravel.Application.Catalog.Flight
{
    public class FlightService : IFlightService
    {
        private readonly NgocMaiTravelDbContext _context;
        private readonly INMBookingApiClient _nMBookingApiClient;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMoMoService _momoService;
        private readonly IVNPayService _vnPayService;
        private readonly IQueueService _emailQueue;
        private readonly ILoggerService _loggerService;
        private readonly IValidatorBookingService _validatorBookingService;
        private readonly IVoucherService _voucherService;
        private readonly IXApiKeyService _xApiKeyService;
        private readonly IOriginService _originService;

        public FlightService(NgocMaiTravelDbContext context,
            INMBookingApiClient nMBookingApiClient,
            IHttpContextAccessor httpContextAccessor,
            IMoMoService momoService,
            IVNPayService vnPayService,
            IQueueService emailQueue,
            ILoggerService loggerService,
            IValidatorBookingService validatorBookingService,
            IVoucherService voucherService,
            IXApiKeyService xApiKeyService,
            IOriginService originService)
        {
            _context = context;
            _nMBookingApiClient = nMBookingApiClient;
            _httpContextAccessor = httpContextAccessor;
            _momoService = momoService;
            _vnPayService = vnPayService;
            _emailQueue = emailQueue;
            _loggerService = loggerService;
            _validatorBookingService = validatorBookingService;
            _voucherService = voucherService;
            _xApiKeyService = xApiKeyService;
            _originService = originService;
        }



        public async Task<ApiResult<PagedResult<FlightRequestPagingVM>>> PagingRequest(FlightRequestPagingRequest request)
        {
            var query = from f in _context.FlightRequests
                        join u in _context.Users on f.UpdateBy equals u.Id into gj
                        from updater in gj.DefaultIfEmpty()
                        join kg in _context.tblApiKeyLibs on f.OwnerID equals kg.Id into kf
                        from owner in kf.DefaultIfEmpty()
                        select new
                        {
                            Flight = f,
                            UpdaterName = updater != null ? updater.FirstName + updater.LastName : null,
                            OwnerName = owner != null ? owner.Name : "NGỌC MAI TRAVEL"
                        };

            query = query.AsNoTracking();

            // Filter by date
            if (!string.IsNullOrEmpty(request.TypeDate))
            {
                request.FromDate = request.FromDate?.Date ?? DateTime.Today;
                request.ToDate = request.ToDate?.Date.AddDays(1).AddTicks(-1) ?? DateTime.Today.AddDays(1).AddTicks(-1);
                if (request.TypeDate == "BookingDate")
                {
                    query = query.Where(f => f.Flight.TimeCreate >= request.FromDate && f.Flight.TimeCreate <= request.ToDate);
                }
                else if (request.TypeDate == "DepartDate")
                {
                    query = query.Where(f => f.Flight.DepartDate >= request.FromDate && f.Flight.DepartDate <= request.ToDate);
                }
            }

            // Filter by agent
            if (string.IsNullOrEmpty(request.FilterAgent))
            {
                query = query.Where(f => f.Flight.OwnerID == null);
            }
            else if (!request.FilterAgent.Equals("all", StringComparison.OrdinalIgnoreCase))
            {
                if (Guid.TryParse(request.FilterAgent, out var ownerID))
                {
                    query = query.Where(f => f.Flight.OwnerID == ownerID);
                }
            }

            // Filter by code
            if (!string.IsNullOrEmpty(request.CodeKeyword))
                query = query.Where(f => f.Flight.Id.Contains(request.CodeKeyword) || f.Flight.Pnrs.Contains(request.CodeKeyword));


            // Filter by status
            if (request.FilterStatus.HasValue)
                query = query.Where(f => f.Flight.Status == request.FilterStatus);

            //// Filter by airline
            if (!string.IsNullOrEmpty(request.FilterAirline))
                query = query.Where(f => f.Flight.Airlines.Contains(request.FilterAirline));

            // Sorting
            if (!string.IsNullOrEmpty(request.SortColumn))
            {
                switch (request.SortColumn)
                {
                    case "OwnerName":
                        query = request.SortOrder.ToLower() == "desc"
                            ? query.OrderByDescending(x => x.OwnerName)
                            : query.OrderBy(x => x.OwnerName);
                        break;
                    case "UpdaterName":
                        query = request.SortOrder.ToLower() == "desc"
                            ? query.OrderByDescending(x => x.UpdaterName)
                            : query.OrderBy(x => x.UpdaterName);
                        break;
                    default:
                        query = request.SortOrder.ToLower() == "desc"
                            ? query.OrderByDescending(e => EF.Property<object>(e.Flight, request.SortColumn))
                            : query.OrderBy(e => EF.Property<object>(e.Flight, request.SortColumn));
                        break;
                }
            }
            else
            {
                query = query.OrderByDescending(x => x.Flight.TimeCreate); // Default sorting
            }

            var data = await query.ToListAsync();
            // Filter by customer
            if (!string.IsNullOrEmpty(request.CustomerKeyword))
            {
                var normalizedKeyword = SeoHelper.RemoveDiacritics(request.CustomerKeyword);

                data = data.Where(f =>
                    SeoHelper.RemoveDiacritics(f.Flight.CustomerName).Contains(normalizedKeyword) ||
                    f.Flight.PhoneNumber.Contains(request.CustomerKeyword) ||
                    f.Flight.Email.Contains(request.CustomerKeyword)).ToList();
            }


            var totalRecords = data.Count();
            var rawItems = data
                        .Skip((request.PageIndex - 1) * request.PageSize)
                        .Take(request.PageSize)
                        .ToList();

            var items = rawItems.Select(f =>
            {
                List<string> itineraryList = new();

                try
                {
                    if (!string.IsNullOrEmpty(f.Flight.Note))
                    {
                        var json = JsonConvert.DeserializeObject<JObject>(f.Flight.Note);
                        var summaries = json["summary"] as JArray;

                        if (summaries != null)
                        {
                            foreach (var summary in summaries)
                            {
                                var legs = summary["Legs"] as JArray;

                                if (legs != null)
                                {
                                    for (int i = 0; i < legs.Count; i++)
                                    {
                                        var leg = legs[i]?.ToString();
                                        if (string.IsNullOrWhiteSpace(leg)) continue;

                                        var parts = leg.Split('_');
                                        if (parts.Length < 6) continue;

                                        var flightNumber = parts[1]; // CA904
                                        var from = parts[2];         // SGN
                                        var to = parts[3];           // PEK
                                        var departureRaw = parts[4]; // 2025-05-31T05:10:00+07:00

                                        if (!DateTime.TryParse(departureRaw, out var departureDateTime))
                                            continue;

                                        var formatted = $"{flightNumber} {departureDateTime:HH:mm} {departureDateTime:dd/MM/yyyy} {from}-{to}";
                                        itineraryList.Add(formatted);
                                    }
                                }
                            }
                        }
                    }
                }
                catch
                {
                    // Log nếu cần
                }

                return new FlightRequestPagingVM
                {
                    Id = f.Flight.Id,
                    Pnrs = !string.IsNullOrEmpty(f.Flight.Pnrs) ? f.Flight.Pnrs : (!string.IsNullOrEmpty(f.Flight.Airlines) ? f.Flight.Airlines + ": Fail" : null),
                    CustomerName = f.Flight.CustomerName,
                    PhoneNumber = f.Flight.PhoneNumber,
                    Itinerary = itineraryList,
                    TotalPrice = f.Flight.TotalPrice,
                    TimeCreate = f.Flight.TimeCreate.ToString("dd/MM/yyyy HH:mm:ss"),
                    TimeCompletion = f.Flight.TimeCompletion?.ToString("dd/MM/yyyy HH:mm:ss"),
                    Status = f.Flight.Status,
                    TimeUpdate = f.Flight.TimeUpdate?.ToString("dd/MM/yyyy HH:mm:ss"),
                    UpdateBy = f.UpdaterName,
                    Notes = f.Flight.Note,
                    Agent = f.OwnerName,
                    UserNote = GetLatestNoteContent(f.Flight.UserNote)
                };
            }).ToList();



            var result = new PagedResult<FlightRequestPagingVM>
            {
                Items = items,
                PageIndex = request.PageIndex,
                PageSize = request.PageSize,
                TotalRecords = totalRecords,
                From = totalRecords > 0 ? ((request.PageIndex - 1) * request.PageSize) + 1 : 0,
                To = totalRecords > 0 ? Math.Min(request.PageIndex * request.PageSize, totalRecords) : 0
            };

            return new ApiSuccessResult<PagedResult<FlightRequestPagingVM>>(result);
        }
        private string GenerateCode()
        {
            string code;
            do
            {
                byte[] bytes = new byte[4]; // 4 byte = 32 bit, đủ để chứa số 0–4,294,967,295
                RandomNumberGenerator.Fill(bytes);
                int number = BitConverter.ToInt32(bytes, 0) & int.MaxValue; // Lấy giá trị dương
                int codeNumber = 10000000 + (number % 90000000); // Đảm bảo 8 chữ số
                code = codeNumber.ToString();
            } while (_context.FlightRequests.Any(x => x.Id == code));

            return code;
        }


        public async Task<ApiResult<bool>> SentRequest(FlightRequestVM request)
        {
            var flightRequest = new FlightRequest()
            {
                Id = GenerateCode(),
                Depart = request.Depart,
                Arrival = request.Arrival,
                DepartDate = request.DepartDate,
                ReturnDate = request.ReturnDate,
                Adult = request.Adult,
                Child = request.Child,
                Infant = request.Infant,
                CustomerName = request.CustomerName,
                PhoneNumber = request.PhoneNumber,
                Email = request.Email,
                Note = request.Note,
                PaymentMethod = request.PaymentMethod,
                Status = 0
            };
            _context.FlightRequests.Add(flightRequest);
            var result = await _context.SaveChangesAsync();
            if (result > 0)
            {
                return new ApiSuccessResult<bool>();
            }
            return new ApiErrorResult<bool>("Sent request failed");
        }

        public async Task<ApiResult<FlightRequestDetails>> GetRequestByID(string id)
        {
            var userName = _httpContextAccessor.HttpContext?.User.Identity?.Name;
            var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == userName);
            if (user == null)
                return new ApiErrorResult<FlightRequestDetails>("User not found");

            var xApiKeyID = await (
                from u in _context.Users
                join g in _context.GroupUsers on u.GroupUserId equals g.Id
                join k in _context.tblApiKeyLibs on g.XApiKeyID equals k.Id
                where u.Id == user.Id
                select k.Id
            ).FirstOrDefaultAsync();

            if (xApiKeyID == Guid.Empty)
                return new ApiErrorResult<FlightRequestDetails>("XApiKey not found");

            var flightRequest = await _context.FlightRequests.Where(x => x.OwnerID == xApiKeyID && x.Id == id).FirstOrDefaultAsync();
            if (flightRequest == null)
            {
                return new ApiErrorResult<FlightRequestDetails>("Request not found");
            }

            // Nếu đã có người thao tác khác người hiện tại, không cho phép truy cập
            if (flightRequest.UpdateBy != null && flightRequest.UpdateBy != user.Id && flightRequest.Status == 2)
            {
                return new ApiErrorResult<FlightRequestDetails>(409, "Request is being processed by another user");
            }

            // Nếu chưa ai thao tác hoặc chính người hiện tại đang thao tác
            if (flightRequest.UpdateBy == null && flightRequest.Status == 0)
            {
                flightRequest.UpdateBy = user.Id;
                flightRequest.TimeUpdate = DateTime.Now;
                flightRequest.Status = 2;
                _context.FlightRequests.Update(flightRequest);
                await _context.SaveChangesAsync(); // lưu UpdateBy
            }

            var flightRequestDetails = new FlightRequestDetails()
            {
                Id = flightRequest.Id,
                Depart = flightRequest.Depart,
                Arrival = flightRequest.Arrival,
                DepartDate = flightRequest.DepartDate,
                ReturnDate = flightRequest.ReturnDate,
                Adult = flightRequest.Adult,
                Child = flightRequest.Child,
                Infant = flightRequest.Infant,
                CustomerName = flightRequest.CustomerName,
                PhoneNumber = flightRequest.PhoneNumber,
                Email = flightRequest.Email,
                Note = flightRequest.Note,
                Status = flightRequest.Status,
                TimeCreate = flightRequest.TimeCreate,
                TimeCompletion = flightRequest.TimeCompletion,
                PaymentMethod = flightRequest.PaymentMethod,
                Pnrs = flightRequest.Pnrs,
                Airlines = flightRequest.Airlines,
                NoteUser = flightRequest.UserNote
            };

            return new ApiSuccessResult<FlightRequestDetails>(flightRequestDetails);

        }

        public async Task<ApiResult<bool>> UpdateStatus(string Id, int status)
        {
            var userName = _httpContextAccessor.HttpContext?.User.Identity?.Name;
            var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == userName);
            if (user == null)
                return new ApiErrorResult<bool>("User not found");

            var flightRequest = await _context.FlightRequests.FindAsync(Id);
            if (flightRequest == null)
            {
                return new ApiErrorResult<bool>("Request not found");
            }

            flightRequest.Status = status;
            flightRequest.TimeCompletion = DateTime.Now;
            flightRequest.TimeUpdate = DateTime.Now;
            flightRequest.UpdateBy = user.Id;

            _context.FlightRequests.Update(flightRequest);
            var result = await _context.SaveChangesAsync();
            if (result > 0)
            {
                return new ApiSuccessResult<bool>();
            }
            return new ApiErrorResult<bool>("Update status failed");
        }

        public async Task<ApiResult<TripAvailableRequest>> RequestTrip(FlightRequestVM request)
        {
            var logId = Guid.NewGuid().ToString();
            _loggerService.LogRequest(_httpContextAccessor.HttpContext!, JsonConvert.SerializeObject(request), logId, "", false);

            //check passenger double in trip
            var checkDoublePassenger = await _validatorBookingService.CheckBookingDuplicate(request);

            if (checkDoublePassenger.IsSuccessed == false)
            {
                var resultErr = (ApiErrorResult<bool>)checkDoublePassenger;
                var errorMessage = string.Join("; ", resultErr.ValidationErrors);
                return new ApiErrorResult<TripAvailableRequest>($"Khách hàng: {errorMessage} đã thực hiện giữ vé ở hành trình này.");
            }

            var refererClient = _httpContextAccessor.HttpContext.Request.Headers["Referer"].ToString();
            var originClient = _httpContextAccessor.HttpContext.Request.Headers["Origin"].ToString();

            Guid ownerID = Guid.Empty;

            //hold trip
            if (!string.IsNullOrEmpty(request.Note))
            {
                var orderCode = GenerateCode();
                var dataCart = JObject.Parse(request.Note);
                var BookTtripJson = dataCart["request"]?.ToString();
                var amount = dataCart["totalPrice"].Value<long>();
                var requestBookTrip = string.IsNullOrEmpty(BookTtripJson) ? null : JsonConvert.DeserializeObject<List<RqBookTripModel>>(BookTtripJson);
                var listResultBookTrip = new List<ApiResult<RpBookRetrieveModel>>();
                var isHoldTrip =  false;

                // Handle voucher if provided
                if (!string.IsNullOrEmpty(request.VoucherCode))
                {
                    // Determine if this is online payment
                    bool isOnlinePayment = !string.IsNullOrEmpty(request.PaymentMethod) &&
                                         (request.PaymentMethod.Contains("e-wallet") ||
                                          request.PaymentMethod.Contains("vnpay") ||
                                          request.PaymentMethod.Contains("momo"));

                    if (isOnlinePayment)
                    {
                        var voucherResult = await _voucherService.CheckAndReserveVoucherAsync(request.VoucherCode, amount, orderCode);

                        if (!voucherResult.IsSuccessed)
                        {
                            _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Voucher reservation failed: {voucherResult.Message}", logId, "", false);
                            return new ApiErrorResult<TripAvailableRequest>($"Voucher error: {voucherResult.Message}");
                        }

                        _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Voucher {request.VoucherCode} reserved for online payment. Discount: {voucherResult.ResultObj.discount}", logId, "", false);
                    }
                }

                //check header have X-Api-Key
                var apiKey = _httpContextAccessor.HttpContext.Request.Headers["X-Api-Key"].ToString();
                if (!string.IsNullOrEmpty(apiKey))
                {
                    ownerID = await _context.tblApiKeyLibs.Where(t => t.XApiKey == apiKey).Select(t => t.Id).FirstOrDefaultAsync();
                    isHoldTrip = await _context.tblFeatures.Where(x => x.OwnerID == ownerID && x.FeatureName == "HoldTrip").Select(x => x.IsUnlocked).FirstOrDefaultAsync();
                }
                else
                {
                    isHoldTrip = await _context.tblFeatures.Where(t => t.FeatureName == "HoldTrip" && (t.OwnerID == null || t.OwnerID == Guid.Empty)).Select(t => t.IsUnlocked).FirstOrDefaultAsync() || false; 
                }
                //isHoldTrip = true;

                var countBookTrip = -1;
                if (requestBookTrip != null && isHoldTrip)
                {
                    try
                    {
                        var emailReceiveGDS = await _xApiKeyService.GetEmailReciveGDS(apiKey);
                        if (!string.IsNullOrEmpty(emailReceiveGDS))
                        {
                            _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Email receive GDS: {emailReceiveGDS}", logId, "", false);
                            requestBookTrip.ForEach(x => x.Email = emailReceiveGDS);
                        }
                        var bookTripTasks = requestBookTrip.Select(item => _nMBookingApiClient.BookTrip(item)).ToList();
                        countBookTrip = bookTripTasks.Count();

                        while (bookTripTasks.Any())
                        {
                            var completedTask = await Task.WhenAny(bookTripTasks);
                            bookTripTasks.Remove(completedTask);

                            var response = await completedTask;
                            listResultBookTrip.Add(response);
                        }
                    }
                    catch (Exception ex)
                    {
                        _loggerService.LogResponse(_httpContextAccessor.HttpContext, "Order trip failed: " + ex.Message, logId, "" , false);
                    }
                }
                if(countBookTrip > 0)
                {
                    _loggerService.LogResponse(_httpContextAccessor.HttpContext, JsonConvert.SerializeObject(listResultBookTrip), logId, "", false);
                }
                var bookTripSuccess = listResultBookTrip.Where(x => x.IsSuccessed).Count();

                //save request
                var resultSaveRq = new ApiResult<TripAvailableRequest>();

                request.Pnrs = string.Join(",", listResultBookTrip.Where(x => x.IsSuccessed == true && x?.ResultObj?.ListRetrieve != null).SelectMany(x => x.ResultObj.ListRetrieve.Select(t => t.Pnr)));
                resultSaveRq = await SaveRequest(request, amount, ownerID, orderCode);
                if (!resultSaveRq.IsSuccessed)
                {
                    _loggerService.LogResponse(_httpContextAccessor.HttpContext, "save request Order trip failed: "+ resultSaveRq.Message, logId, "", false);
                    return new ApiErrorResult<TripAvailableRequest>("save request Order trip failed");
                }

                
                if (bookTripSuccess == countBookTrip || !isHoldTrip)
                {              
                    if(bookTripSuccess == countBookTrip)
                    {
                        _emailQueue.Enqueue(async provider =>
                        {
                            var validatorBookingServiceTemp = provider.GetRequiredService<IValidatorBookingService>();
                            await validatorBookingServiceTemp.SaveFlightPassengerHold(request, listResultBookTrip);
                        });
                    }
                    //get payment url
                    if (request.PaymentMethod.Contains("e-wallet"))
                    {
                        var paymentRq = new PaymentCreateModelRq()
                        {
                            orderId = resultSaveRq.Message,
                            amount = amount,
                            redirectUrl = (string.IsNullOrEmpty(refererClient) ? originClient : refererClient) + $"/TripResult?OrderCode={resultSaveRq.ResultObj.OrderCode}&PhoneCustomer={resultSaveRq.ResultObj.PhoneCustomer}&EmailCustomer={resultSaveRq.ResultObj.EmailCustomer}",
                            orderInfo = $"Thanh toan don hang: {resultSaveRq.ResultObj.OrderCode}"
                        };
                        if (request.PaymentMethod.ToLower().Contains("momo"))
                        {
                            paymentRq.ipnUrl = "https://api.ngocmaitravel.vn/api/Payments/Momo/Callback";
                            var resultMomo = await _momoService.CreatePayment(paymentRq);


                            _loggerService.LogResponse(_httpContextAccessor.HttpContext, JsonConvert.SerializeObject(resultMomo), logId, "", false);
                            return new ApiSuccessResult<TripAvailableRequest>()
                            {
                                Message = resultMomo.ResultObj.payUrl,
                                ResultObj = resultSaveRq.ResultObj
                            };
                        }
                        else if (request.PaymentMethod.ToLower().Contains("vnpay"))
                        {
                            //đã cấu hình trên VnPay
                            //paymentRq.ipnUrl = domain + $"/api/Payments/VNPay/Callback"; 

                            paymentRq.PaymentMethod = request.PaymentMethod.Replace("e-walletVNPay", "");
                            var paymentUrl = _vnPayService.CreatePayment(paymentRq).Result;

                            _loggerService.LogResponse(_httpContextAccessor.HttpContext, JsonConvert.SerializeObject(paymentUrl), logId, "", false);
                            return new ApiSuccessResult<TripAvailableRequest>()
                            {
                                Message = paymentUrl.ResultObj
                            };
                        }
                    }
                    else
                    {
                        _emailQueue.Enqueue(async provider =>
                        {
                            var emailService = provider.GetRequiredService<IEmailService>();
                            await emailService.SentEmailBookTrip(resultSaveRq.ResultObj.OrderCode, false);
                            await emailService.SentEmailBookTrip(resultSaveRq.ResultObj.OrderCode, true);
                        });
                        _loggerService.LogResponse(_httpContextAccessor.HttpContext, JsonConvert.SerializeObject(resultSaveRq), logId, "", false);
                        return new ApiSuccessResult<TripAvailableRequest>()
                        {
                            Message = $"/TripResult?OrderCode={resultSaveRq.ResultObj.OrderCode}&PhoneCustomer={resultSaveRq.ResultObj.PhoneCustomer}&EmailCustomer={resultSaveRq.ResultObj.EmailCustomer}",
                        };
                    }
                }
                else
                {
                    var messErr = string.Join(",",
                            listResultBookTrip
                                .Where(x => x?.ResultObj?.ListRetrieve != null) // Kiểm tra null
                                .SelectMany(x => x.ResultObj.ListRetrieve)
                                .Where(t => t?.StatusMessage != null) // Kiểm tra null
                                .SelectMany(t => t.StatusMessage)
                                .Where(msg => !string.IsNullOrEmpty(msg?.Message)) // Kiểm tra null và rỗng
                                .Select(msg => msg.Message) // Lấy thuộc tính Message
                        );

                    return new ApiErrorResult<TripAvailableRequest>(messErr);
                }
            }

            _loggerService.LogResponse(_httpContextAccessor.HttpContext, "Order trip failed", logId, "" , false);
            return new ApiErrorResult<TripAvailableRequest>("Order trip failed");
        }

        private async Task<ApiResult<TripAvailableRequest>> SaveRequest(FlightRequestVM request,long amount, Guid ownerID, string orderCode)
        {
            //var orderCode = GenerateCode();
            var airlines = "";
            if (!string.IsNullOrEmpty(request.Note))
            {
                try
                {
                    var json = JObject.Parse(request.Note);
                    var sessionPrefixes = json["request"]
                        .Select(r => r["SessionID"]?.ToString()?.Substring(0, 2))
                        .Where(prefix => !string.IsNullOrEmpty(prefix))
                        .Distinct()
                        .ToList();

                    airlines = string.Join(";", sessionPrefixes);
                }catch(Exception ex) { }
                
            }

            var flightRequest = new FlightRequest()
            {
                Id = orderCode,
                Depart = request.Depart,
                Arrival = request.Arrival,
                DepartDate = request.DepartDate,
                ReturnDate = request.ReturnDate,
                Adult = request.Adult,
                Child = request.Child,
                Infant = request.Infant,
                CustomerName = request.CustomerName,
                PhoneNumber = request.PhoneNumber,
                Email = request.Email,
                Note = request.Note,
                PaymentMethod = request.PaymentMethod,
                Pnrs = request.Pnrs,
                TicketNumbers = request.TicketNumbers,
                Status = 0,
                TransactionId = Guid.NewGuid().ToString("N"),
                TotalPrice = amount,
                OwnerID = ownerID == Guid.Empty ? null : ownerID,
                Airlines = airlines,
                VoucherCode = request.VoucherCode,
                VoucherDiscount = request.VoucherDiscount
            };
            _context.FlightRequests.Add(flightRequest);
            var result = await _context.SaveChangesAsync();
            if (result > 0)
            {
                var response = new TripAvailableRequest()
                {
                    OrderCode = orderCode,
                    EmailCustomer = request.Email,
                    PhoneCustomer = request.PhoneNumber
                };
                return new ApiSuccessResult<TripAvailableRequest>()
                {
                    ResultObj = response,
                    Message = flightRequest.TransactionId.ToString()
                };
            }
            return new ApiErrorResult<TripAvailableRequest>();
        }

        public async Task<ApiResult<OrderTripVM>> TripAvailable(TripAvailableRequest request)
        {
            if(request == null)
            {
                return new ApiErrorResult<OrderTripVM>("Request is required");
            }
            //check request.Ordercode not null and have to email customer or phone customer to get order details
            if(string.IsNullOrEmpty(request.OrderCode))
            {
                return new ApiErrorResult<OrderTripVM>("Order code is required");
            }
            if (string.IsNullOrEmpty(request.EmailCustomer) && string.IsNullOrEmpty(request.PhoneCustomer))
            {
                return new ApiErrorResult<OrderTripVM>("Email or Phone number is required");
            }
            var flightRequest = await _context.FlightRequests.Where(x => x.Id == request.OrderCode && (x.Email == request.EmailCustomer || x.PhoneNumber == request.PhoneCustomer)).FirstOrDefaultAsync();
            if (flightRequest == null)
            {
                return new ApiErrorResult<OrderTripVM>("Order not found");
            }
            var status = flightRequest.Status;
            if(status == 0 || status == 2)
            {
                status = 0;
                var dataRequestExpiry = GetDataRequestExpiry(flightRequest.Note);
                if (DateTime.Now > dataRequestExpiry)
                {
                    status = -1; // Expired
                }
            }
            
            var orderTrip = new OrderTripVM()
            {
                OrderCode = flightRequest.Id,
                Depart = flightRequest.Depart,
                Arrival = flightRequest.Arrival,
                DepartDate = flightRequest.DepartDate.ToString("dd/MM/yyyy"),
                ReturnDate = flightRequest.ReturnDate != null ? flightRequest.DepartDate.ToString("dd/MM/yyyy") : "",
                Adult = flightRequest.Adult,
                Child = flightRequest.Child,
                Infant = flightRequest.Infant,
                CustomerName = flightRequest.CustomerName,
                PhoneNumber = flightRequest.PhoneNumber,
                Email = flightRequest.Email,
                Note = flightRequest.Note,
                PaymentMethod = flightRequest.PaymentMethod,
                Status = status,
                TimeCreate = flightRequest.TimeCreate.ToString("dd/MM/yyyy HH:mm:ss")
            };
            if (flightRequest.OwnerID != null && flightRequest.OwnerID != Guid.Empty)
            {
                try
                {
                    var payment = flightRequest.PaymentMethod.StartsWith("bank-transfer") ? "credit" : "cash";

                    var features = await (from f in _context.tblFeatures 
                                             where f.OwnerID == flightRequest.OwnerID && (f.FeatureName == payment || f.FeatureName == "note-result")
                                             select new
                                             {
                                                 Key = f.FeatureName,
                                                 Value = f.Note
                                             }).ToListAsync();
                    foreach(var feature in features)
                    {
                        if(feature.Key == "cash" || feature.Key == "credit")
                        {
                            if (payment == "cash")
                            {
                                orderTrip.PaymentNote = feature.Value;
                            }
                            else
                            {
                                var bankInfo = JObject.Parse(feature.Value);
                                var bankName = flightRequest.PaymentMethod.Split("_")[1];
                                var filteredBanks = bankInfo["banksInfo"]
                                    .Where(b => b["bankName"].ToString().Equals(bankName, StringComparison.OrdinalIgnoreCase))
                                    .ToList();
                                bankInfo["banksInfo"] = JToken.FromObject(filteredBanks);
                                string updatedNotePayment = bankInfo.ToString(Formatting.Indented);
                                orderTrip.PaymentNote = updatedNotePayment;
                            }
                        }else if(feature.Key == "note-result")
                        {
                            orderTrip.NoteResult = feature.Value;
                        }
                    }
                    

                }catch(Exception ex) { }
            }

            return new ApiSuccessResult<OrderTripVM>(orderTrip);
        }

        public async Task<ApiResult<string>> RePayment(TripRePaymentRequest request)
        {
            if(request!= null)
            {
                var flightrequest = await _context.FlightRequests.FindAsync(request.OrderCode);

                if(flightrequest != null)
                {
                    //reset transaction id
                    flightrequest.TransactionId = Guid.NewGuid().ToString("N");
                    flightrequest.PaymentMethod = request.PaymentMethod;
                    flightrequest.VoucherCode = request.VoucherCode;
                    flightrequest.VoucherDiscount = request.VoucherDiscount;

                    //update value in flightrequest.Note
                    var noteJS = JObject.Parse(flightrequest.Note);
                    var amount = noteJS["totalPrice"]?.Value<long>();
                    if (amount == null || amount <= 0)
                    {
                        _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Invalid total price for OrderCode: {request.OrderCode}", request.OrderCode, "", false);
                        return new ApiErrorResult<string>("Giá trị tổng tiền không hợp lệ.");
                    }
                    noteJS["totalPrice"] = request.Amount;
                    noteJS["voucherDiscount"] = request.VoucherDiscount;
                    noteJS["transactionFee"] = request.TransactionFee;
                    flightrequest.Note = noteJS.ToString(Formatting.Indented);

                    _context.FlightRequests.Update(flightrequest);
                    await _context.SaveChangesAsync();
                   

                    // Check if data request is still valid (within time limit)
                    var dataRequestExpiry = GetDataRequestExpiry(flightrequest.Note);
                    if (DateTime.Now > dataRequestExpiry)
                    {
                        _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Data request expired for OrderCode: {request.OrderCode}. Expiry: {dataRequestExpiry}", request.OrderCode, "", false);
                        return new ApiErrorResult<string>("Đơn hàng đã hết hạn. Không thể thực hiện thanh toán.");
                    }

                    // Check order status - only allow repayment if status is 0 (pending)
                    if (flightrequest.Status == 1 || flightrequest.Status == -1)
                    {
                        _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Order status is not pending for OrderCode: {request.OrderCode}. Current status: {flightrequest.Status}", request.OrderCode, "", false);
                        return new ApiErrorResult<string>("Đơn hàng không ở trạng thái chờ thanh toán.");
                    }

                    // Handle voucher if provided (only reserve for repayment)
                    if (!string.IsNullOrEmpty(request.VoucherCode) && amount != null)
                    {
                        var voucherResult = await _voucherService.CheckAndReserveVoucherAsync(
                            request.VoucherCode,
                            (decimal)amount,
                            request.OrderCode
                        );

                        if (!voucherResult.IsSuccessed)
                        {
                            _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Voucher reservation failed: {voucherResult.Message}", request.OrderCode, "", false);
                            return new ApiErrorResult<string>($"Voucher error: {voucherResult.Message}");
                        }
                        _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Voucher {request.VoucherCode} reserved successfully. Discount: {voucherResult.ResultObj.discount}", request.OrderCode, "", false);
                    }


                    // Check if hold trip feature is enabled and perform hold if needed
                    Guid ownerID = Guid.Empty;
                    var apiKey = _httpContextAccessor.HttpContext.Request.Headers["X-Api-Key"].ToString();
                    if (!string.IsNullOrEmpty(apiKey))
                    {
                        ownerID = await _context.tblApiKeyLibs.Where(t => t.XApiKey == apiKey).Select(t => t.Id).FirstOrDefaultAsync();
                    }
                    if (string.IsNullOrEmpty(flightrequest.Pnrs))
                    {
                        var isHoldTrip = false;
                        if (ownerID != Guid.Empty)
                        {
                            isHoldTrip = await _context.tblFeatures.Where(x => x.OwnerID == ownerID && x.FeatureName == "HoldTrip").Select(x => x.IsUnlocked).FirstOrDefaultAsync();
                        }
                        else
                        {
                            isHoldTrip = await _context.tblFeatures.Where(t => t.FeatureName == "HoldTrip" && (t.OwnerID == null || t.OwnerID == Guid.Empty)).Select(t => t.IsUnlocked).FirstOrDefaultAsync() || false;
                        }

                        // If hold trip is enabled, perform hold operation
                        if (isHoldTrip)
                        {
                            try
                            {
                                var holdResult = await PerformHoldTripForRepayment(flightrequest);
                                if (!holdResult.IsSuccessed)
                                {
                                    _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Hold trip failed for OrderCode: {request.OrderCode}. Error: {holdResult.Message}", request.OrderCode, "", false);
                                    return new ApiErrorResult<string>($"Không thể giữ chỗ: {holdResult.Message}");
                                }
                                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Hold trip successful for OrderCode: {request.OrderCode}", request.OrderCode, "", false);
                            }
                            catch (Exception ex)
                            {
                                _loggerService.LogResponse(_httpContextAccessor.HttpContext, $"Hold trip exception for OrderCode: {request.OrderCode}. Error: {ex.Message}", request.OrderCode, "", false);
                                return new ApiErrorResult<string>("Lỗi khi thực hiện giữ chỗ.");
                            }
                        }

                    }
                    
                    var refererClient = _httpContextAccessor.HttpContext.Request.Headers["Referer"].ToString();
                    var originClient = _httpContextAccessor.HttpContext.Request.Headers["Origin"].ToString();


                    
                    //get payment url
                    if (request.PaymentMethod.Contains("e-wallet"))
                    {
                        //get doamin client from httpcontext
                        var domainClient = _httpContextAccessor.HttpContext.Request.Host.Value;
                        var scheme = _httpContextAccessor.HttpContext.Request.Scheme;
                        var domain = scheme + "://" + domainClient;
                        //var tripSumary = dataCart["summary"]?.ToString();
                        var paymentRq = new PaymentCreateModelRq()
                        {
                            orderId = flightrequest.TransactionId.ToString(),
                            amount = (long)amount,
                            redirectUrl = (string.IsNullOrEmpty(refererClient) ? originClient : refererClient) + $"/TripResult?OrderCode={flightrequest.Id}&PhoneCustomer={flightrequest.PhoneNumber}&EmailCustomer={flightrequest.Email}",
                            orderInfo = $"Thanh toan don hang:{flightrequest.Id}"
                        };
                        if (request.PaymentMethod.ToLower().Contains("momo"))
                        {
                            paymentRq.ipnUrl = domain + "/api/Payments/Momo/Callback";
                            var resultMomo = await _momoService.CreatePayment(paymentRq);
                            return new ApiSuccessResult<string>(resultMomo.ResultObj.payUrl);
                        }
                        else if (request.PaymentMethod.ToLower().Contains("vnpay"))
                        {

                            paymentRq.PaymentMethod = request.PaymentMethod.Replace("e-walletVNPay", "");
                            var paymentUrl = _vnPayService.CreatePayment(paymentRq).Result;
                            return new ApiSuccessResult<string>(paymentUrl.ResultObj);
                        }
                    }
                    else
                    {
                        //await _emailService.SentEmailBookTrip(flightrequest.Id);
                        _emailQueue.Enqueue(async provider =>
                        {
                            var emailService = provider.GetRequiredService<IEmailService>();

                            await emailService.SentEmailBookTrip(flightrequest.Id, false);
                            await emailService.SentEmailBookTrip(flightrequest.Id, true);
                        });
                        return new ApiSuccessResult<string>($"/TripResult?OrderCode={flightrequest.Id}&PhoneCustomer={flightrequest.PhoneNumber}&EmailCustomer={flightrequest.Email}");
                    }
                }
            }
            
            return new ApiErrorResult<string>("ERROR:request null");
        }

        public async Task<ApiResult<PagedResult<PartnerTicketPagingVM>>> PartnerPagingRequest(PartnerTicketPagingRequest request)
        {
            var userName = _httpContextAccessor.HttpContext?.User.Identity?.Name;
            var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == userName);
            if (user == null)
                return new ApiErrorResult<PagedResult<PartnerTicketPagingVM>>("User not found");

            var xApiKey = await _originService.GetOwnerIDApiKey(user.Id);

            if (xApiKey == null)
                return new ApiErrorResult<PagedResult<PartnerTicketPagingVM>>("XApiKey not found");

            var query = from f in _context.FlightRequests
                        where f.OwnerID == xApiKey
                        join u in _context.Users on f.UpdateBy equals u.Id into gj
                        from updater in gj.DefaultIfEmpty()
                        select new
                        {
                            Flight = f,
                            UpdaterName = updater != null ? updater.FirstName + updater.LastName : null
                        };

            query = query.AsNoTracking();

            // Filter by date
            if (!string.IsNullOrEmpty(request.TypeDate))
            {
                request.FromDate = request.FromDate?.Date ?? DateTime.Today;
                request.ToDate = request.ToDate?.Date.AddDays(1).AddTicks(-1) ?? DateTime.Today.AddDays(1).AddTicks(-1);
                if(request.TypeDate == "BookingDate")
                {
                    query = query.Where(f => f.Flight.TimeCreate >= request.FromDate && f.Flight.TimeCreate <= request.ToDate);
                }
                else if (request.TypeDate == "DepartDate")
                {
                    query = query.Where(f => f.Flight.DepartDate >= request.FromDate && f.Flight.DepartDate <= request.ToDate);
                }
            }
            
            // Filter by code
            if (!string.IsNullOrEmpty(request.CodeKeyword))
                query = query.Where(f => f.Flight.Id.Contains(request.CodeKeyword) || f.Flight.Pnrs.Contains(request.CodeKeyword));


            // Filter by status
            if (request.FilterStatus.HasValue)
                query = query.Where(f => f.Flight.Status == request.FilterStatus);

            //// Filter by airline
            if (!string.IsNullOrEmpty(request.FilterAirline))
                query = query.Where(f => f.Flight.Airlines.Contains(request.FilterAirline));

            // Sorting
            if (!string.IsNullOrEmpty(request.SortColumn))
            {
                query = request.SortOrder.ToLower() == "desc"
                    ? query.OrderByDescending(e => EF.Property<object>(e.Flight, request.SortColumn))
                    : query.OrderBy(e => EF.Property<object>(e.Flight, request.SortColumn));
            }
            else
            {
                query = query.OrderByDescending(x => x.Flight.TimeCreate); // Default sorting
            }

            var data = await query.ToListAsync();
            // Filter by customer
            if (!string.IsNullOrEmpty(request.CustomerKeyword))
            {
                var normalizedKeyword = SeoHelper.RemoveDiacritics(request.CustomerKeyword);

                data = data.Where(f =>
                    SeoHelper.RemoveDiacritics(f.Flight.CustomerName).Contains(normalizedKeyword) ||
                    f.Flight.PhoneNumber.Contains(request.CustomerKeyword) ||
                    f.Flight.Email.Contains(request.CustomerKeyword)).ToList();
            }


            var totalRecords = data.Count();
            var rawItems = data
                        .Skip((request.PageIndex - 1) * request.PageSize)
                        .Take(request.PageSize)
                        .ToList();

            var items = rawItems.Select(f =>
            {
                List<string> itineraryList = new();

                try
                {
                    if (!string.IsNullOrEmpty(f.Flight.Note))
                    {
                        var json = JsonConvert.DeserializeObject<JObject>(f.Flight.Note);
                        var summaries = json["summary"] as JArray;

                        if (summaries != null)
                        {
                            foreach (var summary in summaries)
                            {
                                var legs = summary["Legs"] as JArray;

                                if (legs != null)
                                {
                                    for (int i = 0; i < legs.Count; i++)
                                    {
                                        var leg = legs[i]?.ToString();
                                        if (string.IsNullOrWhiteSpace(leg)) continue;

                                        var parts = leg.Split('_');
                                        if (parts.Length < 6) continue;

                                        var flightNumber = parts[1]; // CA904
                                        var from = parts[2];         // SGN
                                        var to = parts[3];           // PEK
                                        var departureRaw = parts[4]; // 2025-05-31T05:10:00+07:00

                                        if (!DateTime.TryParse(departureRaw, out var departureDateTime))
                                            continue;

                                        var formatted = $"{flightNumber} {departureDateTime:HH:mm} {departureDateTime:dd/MM/yyyy} {from}-{to}";
                                        itineraryList.Add(formatted);
                                    }
                                }
                            }
                        }
                    }
                }
                catch
                {
                    // Log nếu cần
                }

                return new PartnerTicketPagingVM
                {
                    Id = f.Flight.Id,
                    Pnrs = !string.IsNullOrEmpty(f.Flight.Pnrs) ? f.Flight.Pnrs : (!string.IsNullOrEmpty(f.Flight.Airlines) ? f.Flight.Airlines + ": Fail" : null),
                    CustomerName = f.Flight.CustomerName,
                    PhoneNumber = f.Flight.PhoneNumber,
                    Itinerary = itineraryList,
                    TotalPrice = f.Flight.TotalPrice,
                    TimeCreate = f.Flight.TimeCreate.ToString("dd/MM/yyyy HH:mm:ss"),
                    TimeCompletion = f.Flight.TimeCompletion?.ToString("dd/MM/yyyy HH:mm:ss"),
                    Status = f.Flight.Status,
                    TimeUpdate = f.Flight.TimeUpdate?.ToString("dd/MM/yyyy HH:mm:ss"),
                    UpdateBy = f.UpdaterName,
                    Notes = f.Flight.Note,
                    UserNote = GetLatestNoteContent(f.Flight.UserNote)
                };
            }).ToList();



            var result = new PagedResult<PartnerTicketPagingVM>
            {
                Items = items,
                PageIndex = request.PageIndex,
                PageSize = request.PageSize,
                TotalRecords = totalRecords,
                From = totalRecords > 0 ? ((request.PageIndex - 1) * request.PageSize) + 1 : 0,
                To = totalRecords > 0 ? Math.Min(request.PageIndex * request.PageSize, totalRecords) : 0
            };

            return new ApiSuccessResult<PagedResult<PartnerTicketPagingVM>>(result);
        }
        private string GetLatestNoteContent(string noteUserJson)
        {
            if (string.IsNullOrWhiteSpace(noteUserJson)) return null;
            try
            {
                var arr = JsonConvert.DeserializeObject<List<NoteUserItem>>(noteUserJson);
                if (arr == null || arr.Count == 0) return null;
                var latest = arr.OrderByDescending(x => x.time).FirstOrDefault();
                return latest?.content;
            }
            catch
            {
                return null;
            }
        }

        public async Task<ApiResult<FlightRequestDetails>> GetRequestDetailsByID(string id)
        {
            var userName = _httpContextAccessor.HttpContext?.User.Identity?.Name;
            var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == userName);
            if (user == null)
                return new ApiErrorResult<FlightRequestDetails>("User not found");

            var flightRequest = await _context.FlightRequests.FindAsync(id);
            if (flightRequest == null)
            {
                return new ApiErrorResult<FlightRequestDetails>("Request not found");
            }

            // Nếu đã có người thao tác khác người hiện tại, không cho phép truy cập
            if (flightRequest.UpdateBy != null && flightRequest.UpdateBy != user.Id && flightRequest.Status == 2)
            {
                return new ApiErrorResult<FlightRequestDetails>(409, "Request is being processed by another user");
            }

            // Nếu chưa ai thao tác hoặc chính người hiện tại đang thao tác
            if (flightRequest.UpdateBy == null && flightRequest.Status == 0 && flightRequest.OwnerID == null)
            {
                flightRequest.UpdateBy = user.Id;
                flightRequest.TimeUpdate = DateTime.Now;
                flightRequest.Status = 2;
                _context.FlightRequests.Update(flightRequest);
                await _context.SaveChangesAsync(); // lưu UpdateBy
            }

            var flightRequestDetails = new FlightRequestDetails()
            {
                Id = flightRequest.Id,
                Depart = flightRequest.Depart,
                Arrival = flightRequest.Arrival,
                DepartDate = flightRequest.DepartDate,
                ReturnDate = flightRequest.ReturnDate,
                Adult = flightRequest.Adult,
                Child = flightRequest.Child,
                Infant = flightRequest.Infant,
                CustomerName = flightRequest.CustomerName,
                PhoneNumber = flightRequest.PhoneNumber,
                Email = flightRequest.Email,
                Note = flightRequest.Note,
                Status = flightRequest.Status,
                TimeCreate = flightRequest.TimeCreate,
                TimeCompletion = flightRequest.TimeCompletion,
                PaymentMethod = flightRequest.PaymentMethod,
                Pnrs = flightRequest.Pnrs,
                Airlines = flightRequest.Airlines,
                NoteUser = flightRequest.UserNote
            };
            int code = flightRequest.OwnerID == null ? 200 : 403;
            return new ApiSuccessResult<FlightRequestDetails>(code, flightRequestDetails);
        }

        private DateTime GetDataRequestExpiry(string flightRequestNote)
        {
            try
            {
                if (!string.IsNullOrEmpty(flightRequestNote))
                {
                    var noteData = JObject.Parse(flightRequestNote);
                    var fullToken = noteData["full"];
                    if (fullToken != null && fullToken["expDate"] != null)
                    {
                        var expDateToken = fullToken["expDate"];
                        if (DateTime.TryParse(expDateToken.ToString(), null, DateTimeStyles.AdjustToUniversal, out var expDate))
                        {
                            return expDate.ToLocalTime();
                        }
                    }
                }
                // Nếu không tìm thấy expDate, trả về giá trị đã hết hạn
                return DateTime.Now.AddMinutes(-1);
            }
            catch (Exception)
            {
                // Nếu lỗi, trả về giá trị đã hết hạn
                return DateTime.Now.AddMinutes(-1);
            }
        }

        private async Task<ApiResult<bool>> PerformHoldTripForRepayment(FlightRequest flightRequest)
        {
            try
            {
                if (string.IsNullOrEmpty(flightRequest.Note))
                {
                    return new ApiErrorResult<bool>("Không tìm thấy thông tin chuyến bay để giữ chỗ.");
                }

                var noteData = JObject.Parse(flightRequest.Note);
                var bookTripJson = noteData["request"]?.ToString();

                if (string.IsNullOrEmpty(bookTripJson))
                {
                    return new ApiErrorResult<bool>("Không tìm thấy dữ liệu booking để giữ chỗ.");
                }

                var requestBookTrip = JsonConvert.DeserializeObject<List<RqBookTripModel>>(bookTripJson);
                if (requestBookTrip == null || !requestBookTrip.Any())
                {
                    return new ApiErrorResult<bool>("Dữ liệu booking không hợp lệ.");
                }

                var apiKey = _httpContextAccessor.HttpContext.Request.Headers["X-Api-Key"].ToString();
                var emailReceiveGDS = await _xApiKeyService.GetEmailReciveGDS(apiKey);
                if (string.IsNullOrEmpty(emailReceiveGDS))
                {
                    return new ApiErrorResult<bool>("Không tìm thấy email nhận thông báo GDS.");
                }
                // Set email for each booking request
                requestBookTrip.ForEach(item => item.Email = emailReceiveGDS);

                var listResultBookTrip = new List<ApiResult<RpBookRetrieveModel>>();
                var bookTripTasks = requestBookTrip.Select(item => _nMBookingApiClient.BookTrip(item)).ToList();

                var bookTripResults = await Task.WhenAll(bookTripTasks);
                listResultBookTrip.AddRange(bookTripResults);

                var bookTripSuccess = listResultBookTrip.Count(x => x.IsSuccessed);
                var countBookTrip = bookTripTasks.Count;

                // Update PNRs if hold successful
                var newPnrs = string.Join(",", listResultBookTrip
                    .Where(x => x.IsSuccessed && x?.ResultObj?.ListRetrieve != null)
                    .SelectMany(x => x.ResultObj.ListRetrieve.Select(t => t.Pnr)));

                if (!string.IsNullOrEmpty(newPnrs))
                {
                    flightRequest.Pnrs = newPnrs;
                    _context.FlightRequests.Update(flightRequest);
                    await _context.SaveChangesAsync();
                }

                if (bookTripSuccess != countBookTrip)
                {
                    var failedBookings = listResultBookTrip.Where(x => !x.IsSuccessed).ToList();
                    var errorMessages = string.Join("; ", failedBookings.Select(x => x.Message));

                    _loggerService.LogResponse(_httpContextAccessor.HttpContext,
                        $"Hold trip partially failed for OrderCode: {flightRequest.Id}. Success: {bookTripSuccess}/{countBookTrip}. Errors: {errorMessages}",
                        flightRequest.Id, "", false);

                    return new ApiErrorResult<bool>($"Chỉ giữ được {bookTripSuccess}/{countBookTrip} chuyến bay. Lỗi: {errorMessages}");
                }

                // Save flight passenger hold data
                _emailQueue.Enqueue(async provider =>
                {
                    var validatorBookingServiceTemp = provider.GetRequiredService<IValidatorBookingService>();
                    await validatorBookingServiceTemp.SaveFlightPassengerHold(new FlightRequestVM
                    {
                        CustomerName = flightRequest.CustomerName,
                        PhoneNumber = flightRequest.PhoneNumber,
                        Email = flightRequest.Email,
                        Note = flightRequest.Note,
                        PaymentMethod = flightRequest.PaymentMethod,
                        Pnrs = flightRequest.Pnrs,
                        TicketNumbers = flightRequest.TicketNumbers,
                        VoucherCode = flightRequest.VoucherCode,
                        VoucherDiscount = flightRequest.VoucherDiscount
                    }, listResultBookTrip);
                });

                return new ApiSuccessResult<bool>();
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(_httpContextAccessor.HttpContext,
                    $"Exception in PerformHoldTripForRepayment for OrderCode: {flightRequest.Id}. Error: {ex.Message}",
                    flightRequest.Id, "", false);

                return new ApiErrorResult<bool>($"Lỗi khi thực hiện giữ chỗ: {ex.Message}");
            }
        }

        public async Task<ApiResult<bool>> AddNoteUser(FlightNoteUserRq request)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            var user = await _originService.CheckUserRequest(httpContext);
            if (user == null)
            {
                return new ApiErrorResult<bool>("User not found");
            }
            var ownerID = await _originService.GetOwnerIDApiKey(user.Id);
            if(ownerID == Guid.Empty)
            {
                ownerID = null;
            }

            if (string.IsNullOrEmpty(request.OrderCode) || string.IsNullOrEmpty(request.NoteUser))
            {
                return new ApiErrorResult<bool>("Order code and note user are required");
            }
            var flightRequest = await _context.FlightRequests.Where(x => x.Id == request.OrderCode && x.OwnerID == ownerID).FirstOrDefaultAsync();
            if (flightRequest == null)
            {
                return new ApiErrorResult<bool>("Flight request not found");
            }
            if (flightRequest.UpdateBy != user.Id && flightRequest.Status == 2)
            {
                return new ApiErrorResult<bool>(409, "Request is being processed by another user");
            }
            if (flightRequest.Status == 2)
            {
                flightRequest.UserNote = request.NoteUser;
                flightRequest.UpdateBy = user.Id;
                flightRequest.TimeUpdate = DateTime.Now;
                flightRequest.Status = 2;
                _context.FlightRequests.Update(flightRequest);
                var checkupdate = await _context.SaveChangesAsync();
                if (checkupdate > 0)
                {
                    _loggerService.LogResponse(httpContext, $"User {user.UserName} added note to flight request {flightRequest.Id}", flightRequest.Id, "", true);
                    return new ApiSuccessResult<bool>(true);
                }
                else
                {
                    return new ApiErrorResult<bool>("Failed to update flight request");
                }
            }
            return new ApiErrorResult<bool>("Flight request is not in a valid state for adding note");
        }
    }
}
