using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NgocMaiTravel.Application.Catalog.ESim.Models;
using NgocMaiTravel.Application.Catalog.ESim.Repositories;
using NgocMaiTravel.ApiIntegration.ESimBlueAPI;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities.ESim;
using System.Diagnostics;
using System.Text.Json;

namespace NgocMaiTravel.Application.Catalog.ESim.Services
{
    /// <summary>
    /// Service implementation for ESim synchronization
    /// </summary>
    public class ESimSyncService : IESimSyncService
    {
        private readonly IESimBlueAPIClient _apiClient;
        private readonly IESimRepository _repository;
        private readonly NgocMaiTravelDbContext _context;
        private readonly ILogger<ESimSyncService> _logger;
        private readonly ESimSyncConfiguration _config;

        public ESimSyncService(
            IESimBlueAPIClient apiClient,
            IESimRepository repository,
            NgocMaiTravelDbContext context,
            ILogger<ESimSyncService> logger,
            IOptions<ESimSyncConfiguration> config)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _config = config.Value ?? throw new ArgumentNullException(nameof(config));
        }

        public async Task<bool> IsSyncNeededAsync(TimeSpan interval)
        {
            try
            {
                var lastSyncDate = await _repository.GetLastSyncDateAsync();
                return !lastSyncDate.HasValue || DateTime.UtcNow - lastSyncDate.Value > interval;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if sync is needed");
                return true; // Default to sync needed if we can't determine
            }
        }

        public async Task QueueFullSyncAsync(string requestedBy)
        {
            try
            {
                var syncLog = new ESimSyncLog
                {
                    SyncType = ESimSyncTypes.FullSync,
                    Status = ESimSyncStatus.Pending,
                    StartTime = DateTime.UtcNow,
                    RequestedBy = requestedBy
                };

                _context.ESimSyncLogs.Add(syncLog);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Queued full sync requested by: {RequestedBy}", requestedBy);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error queueing full sync");
                throw;
            }
        }

        public async Task QueueIncrementalSyncAsync(string requestedBy)
        {
            try
            {
                var syncLog = new ESimSyncLog
                {
                    SyncType = ESimSyncTypes.IncrementalSync,
                    Status = ESimSyncStatus.Pending,
                    StartTime = DateTime.UtcNow,
                    RequestedBy = requestedBy
                };

                _context.ESimSyncLogs.Add(syncLog);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Queued incremental sync requested by: {RequestedBy}", requestedBy);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error queueing incremental sync");
                throw;
            }
        }

        public async Task ExecuteFullSyncAsync(string requestedBy)
        {
            var stopwatch = Stopwatch.StartNew();
            var syncLog = new ESimSyncLog
            {
                SyncType = ESimSyncTypes.FullSync,
                Status = ESimSyncStatus.Running,
                StartTime = DateTime.UtcNow,
                RequestedBy = requestedBy
            };

            try
            {
                _context.ESimSyncLogs.Add(syncLog);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Starting full ESim sync requested by: {RequestedBy}", requestedBy);

                // Get all packages from API
                var apiRequest = new EsimPackagesRq
                {
                    LocationCode = "",
                    Type = "data"
                };

                var apiResult = await _apiClient.SearchPlansAsync(apiRequest);
                if (!apiResult.IsSuccessed || apiResult.ResultObj?.PackageList == null)
                {
                    throw new Exception($"Failed to get packages from API: {apiResult.Message}");
                }

                var packages = apiResult.ResultObj.PackageList;
                syncLog.TotalPackages = packages.Count;

                var processedCount = 0;
                var newCount = 0;
                var updatedCount = 0;

                // Process packages in batches
                var batches = packages.Chunk(_config.BatchSize);
                foreach (var batch in batches)
                {
                    foreach (var pkg in batch)
                    {
                        try
                        {
                            var isNew = await ProcessPackageAsync(pkg);
                            if (isNew)
                                newCount++;
                            else
                                updatedCount++;

                            processedCount++;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to process package {PackageId}", pkg.Id);
                        }
                    }

                    // Update progress
                    syncLog.ProcessedPackages = processedCount;
                    syncLog.NewPackages = newCount;
                    syncLog.UpdatedPackages = updatedCount;
                    _context.ESimSyncLogs.Update(syncLog);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Processed {ProcessedCount}/{TotalCount} packages", processedCount, packages.Count);
                }

                // Complete sync
                stopwatch.Stop();
                syncLog.Status = ESimSyncStatus.Success;
                syncLog.EndTime = DateTime.UtcNow;
                syncLog.Duration = (int)stopwatch.ElapsedMilliseconds;
                syncLog.ProcessedPackages = processedCount;
                syncLog.NewPackages = newCount;
                syncLog.UpdatedPackages = updatedCount;

                _context.ESimSyncLogs.Update(syncLog);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Completed full ESim sync. Processed: {ProcessedCount}, New: {NewCount}, Updated: {UpdatedCount}, Duration: {Duration}ms",
                    processedCount, newCount, updatedCount, stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                syncLog.Status = ESimSyncStatus.Failed;
                syncLog.EndTime = DateTime.UtcNow;
                syncLog.Duration = (int)stopwatch.ElapsedMilliseconds;
                syncLog.ErrorMessage = ex.Message;

                _context.ESimSyncLogs.Update(syncLog);
                await _context.SaveChangesAsync();

                _logger.LogError(ex, "Full ESim sync failed after {Duration}ms", stopwatch.ElapsedMilliseconds);
                throw;
            }
        }

        public async Task ExecuteIncrementalSyncAsync(string requestedBy)
        {
            var stopwatch = Stopwatch.StartNew();
            var syncLog = new ESimSyncLog
            {
                SyncType = ESimSyncTypes.IncrementalSync,
                Status = ESimSyncStatus.Running,
                StartTime = DateTime.UtcNow,
                RequestedBy = requestedBy
            };

            try
            {
                _context.ESimSyncLogs.Add(syncLog);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Starting incremental ESim sync requested by: {RequestedBy}", requestedBy);

                // For incremental sync, we'll sync packages that haven't been updated recently
                var cutoffDate = DateTime.UtcNow.Subtract(_config.CacheExpiry);
                
                // Get all packages from API (for now, we'll do full sync as incremental)
                // In a real implementation, you might have an API endpoint for incremental updates
                var apiRequest = new EsimPackagesRq
                {
                    LocationCode = "",
                    Type = "data"
                };

                var apiResult = await _apiClient.SearchPlansAsync(apiRequest);
                if (!apiResult.IsSuccessed || apiResult.ResultObj?.PackageList == null)
                {
                    throw new Exception($"Failed to get packages from API: {apiResult.Message}");
                }

                var packages = apiResult.ResultObj.PackageList;
                syncLog.TotalPackages = packages.Count;

                var processedCount = 0;
                var newCount = 0;
                var updatedCount = 0;

                // Process only packages that need updating
                foreach (var pkg in packages.Take(_config.BatchSize)) // Limit for incremental
                {
                    try
                    {
                        var isNew = await ProcessPackageAsync(pkg);
                        if (isNew)
                            newCount++;
                        else
                            updatedCount++;

                        processedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to process package {PackageId}", pkg.Id);
                    }
                }

                // Complete sync
                stopwatch.Stop();
                syncLog.Status = ESimSyncStatus.Success;
                syncLog.EndTime = DateTime.UtcNow;
                syncLog.Duration = (int)stopwatch.ElapsedMilliseconds;
                syncLog.ProcessedPackages = processedCount;
                syncLog.NewPackages = newCount;
                syncLog.UpdatedPackages = updatedCount;

                _context.ESimSyncLogs.Update(syncLog);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Completed incremental ESim sync. Processed: {ProcessedCount}, New: {NewCount}, Updated: {UpdatedCount}, Duration: {Duration}ms",
                    processedCount, newCount, updatedCount, stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                syncLog.Status = ESimSyncStatus.Failed;
                syncLog.EndTime = DateTime.UtcNow;
                syncLog.Duration = (int)stopwatch.ElapsedMilliseconds;
                syncLog.ErrorMessage = ex.Message;

                _context.ESimSyncLogs.Update(syncLog);
                await _context.SaveChangesAsync();

                _logger.LogError(ex, "Incremental ESim sync failed after {Duration}ms", stopwatch.ElapsedMilliseconds);
                throw;
            }
        }

        public async Task ExecuteGroupSyncAsync(string groupCode, string requestedBy)
        {
            var stopwatch = Stopwatch.StartNew();
            var syncLog = new ESimSyncLog
            {
                SyncType = ESimSyncTypes.GroupSync,
                Status = ESimSyncStatus.Running,
                StartTime = DateTime.UtcNow,
                RequestedBy = requestedBy
            };

            try
            {
                _context.ESimSyncLogs.Add(syncLog);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Starting group ESim sync for {GroupCode} requested by: {RequestedBy}", groupCode, requestedBy);

                // Get packages for specific group/country
                var apiRequest = new EsimPackagesRq
                {
                    LocationCode = groupCode,
                    Type = "data"
                };

                var apiResult = await _apiClient.SearchPlansAsync(apiRequest);
                if (!apiResult.IsSuccessed || apiResult.ResultObj?.PackageList == null)
                {
                    throw new Exception($"Failed to get packages from API for group {groupCode}: {apiResult.Message}");
                }

                var packages = apiResult.ResultObj.PackageList;
                syncLog.TotalPackages = packages.Count;

                var processedCount = 0;
                var newCount = 0;
                var updatedCount = 0;

                // Process packages for this group
                foreach (var pkg in packages)
                {
                    try
                    {
                        var isNew = await ProcessPackageAsync(pkg, groupCode);
                        if (isNew)
                            newCount++;
                        else
                            updatedCount++;

                        processedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to process package {PackageId} for group {GroupCode}", pkg.Id, groupCode);
                    }
                }

                // Complete sync
                stopwatch.Stop();
                syncLog.Status = ESimSyncStatus.Success;
                syncLog.EndTime = DateTime.UtcNow;
                syncLog.Duration = (int)stopwatch.ElapsedMilliseconds;
                syncLog.ProcessedPackages = processedCount;
                syncLog.NewPackages = newCount;
                syncLog.UpdatedPackages = updatedCount;

                _context.ESimSyncLogs.Update(syncLog);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Completed group ESim sync for {GroupCode}. Processed: {ProcessedCount}, New: {NewCount}, Updated: {UpdatedCount}, Duration: {Duration}ms",
                    groupCode, processedCount, newCount, updatedCount, stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                syncLog.Status = ESimSyncStatus.Failed;
                syncLog.EndTime = DateTime.UtcNow;
                syncLog.Duration = (int)stopwatch.ElapsedMilliseconds;
                syncLog.ErrorMessage = ex.Message;

                _context.ESimSyncLogs.Update(syncLog);
                await _context.SaveChangesAsync();

                _logger.LogError(ex, "Group ESim sync failed for {GroupCode} after {Duration}ms", groupCode, stopwatch.ElapsedMilliseconds);
                throw;
            }
        }

        private async Task<bool> ProcessPackageAsync(dynamic pkg, string? specificGroupCode = null)
        {
            try
            {
                // Create package entity
                var package = new ESimPackage
                {
                    PackageId = pkg.Id?.ToString() ?? Guid.NewGuid().ToString(),
                    Sku = pkg.Sku?.ToString() ?? "",
                    PackageName = pkg.PackageName?.ToString() ?? pkg.Name?.ToString() ?? "",
                    Description = pkg.Description?.ToString() ?? "",
                    DataAmount = ParseDataAmount(pkg.DataAmount),
                    ValidityDays = ParseInt(pkg.ValidityDays) ?? ParseInt(pkg.Validity) ?? 0,
                    Price = ParseDecimal(pkg.Price) ?? 0,
                    Currency = pkg.Currency?.ToString() ?? "USD",
                    OriginalPrice = ParseDecimal(pkg.OriginalPrice),
                    DiscountPercent = ParseDecimal(pkg.DiscountPercent),
                    PackageType = pkg.PackageType?.ToString() ?? "DATA",
                    NetworkType = pkg.NetworkType?.ToString(),
                    IsUnlimited = ParseBool(pkg.IsUnlimited) ?? false,
                    IsTopUpSupported = ParseBool(pkg.IsTopUpSupported) ?? false,
                    IsActive = true,
                    ApiSource = "ESimBlue",
                    ExternalData = JsonSerializer.Serialize(pkg),
                    CreatedBy = "SyncService",
                    UpdatedBy = "SyncService"
                };

                // Check if package exists
                var existingPackage = await _repository.GetPackageByIdAsync(package.PackageId);
                var isNew = existingPackage == null;

                // Create or update package
                var savedPackage = await _repository.CreateOrUpdatePackageAsync(package);

                // Handle group mappings
                await ProcessGroupMappingsAsync(savedPackage, pkg, specificGroupCode);

                return isNew;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing package: {PackageData}", JsonSerializer.Serialize(pkg));
                throw;
            }
        }

        private async Task ProcessGroupMappingsAsync(ESimPackage package, dynamic pkg, string? specificGroupCode = null)
        {
            try
            {
                var groupCodes = new List<string>();

                if (!string.IsNullOrEmpty(specificGroupCode))
                {
                    groupCodes.Add(specificGroupCode);
                }
                else
                {
                    // Extract country codes from coverage data
                    if (pkg.ExtraData?.Operator?.Coverages?.CoverageArray != null)
                    {
                        foreach (var coverage in pkg.ExtraData.Operator.Coverages.CoverageArray)
                        {
                            var locationCode = coverage.LocationCode?.ToString()?.ToUpper();
                            if (!string.IsNullOrEmpty(locationCode))
                            {
                                groupCodes.Add(locationCode);
                            }
                        }
                    }
                }

                // Create or update groups and mappings
                foreach (var groupCode in groupCodes.Distinct())
                {
                    // Create or update group
                    var group = new ESimGroup
                    {
                        GroupCode = groupCode,
                        GroupName = GetCountryName(groupCode),
                        GroupType = "COUNTRY",
                        IsActive = true,
                        DisplayOrder = 0,
                        CreatedBy = "SyncService",
                        UpdatedBy = "SyncService"
                    };

                    var savedGroup = await _repository.CreateOrUpdateGroupAsync(group);

                    // Create group-package mapping
                    await _repository.CreateGroupPackageMappingAsync(savedGroup.Id, package.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing group mappings for package {PackageId}", package.PackageId);
                throw;
            }
        }

        private string GetCountryName(string countryCode)
        {
            // Simple mapping - in real implementation, you might use a lookup table
            return countryCode switch
            {
                "VN" => "Vietnam",
                "JP" => "Japan",
                "US" => "United States",
                "TH" => "Thailand",
                "SG" => "Singapore",
                "KR" => "South Korea",
                "CN" => "China",
                "MY" => "Malaysia",
                "ID" => "Indonesia",
                "PH" => "Philippines",
                _ => countryCode
            };
        }

        private long ParseDataAmount(dynamic value)
        {
            if (value == null) return 0;
            
            var str = value.ToString();
            if (long.TryParse(str, out var result))
                return result;
                
            // Handle string formats like "1GB", "500MB"
            str = str.ToUpper().Replace(" ", "");
            if (str.EndsWith("GB"))
            {
                if (decimal.TryParse(str.Replace("GB", ""), out var gb))
                    return (long)(gb * 1024 * 1024 * 1024);
            }
            else if (str.EndsWith("MB"))
            {
                if (decimal.TryParse(str.Replace("MB", ""), out var mb))
                    return (long)(mb * 1024 * 1024);
            }
            
            return 0;
        }

        private int? ParseInt(dynamic value)
        {
            if (value == null) return null;
            if (int.TryParse(value.ToString(), out var result))
                return result;
            return null;
        }

        private decimal? ParseDecimal(dynamic value)
        {
            if (value == null) return null;
            if (decimal.TryParse(value.ToString(), out var result))
                return result;
            return null;
        }

        private bool? ParseBool(dynamic value)
        {
            if (value == null) return null;
            if (bool.TryParse(value.ToString(), out var result))
                return result;
            return null;
        }
    }
}
