﻿using NgocMaiTravel.ApiIntegration.Esim;
using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.ApiIntegration.ESimBlueAPI
{
    public interface IESimBlueAPIClient
    {
        Task<ApiResult<BalanceRp>> GetBalanceAsync();
        // Search and Browse
        Task<ApiResult<EsimPackagesRp>> GetAllDataPackagesAsync(EsimPackagesRq rq);
        //Task<ApiResult<List<ESimCountryVM>>> GetCountriesAsync();
        //Task<ApiResult<ESimPlanVM>> GetPlanDetailsAsync(string planId);

        // Order Management
        //Task<ApiResult<ESimOrderVM>> CreateOrderAsync(ESimOrderRequest request);
        //Task<ApiResult<ESimOrderVM>> GetOrderStatusAsync(string orderId);
        //Task<ApiResult<bool>> CancelOrderAsync(string orderId);

        // Activation
        //Task<ApiResult<ESimActivationVM>> ActivateESimAsync(ESimActivationRequest request);
        //Task<ApiResult<ESimActivationVM>> GetActivationStatusAsync(string orderId);

        // Payment Integration
        //Task<ApiResult<string>> CreatePaymentUrlAsync(string orderId, string paymentMethod);
        //Task<ApiResult<bool>> ConfirmPaymentAsync(string orderId, string transactionId);

        // Admin/Monitoring
        //Task<ApiResult<PagedResult<ESimLogVM>>> GetLogsAsync(ESimLogSearchRequest request);
        //Task<ApiResult<ESimStatsVM>> GetStatsAsync(DateTime fromDate, DateTime toDate);
    }
}
