﻿using NgocMaiTravel.ApiIntegration.Esim;
using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.ApiIntegration.ESimBlueAPI
{
    public interface IESimBlueAPIClient
    {
        Task<ApiResult<BalanceRp>> GetBalanceAsync();
        // Search and Browse
        Task<ApiResult<EsimPackagesRp>> GetAllDataPackagesAsync(EsimPackagesRq rq);
        Task<ApiResult<EsimProflieRp>> GetEsimProflieAsync(string serial);
    }
}
