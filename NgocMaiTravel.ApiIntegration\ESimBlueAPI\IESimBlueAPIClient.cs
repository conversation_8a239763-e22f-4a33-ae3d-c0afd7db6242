﻿using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.ApiIntegration.ESimBlueAPI
{
    public interface IESimBlueAPIClient
    {
        // Account Management
        Task<ApiResult<BalanceRp>> GetBalanceAsync();

        // Search and Browse
        Task<ApiResult<EsimPackagesRp>> GetAllDataPackagesAsync(EsimPackagesRq rq);

        // Profile Management
        Task<ApiResult<EsimProflieRp>> GetEsimProflieAsync(string serial);

        // Order Management
        Task<ApiResult<EsimOrderRp>> CreateOrderAsync(EsimOrderRq orderRequest);

        // ESim Activation & Usage
        Task<ApiResult<EsimUsageRp>> GetEsimUsageAsync(string serial);
        Task<ApiResult<EsimRedeemRp>> RedeemEsimAsync(string serial);

        // ESim Management
        Task<ApiResult<bool>> CancelEsimAsync(string iccid, string serial);
        Task<ApiResult<EsimTopUpRp>> TopUpEsimAsync(string serial, string packageSku);
    }
}
