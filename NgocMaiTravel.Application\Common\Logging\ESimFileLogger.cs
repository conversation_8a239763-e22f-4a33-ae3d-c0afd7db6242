using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities.ESim;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Common.Logging
{
    public class ESimFileLogger : IESimFileLogger
    {
        private readonly NgocMaiTravelDbContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly string _logBasePath;

        public ESimFileLogger(
            NgocMaiTravelDbContext context,
            IHttpContextAccessor httpContextAccessor,
            IConfiguration configuration)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
            
            // Get log path from configuration or use default
            _logBasePath = configuration["ESimLogging:BasePath"] ?? Path.Combine(Directory.GetCurrentDirectory(), "Logs", "ESim");
            
            // Ensure log directory exists
            Directory.CreateDirectory(_logBasePath);
        }

        public async Task<Guid> LogApiCallAsync(
            string action,
            string endpoint,
            string? requestData = null,
            string? responseData = null,
            string status = "pending",
            int? httpStatusCode = null,
            TimeSpan? duration = null,
            string? errorMessage = null,
            string? orderId = null,
            string? customerEmail = null,
            string? customerPhone = null,
            string? traceId = null,
            Guid? logId = null)
        {
            var actualLogId = logId ?? Guid.NewGuid();
            var timestamp = DateTime.Now;
            
            try
            {
                // Create log entry in database
                var log = new tblESimLog
                {
                    Id = actualLogId,
                    OrderId = orderId,
                    Action = action,
                    CustomerEmail = customerEmail,
                    CustomerPhone = customerPhone,
                    RequestData = "", // Will store file path instead
                    ResponseData = "", // Will store file path instead
                    Status = status,
                    ErrorMessage = errorMessage,
                    Timestamp = timestamp,
                    Duration = duration,
                    IpAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString(),
                    UserAgent = _httpContextAccessor.HttpContext?.Request?.Headers["User-Agent"].ToString(),
                    ApiEndpoint = endpoint,
                    HttpStatusCode = httpStatusCode,
                    TraceId = traceId ?? Guid.NewGuid().ToString("N"),
                    OwnerID = GetCurrentOwnerID()
                };

                // Save request data to file if provided
                if (!string.IsNullOrEmpty(requestData))
                {
                    var requestFilePath = await SaveToFileAsync(actualLogId, "request", requestData, timestamp);
                    log.RequestData = requestFilePath;
                }

                // Save response data to file if provided
                if (!string.IsNullOrEmpty(responseData))
                {
                    var responseFilePath = await SaveToFileAsync(actualLogId, "response", responseData, timestamp);
                    log.ResponseData = responseFilePath;
                }

                _context.tblESimLogs.Add(log);
                await _context.SaveChangesAsync();

                return actualLogId;
            }
            catch (Exception ex)
            {
                // Fallback: log to system logs if database fails
                global::System.Diagnostics.Debug.WriteLine($"Failed to log ESim API call: {ex.Message}");
                throw;
            }
        }

        public async Task UpdateLogAsync(
            Guid logId,
            string? responseData = null,
            string status = "success",
            int? httpStatusCode = null,
            TimeSpan? duration = null,
            string? errorMessage = null)
        {
            try
            {
                var log = await _context.tblESimLogs.FirstOrDefaultAsync(x => x.Id == logId);
                if (log == null)
                    return;

                // Update database fields
                log.Status = status;
                log.HttpStatusCode = httpStatusCode;
                log.Duration = duration;
                log.ErrorMessage = errorMessage;

                // Save response data to file if provided
                if (!string.IsNullOrEmpty(responseData))
                {
                    var responseFilePath = await SaveToFileAsync(logId, "response", responseData, log.Timestamp);
                    log.ResponseData = responseFilePath;
                }

                _context.tblESimLogs.Update(log);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                global::System.Diagnostics.Debug.WriteLine($"Failed to update ESim log: {ex.Message}");
                throw;
            }
        }

        public async Task<string?> ReadRequestDataAsync(Guid logId)
        {
            try
            {
                var log = await _context.tblESimLogs.FirstOrDefaultAsync(x => x.Id == logId);
                if (log == null || string.IsNullOrEmpty(log.RequestData))
                    return null;

                return await ReadFromFileAsync(log.RequestData);
            }
            catch (Exception ex)
            {
                global::System.Diagnostics.Debug.WriteLine($"Failed to read request data: {ex.Message}");
                return null;
            }
        }

        public async Task<string?> ReadResponseDataAsync(Guid logId)
        {
            try
            {
                var log = await _context.tblESimLogs.FirstOrDefaultAsync(x => x.Id == logId);
                if (log == null || string.IsNullOrEmpty(log.ResponseData))
                    return null;

                return await ReadFromFileAsync(log.ResponseData);
            }
            catch (Exception ex)
            {
                global::System.Diagnostics.Debug.WriteLine($"Failed to read response data: {ex.Message}");
                return null;
            }
        }

        public async Task CleanupOldLogsAsync(int olderThanDays = 30)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-olderThanDays);
                
                // Get old log entries
                var oldLogs = await _context.tblESimLogs
                    .Where(x => x.Timestamp < cutoffDate)
                    .ToListAsync();

                foreach (var log in oldLogs)
                {
                    // Delete request file
                    if (!string.IsNullOrEmpty(log.RequestData) && File.Exists(log.RequestData))
                    {
                        try { File.Delete(log.RequestData); } catch { }
                    }

                    // Delete response file
                    if (!string.IsNullOrEmpty(log.ResponseData) && File.Exists(log.ResponseData))
                    {
                        try { File.Delete(log.ResponseData); } catch { }
                    }
                }

                // Remove database entries
                _context.tblESimLogs.RemoveRange(oldLogs);
                await _context.SaveChangesAsync();

                // Clean up empty directories
                CleanupEmptyDirectories();
            }
            catch (Exception ex)
            {
                global::System.Diagnostics.Debug.WriteLine($"Failed to cleanup old logs: {ex.Message}");
                throw;
            }
        }

        private async Task<string> SaveToFileAsync(Guid logId, string type, string content, DateTime timestamp)
        {
            // Create directory structure: Logs/ESim/YYYY/MM/DD/
            var dateFolder = Path.Combine(_logBasePath, timestamp.ToString("yyyy"), timestamp.ToString("MM"), timestamp.ToString("dd"));
            Directory.CreateDirectory(dateFolder);

            // Create filename: {logId}_{type}_{timestamp}.json
            var fileName = $"{logId:N}_{type}_{timestamp:yyyyMMdd_HHmmss}.json";
            var filePath = Path.Combine(dateFolder, fileName);

            // Save content to file
            await File.WriteAllTextAsync(filePath, content);

            return filePath;
        }

        private async Task<string?> ReadFromFileAsync(string filePath)
        {
            if (!File.Exists(filePath))
                return null;

            return await File.ReadAllTextAsync(filePath);
        }

        private void CleanupEmptyDirectories()
        {
            try
            {
                var directories = Directory.GetDirectories(_logBasePath, "*", SearchOption.AllDirectories);
                foreach (var dir in directories)
                {
                    if (!Directory.EnumerateFileSystemEntries(dir).Any())
                    {
                        try { Directory.Delete(dir); } catch { }
                    }
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        private Guid? GetCurrentOwnerID()
        {
            try
            {
                var apiKey = _httpContextAccessor.HttpContext?.Request?.Headers["X-Api-Key"].ToString();
                if (!string.IsNullOrEmpty(apiKey))
                {
                    var ownerID = _context.tblApiKeyLibs.Where(t => t.XApiKey == apiKey).Select(t => t.Id).FirstOrDefault();
                    return ownerID != Guid.Empty ? ownerID : null;
                }
                return null;
            }
            catch
            {
                return null;
            }
        }
    }
}
