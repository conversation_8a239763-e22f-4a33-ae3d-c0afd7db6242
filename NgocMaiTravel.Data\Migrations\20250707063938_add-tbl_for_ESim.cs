﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace NgocMaiTravel.Data.Migrations
{
    /// <inheritdoc />
    public partial class addtbl_for_ESim : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("0468ad51-08c4-495c-bbcd-51adce08164a"));

            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("7ca0f1fa-a22c-4c42-af6b-f3bd4a1f7571"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedDate",
                table: "AppLockUserHistories",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 7, 13, 39, 37, 433, DateTimeKind.Local).AddTicks(4442),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 7, 5, 13, 53, 26, 960, DateTimeKind.Local).AddTicks(5866));

            migrationBuilder.CreateTable(
                name: "tblESimLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWID()"),
                    OrderId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    PlanId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Action = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CustomerEmail = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CustomerPhone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    RequestData = table.Column<string>(type: "ntext", nullable: false),
                    ResponseData = table.Column<string>(type: "ntext", nullable: true),
                    Status = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ErrorMessage = table.Column<string>(type: "ntext", nullable: true),
                    Timestamp = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    IpAddress = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    UserAgent = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Duration = table.Column<TimeSpan>(type: "time", nullable: true),
                    ApiEndpoint = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    HttpStatusCode = table.Column<int>(type: "int", nullable: true),
                    OwnerID = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    SessionId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    TraceId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tblESimLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "tblESimOrders",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false, defaultValueSql: "NEWID()"),
                    OrderId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    PlanId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    PlanName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Country = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CountryCode = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false),
                    Region = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    DataAmount = table.Column<int>(type: "int", nullable: false),
                    IsUnlimited = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    ValidityDays = table.Column<int>(type: "int", nullable: false),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Currency = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, defaultValue: "VND"),
                    Provider = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CustomerName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    CustomerEmail = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    CustomerPhone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Status = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "pending"),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    PaidAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ActivatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExpiresAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    QrCode = table.Column<string>(type: "ntext", nullable: true),
                    ActivationCode = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Instructions = table.Column<string>(type: "ntext", nullable: true),
                    PaymentMethod = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PaymentTransactionId = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    PaymentUrl = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    TotalAmount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Notes = table.Column<string>(type: "ntext", nullable: true),
                    DeviceInfo = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ErrorMessage = table.Column<string>(type: "ntext", nullable: true),
                    CreatedBy = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    UpdatedBy = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    OwnerID = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_tblESimOrders", x => x.Id);
                });

            migrationBuilder.InsertData(
                table: "AppRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "Description", "Name", "NormalizedName", "TimeCreate", "UserCreateID" },
                values: new object[,]
                {
                    { new Guid("92524a24-1d5d-44f4-a385-56065626dc8f"), null, "Client role", "client", "client", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null },
                    { new Guid("c3f4ea5f-6961-4422-926a-9daee6a14a22"), null, "Employee role", "employee", "employee", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null }
                });

            migrationBuilder.UpdateData(
                table: "AppUsers",
                keyColumn: "Id",
                keyValue: new Guid("69bd714f-9576-45ba-b5b7-f00649be00de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "5e0d8223-a1e4-4e91-857b-861468dcdfdc", "AQAAAAIAAYagAAAAENmDL7DRsrLNduhJeGpvC8ZPMSWnpdTEm94RpLbOSXglArD2lygFYy017qtHL6FoRw==" });

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab53"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 7, 13, 39, 37, 481, DateTimeKind.Local).AddTicks(4342));

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab54"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 7, 13, 39, 37, 481, DateTimeKind.Local).AddTicks(4373));

            migrationBuilder.CreateIndex(
                name: "IX_tblESimLogs_Action",
                table: "tblESimLogs",
                column: "Action");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimLogs_Action_Status",
                table: "tblESimLogs",
                columns: new[] { "Action", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_tblESimLogs_CustomerEmail",
                table: "tblESimLogs",
                column: "CustomerEmail");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimLogs_CustomerEmail_Action",
                table: "tblESimLogs",
                columns: new[] { "CustomerEmail", "Action" });

            migrationBuilder.CreateIndex(
                name: "IX_tblESimLogs_CustomerPhone",
                table: "tblESimLogs",
                column: "CustomerPhone");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimLogs_OrderId",
                table: "tblESimLogs",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimLogs_OrderId_Action",
                table: "tblESimLogs",
                columns: new[] { "OrderId", "Action" });

            migrationBuilder.CreateIndex(
                name: "IX_tblESimLogs_OwnerID",
                table: "tblESimLogs",
                column: "OwnerID");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimLogs_Status",
                table: "tblESimLogs",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimLogs_Timestamp",
                table: "tblESimLogs",
                column: "Timestamp");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimLogs_Timestamp_Action",
                table: "tblESimLogs",
                columns: new[] { "Timestamp", "Action" });

            migrationBuilder.CreateIndex(
                name: "IX_tblESimLogs_TraceId",
                table: "tblESimLogs",
                column: "TraceId");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimOrders_CreatedAt",
                table: "tblESimOrders",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimOrders_CreatedAt_Status",
                table: "tblESimOrders",
                columns: new[] { "CreatedAt", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_tblESimOrders_CustomerEmail",
                table: "tblESimOrders",
                column: "CustomerEmail");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimOrders_CustomerEmail_Status",
                table: "tblESimOrders",
                columns: new[] { "CustomerEmail", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_tblESimOrders_CustomerPhone",
                table: "tblESimOrders",
                column: "CustomerPhone");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimOrders_OrderId",
                table: "tblESimOrders",
                column: "OrderId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_tblESimOrders_OwnerID",
                table: "tblESimOrders",
                column: "OwnerID");

            migrationBuilder.CreateIndex(
                name: "IX_tblESimOrders_Status",
                table: "tblESimOrders",
                column: "Status");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "tblESimLogs");

            migrationBuilder.DropTable(
                name: "tblESimOrders");

            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("92524a24-1d5d-44f4-a385-56065626dc8f"));

            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("c3f4ea5f-6961-4422-926a-9daee6a14a22"));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedDate",
                table: "AppLockUserHistories",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 5, 13, 53, 26, 960, DateTimeKind.Local).AddTicks(5866),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 7, 7, 13, 39, 37, 433, DateTimeKind.Local).AddTicks(4442));

            migrationBuilder.InsertData(
                table: "AppRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "Description", "Name", "NormalizedName", "TimeCreate", "UserCreateID" },
                values: new object[,]
                {
                    { new Guid("0468ad51-08c4-495c-bbcd-51adce08164a"), null, "Employee role", "employee", "employee", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null },
                    { new Guid("7ca0f1fa-a22c-4c42-af6b-f3bd4a1f7571"), null, "Client role", "client", "client", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null }
                });

            migrationBuilder.UpdateData(
                table: "AppUsers",
                keyColumn: "Id",
                keyValue: new Guid("69bd714f-9576-45ba-b5b7-f00649be00de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "2561198b-3558-41d4-ae24-92f4979fd02e", "AQAAAAIAAYagAAAAECA1eeToLn58Fie0ihR1CJDRaK/xlrZONjo2Ky/YwKIKyhOlMCAhi4BeGEkRwkUJrQ==" });

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab53"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 5, 13, 53, 27, 8, DateTimeKind.Local).AddTicks(5317));

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab54"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 5, 13, 53, 27, 8, DateTimeKind.Local).AddTicks(5354));
        }
    }
}
