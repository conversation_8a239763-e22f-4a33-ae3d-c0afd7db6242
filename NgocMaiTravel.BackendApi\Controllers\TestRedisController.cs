using Microsoft.AspNetCore.Mvc;
using NgocMaiTravel.Application.Common.Services;
using System;
using System.Threading.Tasks;

namespace NgocMaiTravel.BackendApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TestRedisController : ControllerBase
    {
        private readonly IRequestDeduplicationService _deduplicationService;

        public TestRedisController(IRequestDeduplicationService deduplicationService)
        {
            _deduplicationService = deduplicationService ?? throw new ArgumentNullException(nameof(deduplicationService));
        }

        /// <summary>
        /// Test Redis-based request deduplication
        /// </summary>
        /// <param name="key">Test key</param>
        /// <param name="delay">Simulated processing delay in milliseconds</param>
        /// <returns>Test result</returns>
        [HttpGet("test-deduplication")]
        public async Task<IActionResult> TestDeduplication([FromQuery] string key = "test-key", [FromQuery] int delay = 2000)
        {
            var result = await _deduplicationService.ExecuteAsync(key, async () =>
            {
                // Simulate some work
                await Task.Delay(delay);
                
                return new
                {
                    Message = "Task completed",
                    Timestamp = DateTime.UtcNow,
                    ProcessingTime = delay,
                    Key = key
                };
            }, cacheDurationSeconds: 60);

            return Ok(new
            {
                Success = true,
                Data = result,
                Note = "If you call this endpoint multiple times with the same key within 60 seconds, you should get cached results"
            });
        }

        /// <summary>
        /// Test concurrent requests to same key
        /// </summary>
        /// <param name="key">Test key</param>
        /// <returns>Test result</returns>
        [HttpGet("test-concurrent")]
        public async Task<IActionResult> TestConcurrentRequests([FromQuery] string key = "concurrent-test")
        {
            var tasks = new Task<object>[5];
            
            for (int i = 0; i < 5; i++)
            {
                var taskId = i;
                tasks[i] = _deduplicationService.ExecuteAsync($"{key}-{taskId}", async () =>
                {
                    // Simulate work
                    await Task.Delay(1000);
                    
                    return new
                    {
                        TaskId = taskId,
                        Message = $"Task {taskId} completed",
                        Timestamp = DateTime.UtcNow
                    };
                }, cacheDurationSeconds: 30);
            }

            var results = await Task.WhenAll(tasks);

            return Ok(new
            {
                Success = true,
                Results = results,
                Note = "All tasks should complete independently since they have different keys"
            });
        }

        /// <summary>
        /// Get cache statistics
        /// </summary>
        /// <returns>Cache statistics</returns>
        [HttpGet("stats")]
        public IActionResult GetCacheStats()
        {
            var stats = _deduplicationService.GetCacheStats();
            
            return Ok(new
            {
                Success = true,
                Stats = stats,
                Note = "These statistics show Redis-based cache performance"
            });
        }

        /// <summary>
        /// Clear specific cache key
        /// </summary>
        /// <param name="key">Key to clear</param>
        /// <returns>Clear result</returns>
        [HttpDelete("clear/{key}")]
        public IActionResult ClearCache(string key)
        {
            _deduplicationService.ClearCache(key);
            
            return Ok(new
            {
                Success = true,
                Message = $"Cache cleared for key: {key}",
                Note = "The key has been removed from Redis cache"
            });
        }

        /// <summary>
        /// Clear all cache entries
        /// </summary>
        /// <returns>Clear result</returns>
        [HttpDelete("clear-all")]
        public IActionResult ClearAllCache()
        {
            _deduplicationService.ClearAllCache();
            
            return Ok(new
            {
                Success = true,
                Message = "All cache entries cleared",
                Note = "All deduplication cache entries have been removed from Redis"
            });
        }

        /// <summary>
        /// Test Redis connection and basic operations
        /// </summary>
        /// <returns>Connection test result</returns>
        [HttpGet("test-connection")]
        public async Task<IActionResult> TestRedisConnection()
        {
            try
            {
                // Test basic cache operation
                var testKey = $"connection-test-{DateTime.UtcNow.Ticks}";
                
                var result = await _deduplicationService.ExecuteAsync(testKey, async () =>
                {
                    await Task.Delay(100);
                    return new
                    {
                        Message = "Redis connection test successful",
                        Timestamp = DateTime.UtcNow,
                        TestKey = testKey
                    };
                }, cacheDurationSeconds: 10);

                // Test cache hit
                var cachedResult = await _deduplicationService.ExecuteAsync(testKey, async () =>
                {
                    await Task.Delay(100);
                    return new
                    {
                        Message = "This should not be executed if cache works",
                        Timestamp = DateTime.UtcNow,
                        TestKey = testKey
                    };
                }, cacheDurationSeconds: 10);

                return Ok(new
                {
                    Success = true,
                    FirstCall = result,
                    SecondCall = cachedResult,
                    CacheWorking = result.Equals(cachedResult),
                    Note = "If cache is working, both calls should return identical results"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Redis connection or operation failed"
                });
            }
        }

        /// <summary>
        /// Cleanup expired semaphores (maintenance endpoint)
        /// </summary>
        /// <returns>Cleanup result</returns>
        [HttpPost("cleanup")]
        public IActionResult CleanupExpiredSemaphores()
        {
            try
            {
                _deduplicationService.CleanupExpiredSemaphores();
                
                return Ok(new
                {
                    Success = true,
                    Message = "Cleanup completed",
                    Note = "Expired semaphores have been cleaned up"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message
                });
            }
        }
    }
}
