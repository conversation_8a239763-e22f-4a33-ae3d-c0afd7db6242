using Microsoft.AspNetCore.Mvc;
using NgocMaiTravel.Application.Common.Services;
using System;
using System.Threading.Tasks;

namespace NgocMaiTravel.BackendApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TestRedisController : ControllerBase
    {
        private readonly IRequestDeduplicationService _deduplicationService;

        public TestRedisController(IRequestDeduplicationService deduplicationService)
        {
            _deduplicationService = deduplicationService ?? throw new ArgumentNullException(nameof(deduplicationService));
        }

        /// <summary>
        /// Test Redis-based request deduplication
        /// </summary>
        /// <param name="key">Test key</param>
        /// <param name="delay">Simulated processing delay in milliseconds</param>
        /// <returns>Test result</returns>
        [HttpGet("test-deduplication")]
        public async Task<IActionResult> TestDeduplication([FromQuery] string key = "test-key", [FromQuery] int delay = 2000)
        {
            var result = await _deduplicationService.ExecuteAsync(key, async () =>
            {
                // Simulate some work
                await Task.Delay(delay);

                return new DeduplicationTestResult
                {
                    Message = "Task completed",
                    Timestamp = DateTime.UtcNow,
                    ProcessingTime = delay,
                    Key = key
                };
            }, cacheDurationSeconds: 60);

            return Ok(new
            {
                Success = true,
                Data = result,
                Note = "If you call this endpoint multiple times with the same key within 60 seconds, you should get cached results"
            });
        }

        /// <summary>
        /// Test concurrent requests to same key
        /// </summary>
        /// <param name="key">Test key</param>
        /// <returns>Test result</returns>
        [HttpGet("test-concurrent")]
        public async Task<IActionResult> TestConcurrentRequests([FromQuery] string key = "concurrent-test")
        {
            var tasks = new List<Task<ConcurrentTestResult>>();

            for (int i = 0; i < 5; i++)
            {
                var taskId = i;
                var task = _deduplicationService.ExecuteAsync($"{key}-{taskId}", async () =>
                {
                    // Simulate work
                    await Task.Delay(1000);

                    return new ConcurrentTestResult
                    {
                        TaskId = taskId,
                        Message = $"Task {taskId} completed",
                        Timestamp = DateTime.UtcNow
                    };
                }, cacheDurationSeconds: 30);

                tasks.Add(task);
            }

            var results = await Task.WhenAll(tasks);

            return Ok(new
            {
                Success = true,
                Results = results,
                Note = "All tasks should complete independently since they have different keys"
            });
        }

        /// <summary>
        /// Get cache statistics
        /// </summary>
        /// <returns>Cache statistics</returns>
        [HttpGet("stats")]
        public IActionResult GetCacheStats()
        {
            var stats = _deduplicationService.GetCacheStats();
            
            return Ok(new
            {
                Success = true,
                Stats = stats,
                Note = "These statistics show Redis-based cache performance"
            });
        }

        /// <summary>
        /// Clear specific cache key
        /// </summary>
        /// <param name="key">Key to clear</param>
        /// <returns>Clear result</returns>
        [HttpDelete("clear/{key}")]
        public IActionResult ClearCache(string key)
        {
            _deduplicationService.ClearCache(key);
            
            return Ok(new
            {
                Success = true,
                Message = $"Cache cleared for key: {key}",
                Note = "The key has been removed from Redis cache"
            });
        }

        /// <summary>
        /// Clear all cache entries
        /// </summary>
        /// <returns>Clear result</returns>
        [HttpDelete("clear-all")]
        public IActionResult ClearAllCache()
        {
            _deduplicationService.ClearAllCache();
            
            return Ok(new
            {
                Success = true,
                Message = "All cache entries cleared",
                Note = "All deduplication cache entries have been removed from Redis"
            });
        }

        /// <summary>
        /// Test Redis connection and basic operations
        /// </summary>
        /// <returns>Connection test result</returns>
        [HttpGet("test-connection")]
        public async Task<IActionResult> TestRedisConnection()
        {
            try
            {
                // Test basic cache operation
                var testKey = $"connection-test-{DateTime.UtcNow.Ticks}";
                
                var result = await _deduplicationService.ExecuteAsync(testKey, async () =>
                {
                    await Task.Delay(100);
                    return new ConnectionTestResult
                    {
                        Message = "Redis connection test successful",
                        Timestamp = DateTime.UtcNow,
                        TestKey = testKey
                    };
                }, cacheDurationSeconds: 10);

                // Test cache hit
                var cachedResult = await _deduplicationService.ExecuteAsync(testKey, async () =>
                {
                    await Task.Delay(100);
                    return new ConnectionTestResult
                    {
                        Message = "This should not be executed if cache works",
                        Timestamp = DateTime.UtcNow,
                        TestKey = testKey
                    };
                }, cacheDurationSeconds: 10);

                return Ok(new
                {
                    Success = true,
                    FirstCall = result,
                    SecondCall = cachedResult,
                    CacheWorking = result.Equals(cachedResult),
                    Note = "If cache is working, both calls should return identical results"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Redis connection or operation failed"
                });
            }
        }

        /// <summary>
        /// Cleanup expired semaphores (maintenance endpoint)
        /// </summary>
        /// <returns>Cleanup result</returns>
        [HttpPost("cleanup")]
        public IActionResult CleanupExpiredSemaphores()
        {
            try
            {
                _deduplicationService.CleanupExpiredSemaphores();
                
                return Ok(new
                {
                    Success = true,
                    Message = "Cleanup completed",
                    Note = "Expired semaphores have been cleaned up"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// Test ESim order creation with new API format
        /// </summary>
        /// <param name="sku">Package SKU</param>
        /// <param name="quantity">Quantity to order</param>
        /// <returns>Order creation test result</returns>
        [HttpPost("test-esim-order")]
        public async Task<IActionResult> TestESimOrder([FromQuery] string sku = "BLC-01-JP-moshi-moshi-7days-1gb", [FromQuery] int quantity = 1)
        {
            try
            {
                var requestId = Guid.NewGuid().ToString("N");

                // Create test order request in ESimBlue API format
                var orderRequest = new NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI.EsimOrderRq
                {
                    RequestId = requestId,
                    PackageInfoList = new List<NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI.PackageInfo>
                    {
                        new NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI.PackageInfo
                        {
                            Sku = sku,
                            Quantity = quantity
                        }
                    },
                    CustomerEmail = "<EMAIL>",
                    CustomerPhone = "+84123456789",
                    CustomerName = "Test Customer"
                };

                return Ok(new
                {
                    Success = true,
                    Message = "ESim order request format test",
                    RequestFormat = orderRequest,
                    ExpectedResponse = new
                    {
                        OrderPublicId = "167b2807-0bf4-45f1-85dd-3e2d186b4bab",
                        SerialList = new[]
                        {
                            new
                            {
                                Serial = "TMU7TE2ZFI",
                                ProductId = 20,
                                Status = 0,
                                EsimPublicId = "5e9d40a2-3c6d-40f1-912f-dc89a356b633"
                            }
                        }
                    },
                    Note = "This shows the correct request/response format for ESimBlue API"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Error creating test ESim order request"
                });
            }
        }

        /// <summary>
        /// Test ESim redeem API format
        /// </summary>
        /// <param name="serial">ESim serial number</param>
        /// <returns>Redeem API format test result</returns>
        [HttpPost("test-esim-redeem")]
        public IActionResult TestESimRedeem([FromQuery] string serial = "GX28D0X493")
        {
            try
            {
                return Ok(new
                {
                    Success = true,
                    Message = "ESim redeem API format test",
                    RequestFormat = new
                    {
                        Method = "POST",
                        Endpoint = $"/eip/partner/esim/{serial}/redeem",
                        Body = "Empty body"
                    },
                    SuccessResponse = new
                    {
                        OrderPublicId = "bb559e4f-6b70-498f-939a-9da767049ca6",
                        Serial = "GX28D0X493",
                        Esim = new
                        {
                            PackageId = 25,
                            Sku = "BLC-01-JP-moshi-moshi-7days-1gb",
                            PackageName = "1 GB - 7 Days",
                            Model = "moshi-moshi-7days-1gb",
                            Slug = "japan",
                            DurationUnit = "DAY",
                            Location = "JP",
                            ActiveType = 1,
                            SupportTopUpType = 1,
                            Imsi = (string?)null,
                            Iccid = "894000000000058815",
                            Ac = "LPA:1$lpa.airalo.com$TEST",
                            QrCodeUrl = "https://sandbox.airalo.com/qr?expires=1826683712&id=262303&signature=cbfd8900ad3957b8ec590b1d14149fd0cf97b6c394211e8bfe8b3509f9bb1652",
                            ShortUrl = "https://esimsetup.apple.com/esim_qrcode_provisioning?carddata=LPA:1$lpa.airalo.com$TEST",
                            Apn = (string?)null
                        }
                    },
                    ErrorResponse = new[]
                    {
                        new
                        {
                            Name = "SERIAL",
                            Code = "RESOURCE_NOT_FOUND",
                            Message = "Serial not found"
                        }
                    },
                    Note = "This shows the correct request/response format for ESimBlue redeem API"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Error creating test ESim redeem request"
                });
            }
        }
    }

    // === SUPPORT MODELS ===

    public class ConcurrentTestResult
    {
        public int TaskId { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    public class ConnectionTestResult
    {
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string TestKey { get; set; } = string.Empty;
    }

    public class DeduplicationTestResult
    {
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public int ProcessingTime { get; set; }
        public string Key { get; set; } = string.Empty;
    }
}
