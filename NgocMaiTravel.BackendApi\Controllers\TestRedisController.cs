using Microsoft.AspNetCore.Mvc;
using NgocMaiTravel.Application.Common.Services;
using System;
using System.Threading.Tasks;

namespace NgocMaiTravel.BackendApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TestRedisController : ControllerBase
    {
        private readonly IRequestDeduplicationService _deduplicationService;

        public TestRedisController(IRequestDeduplicationService deduplicationService)
        {
            _deduplicationService = deduplicationService ?? throw new ArgumentNullException(nameof(deduplicationService));
        }

        /// <summary>
        /// Test Redis-based request deduplication
        /// </summary>
        /// <param name="key">Test key</param>
        /// <param name="delay">Simulated processing delay in milliseconds</param>
        /// <returns>Test result</returns>
        [HttpGet("test-deduplication")]
        public async Task<IActionResult> TestDeduplication([FromQuery] string key = "test-key", [FromQuery] int delay = 2000)
        {
            var result = await _deduplicationService.ExecuteAsync(key, async () =>
            {
                // Simulate some work
                await Task.Delay(delay);

                return new DeduplicationTestResult
                {
                    Message = "Task completed",
                    Timestamp = DateTime.UtcNow,
                    ProcessingTime = delay,
                    Key = key
                };
            }, cacheDurationSeconds: 60);

            return Ok(new
            {
                Success = true,
                Data = result,
                Note = "If you call this endpoint multiple times with the same key within 60 seconds, you should get cached results"
            });
        }

        /// <summary>
        /// Test concurrent requests to same key
        /// </summary>
        /// <param name="key">Test key</param>
        /// <returns>Test result</returns>
        [HttpGet("test-concurrent")]
        public async Task<IActionResult> TestConcurrentRequests([FromQuery] string key = "concurrent-test")
        {
            var tasks = new List<Task<ConcurrentTestResult>>();

            for (int i = 0; i < 5; i++)
            {
                var taskId = i;
                var task = _deduplicationService.ExecuteAsync($"{key}-{taskId}", async () =>
                {
                    // Simulate work
                    await Task.Delay(1000);

                    return new ConcurrentTestResult
                    {
                        TaskId = taskId,
                        Message = $"Task {taskId} completed",
                        Timestamp = DateTime.UtcNow
                    };
                }, cacheDurationSeconds: 30);

                tasks.Add(task);
            }

            var results = await Task.WhenAll(tasks);

            return Ok(new
            {
                Success = true,
                Results = results,
                Note = "All tasks should complete independently since they have different keys"
            });
        }

        /// <summary>
        /// Get cache statistics
        /// </summary>
        /// <returns>Cache statistics</returns>
        [HttpGet("stats")]
        public IActionResult GetCacheStats()
        {
            var stats = _deduplicationService.GetCacheStats();
            
            return Ok(new
            {
                Success = true,
                Stats = stats,
                Note = "These statistics show Redis-based cache performance"
            });
        }

        /// <summary>
        /// Clear specific cache key
        /// </summary>
        /// <param name="key">Key to clear</param>
        /// <returns>Clear result</returns>
        [HttpDelete("clear/{key}")]
        public IActionResult ClearCache(string key)
        {
            _deduplicationService.ClearCache(key);
            
            return Ok(new
            {
                Success = true,
                Message = $"Cache cleared for key: {key}",
                Note = "The key has been removed from Redis cache"
            });
        }

        /// <summary>
        /// Clear all cache entries
        /// </summary>
        /// <returns>Clear result</returns>
        [HttpDelete("clear-all")]
        public IActionResult ClearAllCache()
        {
            _deduplicationService.ClearAllCache();
            
            return Ok(new
            {
                Success = true,
                Message = "All cache entries cleared",
                Note = "All deduplication cache entries have been removed from Redis"
            });
        }

        /// <summary>
        /// Test Redis connection and basic operations
        /// </summary>
        /// <returns>Connection test result</returns>
        [HttpGet("test-connection")]
        public async Task<IActionResult> TestRedisConnection()
        {
            try
            {
                // Test basic cache operation
                var testKey = $"connection-test-{DateTime.UtcNow.Ticks}";
                
                var result = await _deduplicationService.ExecuteAsync(testKey, async () =>
                {
                    await Task.Delay(100);
                    return new ConnectionTestResult
                    {
                        Message = "Redis connection test successful",
                        Timestamp = DateTime.UtcNow,
                        TestKey = testKey
                    };
                }, cacheDurationSeconds: 10);

                // Test cache hit
                var cachedResult = await _deduplicationService.ExecuteAsync(testKey, async () =>
                {
                    await Task.Delay(100);
                    return new ConnectionTestResult
                    {
                        Message = "This should not be executed if cache works",
                        Timestamp = DateTime.UtcNow,
                        TestKey = testKey
                    };
                }, cacheDurationSeconds: 10);

                return Ok(new
                {
                    Success = true,
                    FirstCall = result,
                    SecondCall = cachedResult,
                    CacheWorking = result.Equals(cachedResult),
                    Note = "If cache is working, both calls should return identical results"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Redis connection or operation failed"
                });
            }
        }

        /// <summary>
        /// Cleanup expired semaphores (maintenance endpoint)
        /// </summary>
        /// <returns>Cleanup result</returns>
        [HttpPost("cleanup")]
        public IActionResult CleanupExpiredSemaphores()
        {
            try
            {
                _deduplicationService.CleanupExpiredSemaphores();
                
                return Ok(new
                {
                    Success = true,
                    Message = "Cleanup completed",
                    Note = "Expired semaphores have been cleaned up"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// Test ESim order creation with new API format
        /// </summary>
        /// <param name="sku">Package SKU</param>
        /// <param name="quantity">Quantity to order</param>
        /// <returns>Order creation test result</returns>
        [HttpPost("test-esim-order")]
        public async Task<IActionResult> TestESimOrder([FromQuery] string sku = "BLC-01-JP-moshi-moshi-7days-1gb", [FromQuery] int quantity = 1)
        {
            try
            {
                var requestId = Guid.NewGuid().ToString("N");

                // Create test order request in ESimBlue API format
                var orderRequest = new NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI.EsimOrderRq
                {
                    RequestId = requestId,
                    PackageInfoList = new List<NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI.PackageInfo>
                    {
                        new NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI.PackageInfo
                        {
                            Sku = sku,
                            Quantity = quantity
                        }
                    },
                    CustomerEmail = "<EMAIL>",
                    CustomerPhone = "+84123456789",
                    CustomerName = "Test Customer"
                };

                return Ok(new
                {
                    Success = true,
                    Message = "ESim order request format test",
                    RequestFormat = orderRequest,
                    ExpectedResponse = new
                    {
                        OrderPublicId = "167b2807-0bf4-45f1-85dd-3e2d186b4bab",
                        SerialList = new[]
                        {
                            new
                            {
                                Serial = "TMU7TE2ZFI",
                                ProductId = 20,
                                Status = 0,
                                EsimPublicId = "5e9d40a2-3c6d-40f1-912f-dc89a356b633"
                            }
                        }
                    },
                    Note = "This shows the correct request/response format for ESimBlue API"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Error creating test ESim order request"
                });
            }
        }

        /// <summary>
        /// Test ESim redeem API format
        /// </summary>
        /// <param name="serial">ESim serial number</param>
        /// <returns>Redeem API format test result</returns>
        [HttpPost("test-esim-redeem")]
        public IActionResult TestESimRedeem([FromQuery] string serial = "GX28D0X493")
        {
            try
            {
                return Ok(new
                {
                    Success = true,
                    Message = "ESim redeem API format test",
                    RequestFormat = new
                    {
                        Method = "POST",
                        Endpoint = $"/eip/partner/esim/{serial}/redeem",
                        Body = "Empty body"
                    },
                    SuccessResponse = new
                    {
                        OrderPublicId = "bb559e4f-6b70-498f-939a-9da767049ca6",
                        Serial = "GX28D0X493",
                        Esim = new
                        {
                            PackageId = 25,
                            Sku = "BLC-01-JP-moshi-moshi-7days-1gb",
                            PackageName = "1 GB - 7 Days",
                            Model = "moshi-moshi-7days-1gb",
                            Slug = "japan",
                            DurationUnit = "DAY",
                            Location = "JP",
                            ActiveType = 1,
                            SupportTopUpType = 1,
                            Imsi = (string?)null,
                            Iccid = "894000000000058815",
                            Ac = "LPA:1$lpa.airalo.com$TEST",
                            QrCodeUrl = "https://sandbox.airalo.com/qr?expires=1826683712&id=262303&signature=cbfd8900ad3957b8ec590b1d14149fd0cf97b6c394211e8bfe8b3509f9bb1652",
                            ShortUrl = "https://esimsetup.apple.com/esim_qrcode_provisioning?carddata=LPA:1$lpa.airalo.com$TEST",
                            Apn = (string?)null
                        }
                    },
                    ErrorResponse = new[]
                    {
                        new
                        {
                            Name = "SERIAL",
                            Code = "RESOURCE_NOT_FOUND",
                            Message = "Serial not found"
                        }
                    },
                    Note = "This shows the correct request/response format for ESimBlue redeem API"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Error creating test ESim redeem request"
                });
            }
        }

        /// <summary>
        /// Test response format detection logic
        /// </summary>
        /// <param name="responseType">Type of response to simulate (success/error)</param>
        /// <returns>Response format detection test</returns>
        [HttpGet("test-response-detection")]
        public IActionResult TestResponseDetection([FromQuery] string responseType = "success")
        {
            try
            {
                string simulatedResponse;
                bool expectedIsSuccess;

                if (responseType.ToLower() == "error")
                {
                    // Simulate error response (array format)
                    simulatedResponse = @"[
                        {
                            ""name"": ""SERIAL"",
                            ""code"": ""RESOURCE_NOT_FOUND"",
                            ""message"": ""Serial not found""
                        }
                    ]";
                    expectedIsSuccess = false;
                }
                else
                {
                    // Simulate success response (object format)
                    simulatedResponse = @"{
                        ""orderPublicId"": ""bb559e4f-6b70-498f-939a-9da767049ca6"",
                        ""serial"": ""GX28D0X493"",
                        ""esim"": {
                            ""packageId"": 25,
                            ""sku"": ""BLC-01-JP-moshi-moshi-7days-1gb"",
                            ""packageName"": ""1 GB - 7 Days"",
                            ""iccid"": ""894000000000058815"",
                            ""ac"": ""LPA:1$lpa.airalo.com$TEST"",
                            ""qrCodeUrl"": ""https://sandbox.airalo.com/qr?expires=1826683712&id=262303&signature=test"",
                            ""shortUrl"": ""https://esimsetup.apple.com/esim_qrcode_provisioning?carddata=LPA:1$lpa.airalo.com$TEST""
                        }
                    }";
                    expectedIsSuccess = true;
                }

                var trimmedContent = simulatedResponse.Trim();
                var detectedFormat = trimmedContent.StartsWith('[') ? "error_array" :
                                   trimmedContent.StartsWith('{') ? "success_object" : "unknown";

                return Ok(new
                {
                    Success = true,
                    ResponseType = responseType,
                    SimulatedResponse = simulatedResponse,
                    DetectedFormat = detectedFormat,
                    ExpectedIsSuccess = expectedIsSuccess,
                    DetectionLogic = new
                    {
                        StartsWithBracket = trimmedContent.StartsWith('['),
                        StartsWithBrace = trimmedContent.StartsWith('{'),
                        FirstChar = trimmedContent.Length > 0 ? trimmedContent[0].ToString() : "empty"
                    },
                    Note = "This tests the response format detection logic for success vs error responses"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Error testing response detection"
                });
            }
        }

        /// <summary>
        /// Test ESim usage API format
        /// </summary>
        /// <param name="serial">ESim serial number</param>
        /// <returns>Usage API format test result</returns>
        [HttpPost("test-esim-usage")]
        public IActionResult TestESimUsage([FromQuery] string serial = "TL838TJMXS")
        {
            try
            {
                return Ok(new
                {
                    Success = true,
                    Message = "ESim usage API format test",
                    RequestFormat = new
                    {
                        Method = "GET",
                        Endpoint = $"/eip/partner/esim/{serial}/usage",
                        Body = "Empty body"
                    },
                    SuccessResponse = new
                    {
                        OrderPublicId = "f1b15787-2082-456b-9d80-80a14e2aa3c2",
                        Serial = "TL838TJMXS",
                        Remaining = 0,
                        Volume = 0,
                        ExpiredAt = "2025-03-13 04:01:02",
                        Status = "FINISHED"
                    },
                    ErrorResponse = new[]
                    {
                        new
                        {
                            Name = "SERIAL",
                            Code = "RESOURCE_NOT_FOUND",
                            Message = "Serial not found"
                        }
                    },
                    StatusMeanings = new
                    {
                        FINISHED = "ESim has expired or used all data",
                        ACTIVE = "ESim is currently active and usable",
                        SUSPENDED = "ESim is temporarily suspended",
                        CANCELLED = "ESim has been cancelled"
                    },
                    Note = "This shows the correct request/response format for ESimBlue usage API"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Error creating test ESim usage request"
                });
            }
        }

        /// <summary>
        /// Test all ESim API patterns
        /// </summary>
        /// <returns>API patterns summary</returns>
        [HttpGet("test-esim-patterns")]
        public IActionResult TestESimPatterns()
        {
            try
            {
                return Ok(new
                {
                    Success = true,
                    Message = "ESim API patterns summary",
                    ApiPatterns = new
                    {
                        AlwaysHttp200 = new
                        {
                            Description = "All ESimBlue APIs return HTTP 200 OK",
                            SuccessFormat = "JSON Object starting with '{'",
                            ErrorFormat = "JSON Array starting with '['",
                            AffectedAPIs = new[] { "redeem", "usage", "cancel", "topup", "profile(?)" }
                        },
                        StandardAPIs = new
                        {
                            Description = "Standard APIs that may use HTTP status codes",
                            APIs = new[] { "balance", "packages", "orders" },
                            Note = "These may follow normal HTTP status patterns"
                        }
                    },
                    TestEndpoints = new
                    {
                        RedeemFormat = "/api/TestRedis/test-esim-redeem",
                        UsageFormat = "/api/TestRedis/test-esim-usage",
                        CancelFormat = "/api/TestRedis/test-esim-cancel",
                        TopUpFormat = "/api/TestRedis/test-esim-topup",
                        OrderFormat = "/api/TestRedis/test-esim-order",
                        ResponseDetection = "/api/TestRedis/test-response-detection"
                    },
                    Note = "Use these endpoints to verify API format handling"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Error creating ESim patterns summary"
                });
            }
        }

        /// <summary>
        /// Test ESim cancel API format
        /// </summary>
        /// <param name="serial">ESim serial number</param>
        /// <param name="iccid">ESim ICCID</param>
        /// <returns>Cancel API format test result</returns>
        [HttpPost("test-esim-cancel")]
        public IActionResult TestESimCancel([FromQuery] string serial = "GX28D0X493", [FromQuery] string iccid = "")
        {
            try
            {
                return Ok(new
                {
                    Success = true,
                    Message = "ESim cancel API format test",
                    RequestFormat = new
                    {
                        Method = "POST",
                        Endpoint = "/eip/partner/esim/cancel",
                        Body = new
                        {
                            iccid = iccid,
                            serial = serial
                        }
                    },
                    SuccessResponse = true,
                    ErrorResponse = new[]
                    {
                        new
                        {
                            Name = "ESIM",
                            Code = "NOT_ACCEPTABLE",
                            Message = "eSIM is not in a refundable state."
                        }
                    },
                    CommonErrorCodes = new
                    {
                        NOT_ACCEPTABLE = "eSIM is not in a refundable state",
                        RESOURCE_NOT_FOUND = "Serial or ICCID not found",
                        ALREADY_CANCELLED = "eSIM has already been cancelled",
                        INVALID_STATE = "eSIM cannot be cancelled in current state"
                    },
                    Note = "This shows the correct request/response format for ESimBlue cancel API"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Error creating test ESim cancel request"
                });
            }
        }

        /// <summary>
        /// Test ESim topup API format
        /// </summary>
        /// <param name="serial">ESim serial number</param>
        /// <param name="sku">TopUp package SKU</param>
        /// <returns>TopUp API format test result</returns>
        [HttpPost("test-esim-topup")]
        public IActionResult TestESimTopUp([FromQuery] string serial = "GX28D0X493", [FromQuery] string sku = "BLC-01-JP-moshi-moshi-7days-1gb-topup")
        {
            try
            {
                return Ok(new
                {
                    Success = true,
                    Message = "ESim topup API format test",
                    RequestFormat = new
                    {
                        Method = "POST",
                        Endpoint = "/eip/partner/esim/topup",
                        Body = new
                        {
                            serial = serial,
                            sku = sku
                        }
                    },
                    SuccessResponse = new
                    {
                        OrderPublicId = "bb559e4f-6b70-498f-939a-9da767049ca6",
                        Serial = "GX28D0X493",
                        Remaining = 3996,
                        Volume = 4096,
                        ExpiredAt = "2025-03-03 05:01:02",
                        Status = "ACTIVE"
                    },
                    ErrorResponse = new[]
                    {
                        new
                        {
                            Name = "TOP_UP",
                            Code = "RESOURCE_NOT_FOUND",
                            Message = "Top up package not found"
                        }
                    },
                    CommonErrorCodes = new
                    {
                        RESOURCE_NOT_FOUND = "Top up package not found",
                        INVALID_SERIAL = "Serial number is invalid or not found",
                        PACKAGE_NOT_COMPATIBLE = "TopUp package is not compatible with this eSIM",
                        ALREADY_EXPIRED = "eSIM has already expired",
                        INSUFFICIENT_BALANCE = "Insufficient balance for topup"
                    },
                    Note = "This shows the correct request/response format for ESimBlue topup API"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Error creating test ESim topup request"
                });
            }
        }

        /// <summary>
        /// Test ESim caching mechanism
        /// </summary>
        /// <returns>Caching mechanism test result</returns>
        [HttpGet("test-esim-caching")]
        public async Task<IActionResult> TestESimCaching()
        {
            try
            {
                return Ok(new
                {
                    Success = true,
                    Message = "ESim caching mechanism test",
                    CachingFlow = new
                    {
                        Step1 = "Check if data exists in database",
                        Step2_NoData = "If no data: Fetch from API + Queue sync to DB",
                        Step2_HasData = "If has data: Return from DB + Queue background sync",
                        Step3 = "Background service processes sync queue",
                        Step4 = "Database updated with latest data"
                    },
                    DatabaseTables = new
                    {
                        tblESimGroup = "Countries/Regions (VN, JP, US, etc.)",
                        tblESimPackage = "Individual ESim packages",
                        tblESimGroupPackage = "Many-to-many mapping",
                        tblESimSyncLog = "Sync operation tracking"
                    },
                    CacheStrategy = new
                    {
                        FirstRequest = "API -> DB (with queue)",
                        SubsequentRequests = "DB -> Background API sync",
                        CacheExpiry = "6 hours",
                        BackgroundSync = "Every 1 hour",
                        QueueProcessing = "Async background service"
                    },
                    Benefits = new[]
                    {
                        "Fast response from database",
                        "Always up-to-date data via background sync",
                        "Reduced API calls",
                        "Better user experience",
                        "Fault tolerance (DB fallback)"
                    },
                    TestEndpoints = new
                    {
                        SearchPlans = "/api/ESim/search",
                        SyncStatus = "/api/ESim/sync-status",
                        ForceSync = "/api/ESim/force-sync",
                        CacheStats = "/api/ESim/cache-stats"
                    },
                    Note = "This shows the ESim caching and sync mechanism design"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Error creating ESim caching test"
                });
            }
        }

        /// <summary>
        /// Test ESim caching status
        /// </summary>
        /// <returns>Current caching status</returns>
        [HttpGet("test-esim-cache-status")]
        public IActionResult TestESimCacheStatus()
        {
            try
            {
                return Ok(new
                {
                    Success = true,
                    Message = "ESim caching status",
                    CurrentImplementation = new
                    {
                        DatabaseCaching = "✅ ACTIVE (full implementation)",
                        ApiCaching = "✅ ACTIVE (via deduplication service)",
                        BackgroundSync = "✅ ACTIVE (background service running)",
                        QueueProcessing = "✅ ACTIVE (queue processing enabled)"
                    },
                    CacheFlow = new
                    {
                        CurrentFlow = "✅ DB first → API fallback → Background sync",
                        ApiCaching = "12h deduplication cache",
                        DatabaseCaching = "6h expiry with background refresh",
                        BackgroundSync = "Every 1 hour",
                        QueueProcessing = "Async with retry logic"
                    },
                    CompletedSteps = new[]
                    {
                        "✅ Database migration completed",
                        "✅ Repository implementation completed",
                        "✅ Sync service implementation completed",
                        "✅ Services registered in DI container",
                        "✅ Background service enabled",
                        "✅ Full caching flow implemented"
                    },
                    TestCommands = new
                    {
                        SearchPlans = "GET /api/ESim/search?locationCode=VN&type=data",
                        CacheStatus = "GET /api/TestRedis/test-esim-cache-status",
                        CacheDesign = "GET /api/TestRedis/test-esim-caching"
                    },
                    ManagementEndpoints = new
                    {
                        CacheStats = "GET /api/ESimManagement/cache-stats",
                        SyncStatus = "GET /api/ESimManagement/sync-status",
                        ForceFullSync = "POST /api/ESimManagement/force-full-sync",
                        ForceIncrementalSync = "POST /api/ESimManagement/force-incremental-sync",
                        SyncLogs = "GET /api/ESimManagement/sync-logs",
                        DataSummary = "GET /api/ESimManagement/data-summary"
                    },
                    Note = "✅ Full ESim database caching system is now ACTIVE and operational!"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new
                {
                    Success = false,
                    Error = ex.Message,
                    Note = "Error getting ESim cache status"
                });
            }
        }
    }

    // === SUPPORT MODELS ===

    public class ConcurrentTestResult
    {
        public int TaskId { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }

    public class ConnectionTestResult
    {
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string TestKey { get; set; } = string.Empty;
    }

    public class DeduplicationTestResult
    {
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public int ProcessingTime { get; set; }
        public string Key { get; set; } = string.Empty;
    }
}
