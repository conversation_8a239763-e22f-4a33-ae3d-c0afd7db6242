using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Data.Entities.ESim
{
    public class tblESimLog
    {
        public Guid Id { get; set; }
        public string? OrderId { get; set; }
        public string? PlanId { get; set; }
        public string Action { get; set; } // search, order, payment, activate, status_check, etc.
        public string? CustomerEmail { get; set; }
        public string? CustomerPhone { get; set; }
        public string RequestData { get; set; } // File path to request data
        public string? ResponseData { get; set; } // File path to response data
        public string Status { get; set; } // success, error, pending
        public string? ErrorMessage { get; set; }
        public DateTime Timestamp { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public TimeSpan? Duration { get; set; } // Request duration
        public string? ApiEndpoint { get; set; } // ESimBlue endpoint called
        public int? HttpStatusCode { get; set; }
        public Guid? OwnerID { get; set; } // For API key tracking
        public string? SessionId { get; set; }
        public string? TraceId { get; set; } // For request tracing
    }
}
