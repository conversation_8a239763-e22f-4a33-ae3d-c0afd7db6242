using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using System.ComponentModel.DataAnnotations;

namespace NgocMaiTravel.ViewModels.Catalog.ESim
{
    /// <summary>
    /// Request model for ESim search with customer context for enhanced logging
    /// </summary>
    public class EsimSearchWithContextRequest
    {
        /// <summary>
        /// ESim search parameters
        /// </summary>
        [Required]
        public EsimPackagesRq SearchRequest { get; set; } = new EsimPackagesRq();

        /// <summary>
        /// Customer email for logging context
        /// </summary>
        public string? CustomerEmail { get; set; }

        /// <summary>
        /// Customer phone for logging context
        /// </summary>
        public string? CustomerPhone { get; set; }

        /// <summary>
        /// Additional context information
        /// </summary>
        public string? Context { get; set; }

        /// <summary>
        /// Source of the request (web, mobile, api, etc.)
        /// </summary>
        public string? Source { get; set; }
    }
}
