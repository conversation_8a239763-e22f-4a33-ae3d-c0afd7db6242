namespace NgocMaiTravel.Application.Catalog.ESim.Models
{
    /// <summary>
    /// ESim sync operation types
    /// </summary>
    public static class ESimSyncTypes
    {
        /// <summary>
        /// Full synchronization - sync all packages from API
        /// </summary>
        public const string FullSync = "FULL_SYNC";

        /// <summary>
        /// Incremental synchronization - sync only changed packages
        /// </summary>
        public const string IncrementalSync = "INCREMENTAL_SYNC";

        /// <summary>
        /// Group synchronization - sync packages for specific group/country
        /// </summary>
        public const string GroupSync = "GROUP_SYNC";

        /// <summary>
        /// Manual synchronization - triggered by user/admin
        /// </summary>
        public const string ManualSync = "MANUAL_SYNC";

        /// <summary>
        /// Scheduled synchronization - triggered by background service
        /// </summary>
        public const string ScheduledSync = "SCHEDULED_SYNC";
    }

    /// <summary>
    /// ESim sync status values
    /// </summary>
    public static class ESimSyncStatus
    {
        /// <summary>
        /// Sync operation is pending
        /// </summary>
        public const string Pending = "PENDING";

        /// <summary>
        /// Sync operation is currently running
        /// </summary>
        public const string Running = "RUNNING";

        /// <summary>
        /// Sync operation completed successfully
        /// </summary>
        public const string Success = "SUCCESS";

        /// <summary>
        /// Sync operation failed
        /// </summary>
        public const string Failed = "FAILED";

        /// <summary>
        /// Sync operation was cancelled
        /// </summary>
        public const string Cancelled = "CANCELLED";
    }
}
