using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NgocMaiTravel.Domain.Entities.ESim
{
    /// <summary>
    /// ESim Sync Log Entity
    /// </summary>
    [Table("tblESimSyncLog")]
    public class ESimSyncLog
    {
        [Key]
        public long Id { get; set; }

        [Required]
        [StringLength(50)]
        public string SyncType { get; set; } = string.Empty; // FULL_SYNC, INCREMENTAL_SYNC, GROUP_SYNC

        [Required]
        [StringLength(50)]
        public string Status { get; set; } = string.Empty; // PENDING, RUNNING, SUCCESS, FAILED

        public DateTime StartTime { get; set; } = DateTime.UtcNow;

        public DateTime? EndTime { get; set; }

        public int? Duration { get; set; } // Duration in milliseconds

        public int? TotalPackages { get; set; }

        public int? ProcessedPackages { get; set; }

        public int? NewPackages { get; set; }

        public int? UpdatedPackages { get; set; }

        public string? ErrorMessage { get; set; }

        [StringLength(255)]
        public string? RequestedBy { get; set; }

        [Required]
        [StringLength(50)]
        public string ApiSource { get; set; } = "ESimBlue";

        // Helper properties
        [NotMapped]
        public TimeSpan? ExecutionTime => EndTime.HasValue ? EndTime.Value - StartTime : null;

        [NotMapped]
        public bool IsCompleted => Status == ESimSyncStatus.Success || Status == ESimSyncStatus.Failed;

        [NotMapped]
        public bool IsRunning => Status == ESimSyncStatus.Running;

        [NotMapped]
        public double? SuccessRate => TotalPackages > 0 && ProcessedPackages.HasValue 
            ? (double)ProcessedPackages.Value / TotalPackages.Value * 100 
            : null;
    }

    /// <summary>
    /// ESim Sync Types
    /// </summary>
    public static class ESimSyncTypes
    {
        public const string FullSync = "FULL_SYNC";
        public const string IncrementalSync = "INCREMENTAL_SYNC";
        public const string GroupSync = "GROUP_SYNC";
    }

    /// <summary>
    /// ESim Sync Status
    /// </summary>
    public static class ESimSyncStatus
    {
        public const string Pending = "PENDING";
        public const string Running = "RUNNING";
        public const string Success = "SUCCESS";
        public const string Failed = "FAILED";
    }
}
