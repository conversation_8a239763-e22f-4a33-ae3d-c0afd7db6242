# ESim Usage API Format - ESimBlue Integration

## Overview

Updated ESim usage API models to match the actual ESimBlue API response format. Like the redeem API, the usage API also follows the same pattern of always returning HTTP 200 but with different response body formats.

## API Format

### **Usage Endpoint**

**Endpoint:** `GET /eip/partner/esim/{serial}/usage`

**Request:** No body (GET request)

### **Success Response Format**
```json
{
    "orderPublicId": "f1b15787-2082-456b-9d80-80a14e2aa3c2",
    "serial": "TL838TJMXS",
    "remaining": 0,
    "volume": 0,
    "expired_at": "2025-03-13 04:01:02",
    "status": "FINISHED"
}
```

### **Error Response Format**
```json
[
    {
        "name": "SERIAL",
        "code": "RESOURCE_NOT_FOUND",
        "message": "Serial not found"
    }
]
```

## Model Updates

### **EsimUsageRp (Success Response)**
```csharp
public class EsimUsageRp
{
    public string OrderPublicId { get; set; } = string.Empty;
    public string Serial { get; set; } = string.Empty;
    public long Remaining { get; set; }
    public long Volume { get; set; }
    public string ExpiredAt { get; set; } = string.Empty; // Format: "2025-03-13 04:01:02"
    public string Status { get; set; } = string.Empty; // "FINISHED", "ACTIVE", etc.
}
```

### **Status Values**
- `FINISHED` - ESim has expired or used all data
- `ACTIVE` - ESim is currently active and usable
- `SUSPENDED` - ESim is temporarily suspended
- `CANCELLED` - ESim has been cancelled

### **Data Fields**
- `remaining` - Remaining data in bytes
- `volume` - Total data volume in bytes
- `expired_at` - Expiration date in format "YYYY-MM-DD HH:MM:SS"

## Implementation

### **ESimBlueAPIClient.GetEsimUsageAsync (Fixed)**
```csharp
public async Task<ApiResult<EsimUsageRp>> GetEsimUsageAsync(string serial)
{
    var endpoint = $"/eip/partner/esim/{serial}/usage";
    
    try
    {
        var request = new HttpRequestMessage(HttpMethod.Get, endpoint);
        var response = await _httpClient.SendAsync(request);
        var responseContent = await response.Content.ReadAsStringAsync();

        // API always returns 200 OK, but response format differs
        if (response.IsSuccessStatusCode)
        {
            // Check if response starts with '[' (error array) or '{' (success object)
            var trimmedContent = responseContent.Trim();
            
            if (trimmedContent.StartsWith('['))
            {
                // Response is an error array
                try
                {
                    var errorArray = JsonSerializer.Deserialize<List<EsimErrorResponse>>(responseContent);
                    if (errorArray != null && errorArray.Count > 0)
                    {
                        var errorMessage = string.Join("; ", errorArray.Select(e => $"{e.Code}: {e.Message}"));
                        return new ApiErrorResult<EsimUsageRp>($"Get usage failed: {errorMessage}");
                    }
                }
                catch (JsonException ex)
                {
                    return new ApiErrorResult<EsimUsageRp>($"Failed to parse error response: {ex.Message}");
                }
            }
            else if (trimmedContent.StartsWith('{'))
            {
                // Response is a success object
                try
                {
                    var successResult = JsonSerializer.Deserialize<EsimUsageRp>(responseContent);
                    if (successResult != null && !string.IsNullOrEmpty(successResult.OrderPublicId))
                    {
                        return new ApiSuccessResult<EsimUsageRp>(successResult);
                    }
                }
                catch (JsonException ex)
                {
                    return new ApiErrorResult<EsimUsageRp>($"Failed to parse success response: {ex.Message}");
                }
            }
        }

        return new ApiErrorResult<EsimUsageRp>($"Unexpected response format: {response.StatusCode} - {responseContent}");
    }
    catch (Exception ex)
    {
        return new ApiErrorResult<EsimUsageRp>($"Get usage request failed: {ex.Message}");
    }
}
```

## Usage Examples

### **Frontend Integration**
```javascript
// Get ESim usage
async function getESimUsage(serial) {
    try {
        const response = await fetch(`/api/ESim/usage/${serial}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        
        if (result.isSuccessed && result.resultObj) {
            const usageData = result.resultObj;
            
            // Display usage information
            displayUsageInfo({
                remaining: formatBytes(usageData.remaining),
                total: formatBytes(usageData.volume),
                status: usageData.status,
                expiresAt: new Date(usageData.expiredAt.replace(' ', 'T')),
                percentUsed: ((usageData.volume - usageData.remaining) / usageData.volume * 100).toFixed(1)
            });
            
            return usageData;
        } else {
            throw new Error(result.message || 'Get usage failed');
        }
    } catch (error) {
        console.error('Usage error:', error);
        throw error;
    }
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
```

### **Backend Service Usage**
```csharp
// In ESimService
public async Task<ApiResult<EsimUsageRp>> GetEsimUsageAsync(string serial)
{
    var stopwatch = Stopwatch.StartNew();
    var traceId = Guid.NewGuid().ToString("N");

    try
    {
        var logId = Guid.NewGuid();
        await _fileLogger.LogApiCallAsync(
            action: "get_esim_usage",
            endpoint: $"/eip/partner/esim/{serial}/usage",
            requestData: JsonConvert.SerializeObject(new { serial }),
            status: "pending",
            traceId: traceId,
            logId: logId
        );

        var result = await _eSimBlueClient.GetEsimUsageAsync(serial);
        stopwatch.Stop();

        await _fileLogger.LogApiCallAsync(
            action: "get_esim_usage",
            endpoint: $"/eip/partner/esim/{serial}/usage",
            requestData: JsonConvert.SerializeObject(new { serial }),
            responseData: JsonConvert.SerializeObject(result.ResultObj),
            status: result.IsSuccessed ? "success" : "error",
            traceId: traceId,
            logId: logId
        );

        return result;
    }
    catch (Exception ex)
    {
        stopwatch.Stop();
        _logger.LogError(ex, "GetEsimUsage API call exception for serial: {Serial}", serial);
        return new ApiErrorResult<EsimUsageRp>($"Get ESim usage failed: {ex.Message}");
    }
}
```

## Testing

### **Test Usage API Format**
```bash
# Test the usage API format
curl -X POST "https://localhost:7001/api/TestRedis/test-esim-usage?serial=TL838TJMXS"
```

### **Test Actual Usage**
```bash
# Test actual usage (requires valid serial)
curl -X GET "https://localhost:7001/api/ESim/usage/VALID_SERIAL_HERE"
```

### **Expected Test Results**

#### **Success Response:**
```json
{
  "isSuccessed": true,
  "resultObj": {
    "orderPublicId": "f1b15787-2082-456b-9d80-80a14e2aa3c2",
    "serial": "TL838TJMXS",
    "remaining": 0,
    "volume": 1073741824,
    "expiredAt": "2025-03-13 04:01:02",
    "status": "FINISHED"
  }
}
```

#### **Error Response:**
```json
{
  "isSuccessed": false,
  "message": "Get usage failed: RESOURCE_NOT_FOUND: Serial not found"
}
```

## Data Interpretation

### **Usage Calculation**
```csharp
public class UsageCalculator
{
    public static UsageStats CalculateUsage(EsimUsageRp usage)
    {
        var used = usage.Volume - usage.Remaining;
        var percentUsed = usage.Volume > 0 ? (double)used / usage.Volume * 100 : 0;
        
        return new UsageStats
        {
            TotalBytes = usage.Volume,
            UsedBytes = used,
            RemainingBytes = usage.Remaining,
            PercentUsed = percentUsed,
            IsExpired = usage.Status == "FINISHED",
            ExpiresAt = DateTime.ParseExact(usage.ExpiredAt, "yyyy-MM-dd HH:mm:ss", null)
        };
    }
}

public class UsageStats
{
    public long TotalBytes { get; set; }
    public long UsedBytes { get; set; }
    public long RemainingBytes { get; set; }
    public double PercentUsed { get; set; }
    public bool IsExpired { get; set; }
    public DateTime ExpiresAt { get; set; }
}
```

## Benefits

1. **Accurate API Integration**: Matches actual ESimBlue usage API responses
2. **Comprehensive Usage Data**: Complete usage statistics and status
3. **Consistent Error Handling**: Same pattern as redeem API
4. **Real-time Status**: Current ESim status and expiration info
5. **Data Calculation**: Easy usage percentage and remaining data calculation

This update ensures proper handling of both success and error responses from the ESimBlue usage API, following the same reliable pattern as the redeem API.
