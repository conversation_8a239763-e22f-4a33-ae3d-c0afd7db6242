﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace NgocMaiTravel.Data.Migrations
{
    /// <inheritdoc />
    public partial class addemailreciveGDS : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("6d4ed579-012d-4996-8da8-7d6a75d535e0"));

            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("e3cd5ad3-5ed6-45d9-b152-c3520364ae87"));

            migrationBuilder.AddColumn<string>(
                name: "EmailRecive",
                table: "tblApiKeyLib",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedDate",
                table: "AppLockUserHistories",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 7, 5, 9, 2, 27, 823, DateTimeKind.Local).AddTicks(5279),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 6, 25, 0, 28, 40, 277, DateTimeKind.Local).AddTicks(8520));

            migrationBuilder.InsertData(
                table: "AppRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "Description", "Name", "NormalizedName", "TimeCreate", "UserCreateID" },
                values: new object[,]
                {
                    { new Guid("34c70a56-a3ea-44ef-b222-342c70e7d40c"), null, "Client role", "client", "client", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null },
                    { new Guid("e9172683-329d-4a0d-ae07-77a122286d6a"), null, "Employee role", "employee", "employee", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null }
                });

            migrationBuilder.UpdateData(
                table: "AppUsers",
                keyColumn: "Id",
                keyValue: new Guid("69bd714f-9576-45ba-b5b7-f00649be00de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "513086ba-f07a-40a2-84f5-d22db5cc005f", "AQAAAAIAAYagAAAAEJS65n5BuvB84bWY5dBBj5R2XdWxr/bcFGSU8qhIiP9n3qtMzRe9dDJYDaRhyPbqxQ==" });

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab53"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 5, 9, 2, 27, 868, DateTimeKind.Local).AddTicks(7250));

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab54"),
                column: "TimeCreate",
                value: new DateTime(2025, 7, 5, 9, 2, 27, 868, DateTimeKind.Local).AddTicks(7379));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("34c70a56-a3ea-44ef-b222-342c70e7d40c"));

            migrationBuilder.DeleteData(
                table: "AppRoles",
                keyColumn: "Id",
                keyValue: new Guid("e9172683-329d-4a0d-ae07-77a122286d6a"));

            migrationBuilder.DropColumn(
                name: "EmailRecive",
                table: "tblApiKeyLib");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedDate",
                table: "AppLockUserHistories",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(2025, 6, 25, 0, 28, 40, 277, DateTimeKind.Local).AddTicks(8520),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldDefaultValue: new DateTime(2025, 7, 5, 9, 2, 27, 823, DateTimeKind.Local).AddTicks(5279));

            migrationBuilder.InsertData(
                table: "AppRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "Description", "Name", "NormalizedName", "TimeCreate", "UserCreateID" },
                values: new object[,]
                {
                    { new Guid("6d4ed579-012d-4996-8da8-7d6a75d535e0"), null, "Employee role", "employee", "employee", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null },
                    { new Guid("e3cd5ad3-5ed6-45d9-b152-c3520364ae87"), null, "Client role", "client", "client", new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified), null }
                });

            migrationBuilder.UpdateData(
                table: "AppUsers",
                keyColumn: "Id",
                keyValue: new Guid("69bd714f-9576-45ba-b5b7-f00649be00de"),
                columns: new[] { "ConcurrencyStamp", "PasswordHash" },
                values: new object[] { "e7adedb1-1676-4a31-8df8-db86d2401df7", "AQAAAAIAAYagAAAAEOL1B/eXTKk677TNf62h0zkeyWsHHVvoQotlQmDHhF+ClGoJySHhed70nt36rOB1pw==" });

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab53"),
                column: "TimeCreate",
                value: new DateTime(2025, 6, 25, 0, 28, 40, 326, DateTimeKind.Local).AddTicks(1479));

            migrationBuilder.UpdateData(
                table: "category_news",
                keyColumn: "id",
                keyValue: new Guid("d3c8a07b-4f8f-4d9a-8b1b-3d9b41afab54"),
                column: "TimeCreate",
                value: new DateTime(2025, 6, 25, 0, 28, 40, 326, DateTimeKind.Local).AddTicks(1532));
        }
    }
}
