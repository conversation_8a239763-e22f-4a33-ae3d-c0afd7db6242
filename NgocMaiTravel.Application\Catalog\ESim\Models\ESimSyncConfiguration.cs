namespace NgocMaiTravel.Application.Catalog.ESim.Models
{
    /// <summary>
    /// Configuration for ESim sync operations
    /// </summary>
    public class ESimSyncConfiguration
    {
        /// <summary>
        /// How long to cache data before considering it stale
        /// </summary>
        public TimeSpan CacheExpiry { get; set; } = TimeSpan.FromHours(6);

        /// <summary>
        /// Interval between background sync operations
        /// </summary>
        public TimeSpan BackgroundSyncInterval { get; set; } = TimeSpan.FromHours(1);

        /// <summary>
        /// Maximum number of retry attempts for failed sync operations
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Delay between retry attempts
        /// </summary>
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// Number of packages to process in each batch
        /// </summary>
        public int BatchSize { get; set; } = 100;

        /// <summary>
        /// Whether background sync is enabled
        /// </summary>
        public bool EnableBackgroundSync { get; set; } = true;

        /// <summary>
        /// Whether queue processing is enabled
        /// </summary>
        public bool EnableQueueProcessing { get; set; } = true;

        /// <summary>
        /// Maximum number of concurrent sync operations
        /// </summary>
        public int MaxConcurrentSyncs { get; set; } = 1;

        /// <summary>
        /// Timeout for individual sync operations
        /// </summary>
        public TimeSpan SyncTimeout { get; set; } = TimeSpan.FromMinutes(30);

        /// <summary>
        /// Whether to enable detailed logging
        /// </summary>
        public bool EnableDetailedLogging { get; set; } = true;

        /// <summary>
        /// How long to keep sync logs before cleanup
        /// </summary>
        public TimeSpan LogRetentionPeriod { get; set; } = TimeSpan.FromDays(30);

        /// <summary>
        /// API rate limiting - requests per minute
        /// </summary>
        public int ApiRateLimit { get; set; } = 60;

        /// <summary>
        /// API timeout for individual requests
        /// </summary>
        public TimeSpan ApiTimeout { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// Whether to validate data integrity after sync
        /// </summary>
        public bool ValidateDataIntegrity { get; set; } = true;

        /// <summary>
        /// Whether to compress stored external data
        /// </summary>
        public bool CompressExternalData { get; set; } = true;

        /// <summary>
        /// Default configuration for development environment
        /// </summary>
        public static ESimSyncConfiguration Development => new()
        {
            CacheExpiry = TimeSpan.FromMinutes(30),
            BackgroundSyncInterval = TimeSpan.FromMinutes(15),
            MaxRetryAttempts = 2,
            RetryDelay = TimeSpan.FromMinutes(2),
            BatchSize = 50,
            EnableBackgroundSync = true,
            EnableQueueProcessing = true,
            MaxConcurrentSyncs = 1,
            SyncTimeout = TimeSpan.FromMinutes(10),
            EnableDetailedLogging = true,
            LogRetentionPeriod = TimeSpan.FromDays(7),
            ApiRateLimit = 30,
            ApiTimeout = TimeSpan.FromSeconds(15),
            ValidateDataIntegrity = true,
            CompressExternalData = false
        };

        /// <summary>
        /// Default configuration for production environment
        /// </summary>
        public static ESimSyncConfiguration Production => new()
        {
            CacheExpiry = TimeSpan.FromHours(6),
            BackgroundSyncInterval = TimeSpan.FromHours(1),
            MaxRetryAttempts = 3,
            RetryDelay = TimeSpan.FromMinutes(5),
            BatchSize = 100,
            EnableBackgroundSync = true,
            EnableQueueProcessing = true,
            MaxConcurrentSyncs = 2,
            SyncTimeout = TimeSpan.FromMinutes(30),
            EnableDetailedLogging = false,
            LogRetentionPeriod = TimeSpan.FromDays(30),
            ApiRateLimit = 60,
            ApiTimeout = TimeSpan.FromSeconds(30),
            ValidateDataIntegrity = true,
            CompressExternalData = true
        };

        /// <summary>
        /// Validate configuration values
        /// </summary>
        public void Validate()
        {
            if (CacheExpiry <= TimeSpan.Zero)
                throw new ArgumentException("CacheExpiry must be positive", nameof(CacheExpiry));

            if (BackgroundSyncInterval <= TimeSpan.Zero)
                throw new ArgumentException("BackgroundSyncInterval must be positive", nameof(BackgroundSyncInterval));

            if (MaxRetryAttempts < 0)
                throw new ArgumentException("MaxRetryAttempts cannot be negative", nameof(MaxRetryAttempts));

            if (BatchSize <= 0)
                throw new ArgumentException("BatchSize must be positive", nameof(BatchSize));

            if (MaxConcurrentSyncs <= 0)
                throw new ArgumentException("MaxConcurrentSyncs must be positive", nameof(MaxConcurrentSyncs));

            if (ApiRateLimit <= 0)
                throw new ArgumentException("ApiRateLimit must be positive", nameof(ApiRateLimit));
        }
    }
}
