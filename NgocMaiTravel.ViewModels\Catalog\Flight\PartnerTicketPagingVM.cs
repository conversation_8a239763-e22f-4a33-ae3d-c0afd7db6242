﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.ViewModels.Catalog.Flight
{
    public class PartnerTicketPagingVM
    {
        public string Id { get; set; }
        public string Pnrs { get; set; }
        public string CustomerName { get; set; }
        public string PhoneNumber { get; set; }
        public List<string> Itinerary { get; set; } = new List<string>();
        public long TotalPrice { get; set; }
        public string TimeCreate { get; set; }
        public string? TimeCompletion { get; set; }
        public int Status { get; set; }
        public string? TimeUpdate { get; set; }
        public string? UpdateBy { get; set; }
        public string? Notes { get; set; }
        public string? UserNote { get; set; }
    }

    public class NoteUserItem
    {
        public string content { get; set; }
        public DateTime time { get; set; }
    }
}
