using System;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Common.Logging
{
    public interface IESimFileLogger
    {
        /// <summary>
        /// Log ESim API request and response with file storage
        /// </summary>
        /// <param name="action">API action name</param>
        /// <param name="endpoint">API endpoint</param>
        /// <param name="requestData">Request data to save to file</param>
        /// <param name="responseData">Response data to save to file</param>
        /// <param name="status">Request status (success/error/pending)</param>
        /// <param name="httpStatusCode">HTTP status code</param>
        /// <param name="duration">Request duration</param>
        /// <param name="errorMessage">Error message if any</param>
        /// <param name="orderId">Order ID if applicable</param>
        /// <param name="customerEmail">Customer email if applicable</param>
        /// <param name="customerPhone">Customer phone if applicable</param>
        /// <param name="traceId">Trace ID for request tracking</param>
        /// <param name="logId">Custom log ID (optional, will generate if not provided)</param>
        /// <returns>Log entry ID</returns>
        Task<Guid> LogApiCallAsync(
            string action,
            string endpoint,
            string? requestData = null,
            string? responseData = null,
            string status = "pending",
            int? httpStatusCode = null,
            TimeSpan? duration = null,
            string? errorMessage = null,
            string? orderId = null,
            string? customerEmail = null,
            string? customerPhone = null,
            string? traceId = null,
            Guid? logId = null);

        /// <summary>
        /// Update existing log entry with response data
        /// </summary>
        /// <param name="logId">Log entry ID</param>
        /// <param name="responseData">Response data to save to file</param>
        /// <param name="status">Final status</param>
        /// <param name="httpStatusCode">HTTP status code</param>
        /// <param name="duration">Request duration</param>
        /// <param name="errorMessage">Error message if any</param>
        Task UpdateLogAsync(
            Guid logId,
            string? responseData = null,
            string status = "success",
            int? httpStatusCode = null,
            TimeSpan? duration = null,
            string? errorMessage = null);

        /// <summary>
        /// Read request data from file
        /// </summary>
        /// <param name="logId">Log entry ID</param>
        /// <returns>Request data content</returns>
        Task<string?> ReadRequestDataAsync(Guid logId);

        /// <summary>
        /// Read response data from file
        /// </summary>
        /// <param name="logId">Log entry ID</param>
        /// <returns>Response data content</returns>
        Task<string?> ReadResponseDataAsync(Guid logId);

        /// <summary>
        /// Clean up old log files
        /// </summary>
        /// <param name="olderThanDays">Delete files older than specified days</param>
        Task CleanupOldLogsAsync(int olderThanDays = 30);
    }
}
