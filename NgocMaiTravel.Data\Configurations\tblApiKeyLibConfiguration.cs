﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NgocMaiTravel.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Data.Configurations
{
    public class tblApiKeyLibConfiguration : IEntityTypeConfiguration<tblApiKeyLib>
    {
        public void Configure(EntityTypeBuilder<tblApiKeyLib> builder)
        {
            builder.ToTable("tblApiKeyLib");
            builder.HasKey(x => x.Id);
            builder.Property(x=>x.Name)
                .IsRequired()
                .HasMaxLength(255);
            builder.Property(x => x.XApiKey)
                .IsRequired()
                .HasMaxLength(100);
            builder.Property(x => x.Domain)
                .IsRequired()
                .HasMaxLength(255);
            builder.Property(x => x.IPAddress)
                .HasMaxLength(255);
            builder.Property(x => x.RequestLimit)
                .IsRequired();
            builder.Property(x => x.CreatedBy)
                .IsRequired();
            builder.Property(x => x.UpdatedBy);
            builder.Property(x => x.IsActive)
                .IsRequired();
            builder.Property(x => x.CreatedAt)
                .IsRequired();
            builder.Property(x => x.ExpiryDate);
            builder.Property(x=>x.EmailRecive).HasMaxLength(255).IsRequired(false);
            builder.Property(x=>x.SenderEmail).HasMaxLength(255).IsRequired(false);
            builder.Property(x=>x.SmtpServer).HasMaxLength(255).IsRequired(false);
            builder.Property(x => x.EmailPort).IsRequired(false);
            builder.Property(x => x.EmailPass).HasMaxLength(255).IsRequired(false);

            //set index
            builder.HasIndex(x => x.XApiKey)
                .IsUnique()
                .HasDatabaseName("IX_XApiKey");
        }
    }
}
