﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NgocMaiTravel.ApiIntegration.Common.Logger;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities;
using NgocMaiTravel.ViewModels.Catalog.Report;
using NgocMaiTravel.ViewModels.Common;
using NgocMaiTravel.ViewModels.System.XApiKey;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Security;
using System.Net.Sockets;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.System.XApiKey
{
    public class XApiKeyService : IXApiKeyService
    {
        private readonly NgocMaiTravelDbContext _context;
        private readonly ILoggerService _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<XApiKeyService> _loggerService;
        private readonly IConfiguration _configuration;

        public XApiKeyService(NgocMaiTravelDbContext context, ILoggerService logger, IHttpContextAccessor httpContextAccessor, ILogger<XApiKeyService> loggerService, IConfiguration configuration)
        {
            _context = context;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _loggerService = loggerService;
            _configuration = configuration;
        }


        public async Task<ApiResult<string>> CreateApiKey(CreateApiKeyRequest request)
        {
            var checkUser = await CheckUser();
            if (!checkUser.IsSuccessed)
            {
                return new ApiErrorResult<string>(checkUser.Message);
            }
            var user = checkUser.ResultObj;
            var xApiKey = GenarateApiKey();
            var apiKey = new tblApiKeyLib()
            {
                Id = Guid.NewGuid(),
                Name = request.Name,
                UpdatedBy = user.Id,
                XApiKey = xApiKey.ResultObj,
                CreatedAt = DateTime.Now,
                CreatedBy = user.Id,
                Domain = request.Domain,
                IPAddress = request.IPAddress,
                ExpiryDate = request.ExpiryDate,
                IsActive = request.IsActive,
                RequestLimit = request.RequestLimit,
                SenderEmail = request.SenderEmail,
                SmtpServer = request.SmtpServer,
                EmailPass = request.EmailPass,
                EmailPort = request.EmailPort
            };

            try
            {
                await _context.tblApiKeyLibs.AddAsync(apiKey);
                await _context.SaveChangesAsync();
                return new ApiSuccessResult<string>(apiKey.XApiKey);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<string>(ex.Message);
            }

        }
        private ApiResult<string> GenarateApiKey(int length = 32)
        {
            try
            {
                const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
                var randomBytes = new byte[length];
                using (var rng = RandomNumberGenerator.Create())
                {
                    rng.GetBytes(randomBytes);
                }

                var apiKey = new StringBuilder(length);
                foreach (var b in randomBytes)
                {
                    apiKey.Append(chars[b % chars.Length]);
                }

                return new ApiSuccessResult<string>(apiKey.ToString());
            }
            catch(Exception ex)
            {
                return new ApiErrorResult<string>(ex.Message);
            }
        }

        private async Task<ApiResult<AppUser>> CheckUser()
        {
            var userName = _httpContextAccessor.HttpContext?.User.Identity?.Name;
            var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == userName);
            if (user == null)
            {
                return new ApiErrorResult<AppUser>("User not found");
            }
            return new ApiSuccessResult<AppUser>(user);
        }

        public async Task<ApiResult<string>> UpdateApiKey(UpdateApiKeyRequest request)
        {
            var checkUser = await CheckUser();
            if (!checkUser.IsSuccessed)
            {
                return new ApiErrorResult<string>(checkUser.Message);
            }

            var user = checkUser.ResultObj;
            var apiKey = await _context.tblApiKeyLibs.FirstOrDefaultAsync(x => x.Id == request.Id);
            if (apiKey == null)
            {
                return new ApiErrorResult<string>("API Key not found");
            }

            apiKey.UpdatedBy = user.Id;
            apiKey.UpdateAt = DateTime.Now;
            apiKey.Domain = !string.IsNullOrEmpty(request.Domain) ? request.Domain : apiKey.Domain;
            apiKey.IPAddress = !string.IsNullOrEmpty(request.IPAddress) ? request.IPAddress : apiKey.IPAddress;
            apiKey.ExpiryDate = request.ExpiryDate ?? apiKey.ExpiryDate;
            apiKey.IsActive = request.IsActive ?? apiKey.IsActive;
            apiKey.RequestLimit = request.RequestLimit ?? apiKey.RequestLimit;
            apiKey.SenderEmail = request.SenderEmail ?? apiKey.SenderEmail;
            apiKey.SmtpServer = request.SmtpServer ?? apiKey.SmtpServer;
            apiKey.EmailPass = request.EmailPass ?? apiKey.EmailPass;
            apiKey.EmailPort = request.EmailPort ?? apiKey.EmailPort;

            if (request.ChangeXApiKey == true)
            {
                apiKey.XApiKey = GenarateApiKey().ResultObj;
            }

            try
            {
                _context.tblApiKeyLibs.Update(apiKey);
                await _context.SaveChangesAsync();
                return new ApiSuccessResult<string>(apiKey.XApiKey);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<string>(ex.Message);
            }
        }

        public async Task<ApiResult<PagedResult<PagedApiKeyModel>>> Paging(XapiKeyPagingRequest request)
        {
            var currentMonth = DateTime.UtcNow.Month;
            var currentYear = DateTime.UtcNow.Year;

            var query = from a in _context.tblApiKeyLibs
                        join b in _context.Users on a.CreatedBy equals b.Id into ab
                        from b in ab.DefaultIfEmpty()
                        join c in _context.Users on a.UpdatedBy equals c.Id into ac
                        from c in ac.DefaultIfEmpty()
                        join h in _context.tblLibHies on a.Id equals h.ApiKeyId into ah // Left Join
                        select new
                        {
                            a, b, c,
                            TotalRequestCount = ah.Where(h => h.Month == currentMonth && h.Year == currentYear)
                                                  .Sum(h => (long?)h.RequestCount) ?? 0, // Nếu không có request, trả về 0
                            LastAccessed = ah.OrderByDescending(h => h.LastUpdated)
                                                  .Select(h => h.LastUpdated)
                                                  .FirstOrDefault()
                        };

            if (!string.IsNullOrEmpty(request.Keyword))
            {
                query = query.Where(x => x.a.Domain.Contains(request.Keyword) || x.a.Name.Contains(request.Keyword));
            }

            switch(request.SortColumn)
            {
                case "Name":
                    query = request.SortOrder == "asc" ? query.OrderBy(x => x.a.Name) : query.OrderByDescending(x => x.a.Name);
                    break;
                case "Domain":
                    query = request.SortOrder == "asc" ? query.OrderBy(x => x.a.Domain) : query.OrderByDescending(x => x.a.Domain);
                    break;
                case "CreateAt":
                    query = request.SortOrder == "asc" ? query.OrderBy(x => x.a.CreatedAt) : query.OrderByDescending(x => x.a.CreatedAt);
                    break;
                case "UpdateAt":
                    query = request.SortOrder == "asc" ? query.OrderBy(x => x.a.UpdateAt) : query.OrderByDescending(x => x.a.UpdateAt);
                    break;
                case "CreateBy":
                    query = request.SortOrder == "asc" ? query.OrderBy(x => x.b.FirstName) : query.OrderByDescending(x => x.b.FirstName);
                    break;
                case "UpdateBy":
                    query = request.SortOrder == "asc" ? query.OrderBy(x => x.c.FirstName) : query.OrderByDescending(x => x.c.FirstName);
                    break;
                case "RequestCount":
                    query = request.SortOrder == "asc" ? query.OrderBy(x => x.TotalRequestCount) : query.OrderByDescending(x => x.TotalRequestCount);
                    break;
                case "LastAccessed":
                    query = request.SortOrder == "asc" ? query.OrderBy(x => x.LastAccessed) : query.OrderByDescending(x => x.LastAccessed);
                    break;
                case "IsActive":
                    query = request.SortOrder == "asc" ? query.OrderBy(x => x.a.IsActive) : query.OrderByDescending(x => x.a.IsActive);
                    break;
                case "RequestLimit":
                    query = request.SortOrder == "asc" ? query.OrderBy(x => x.a.RequestLimit) : query.OrderByDescending(x => x.a.RequestLimit);
                    break;
                default:
                    query = request.SortOrder == "asc" ? query.OrderBy(x => x.a.CreatedAt) : query.OrderByDescending(x => x.a.CreatedAt);
                    break;
            }
            request.PageIndex = request.PageIndex < 1 ? 1 : request.PageIndex;
            var totalRecords = await query.CountAsync();
            var data = await query.Skip((request.PageIndex - 1) * request.PageSize)
                .Take(request.PageSize)
                .Select(x => new PagedApiKeyModel()
                {
                    Id = x.a.Id,
                    Name = x.a.Name,
                    Domain = x.a.Domain,
                    CreateBy = $"{x.b.FirstName} {x.b.LastName}",
                    IsActive = x.a.IsActive,
                    RequestLimit = x.a.RequestLimit,
                    CreateAt = x.a.CreatedAt.ToString("dd/MM/yyyy HH:mm:ss"),
                    UpdateAt = x.a.UpdateAt.HasValue ? x.a.UpdateAt.Value.ToString("dd/MM/yyyy HH:mm:ss") : "",
                    UpdateBy = x.c != null ? $"{x.c.FirstName} {x.c.LastName}" : "",
                    RequestCount = x.TotalRequestCount,
                    LastAccessed = x.LastAccessed != DateTime.MinValue ? x.LastAccessed.ToString("dd/MM/yyyy HH:mm:ss") : ""
                }).ToListAsync();

            var pagedResult = new PagedResult<PagedApiKeyModel>()
            {
                TotalRecords = totalRecords,
                PageIndex = request.PageIndex,
                PageSize = request.PageSize,
                From = (request.PageIndex - 1) * request.PageSize + 1,
                To = Math.Min(request.PageIndex * request.PageSize, totalRecords),
                Items = data
            };
            return new ApiSuccessResult<PagedResult<PagedApiKeyModel>>(pagedResult);
        }

        public async Task<ApiResult<bool>> ValiDate(HttpContext context)
        {
            var ipAddress = GetIpAddress();
            //get xApiKey from header
            var xApiKey = context.Request.Headers.FirstOrDefault(x => x.Key.ToLower() == "x-api-key").Value.FirstOrDefault();
            if (string.IsNullOrEmpty(xApiKey))
            {
                return new ApiErrorResult<bool>("X-Api-Key is missing");
            }
            var apiKey = await _context.tblApiKeyLibs.FirstOrDefaultAsync(x => x.XApiKey == xApiKey);
            if (apiKey == null)
            {
                return new ApiErrorResult<bool>("X-Api-Key is invalid");
            }
            if (apiKey.ExpiryDate != null && apiKey.ExpiryDate < DateTime.UtcNow)
            {
                return new ApiErrorResult<bool>("X-Api-Key is expired");
            }
            if (apiKey.RequestLimit <= 0)
            {
                return new ApiErrorResult<bool>("X-Api-Key is out of request limit");
            }
            //check if apiKey is active
            if (!apiKey.IsActive)
            {
                return new ApiErrorResult<bool>("X-Api-Key is inactive");
            }
            //get domain from header origin or referer
            var domain = GetClientDomain(context);
            _loggerService.Log(LogLevel.Information, $"ValiDate_Domain: {domain}");
            if (string.IsNullOrEmpty(domain))
            {
                return new ApiErrorResult<bool>("Domain is missing");
            }
            //if (!domain.StartsWith("https://"))
            //{
            //    return new ApiErrorResult<bool>("Domain must use SSL (HTTPS)");
            //}
            if (!domain.StartsWith("http://") && !domain.StartsWith("https://"))
            {
                domain = "http://" + domain; // Thêm scheme mặc định nếu thiếu
            }

            var domainName = new Uri(domain).Host;
            var domains = apiKey.Domain.Split(';');

            var data = new
            {
                doamin = domain,
                xApiKey = xApiKey,
                domainName = domainName,
                domainsSV= domains
            };

            //_logger.LogRequest(context, JsonConvert.SerializeObject(data), Guid.NewGuid().ToString(), "Lib", false);

            if (!domains.Contains(domainName))
            {
                return new ApiErrorResult<bool>("Domain is invalid");
            }
            // Kiểm tra chứng chỉ SSL có khớp với domain không
            //bool isSslValid = await CheckSslCertificate(domainName);
            //if (!isSslValid)
            //{
            //    return new ApiErrorResult<bool>("SSL certificate does not match domain");
            //}
            //check count request in month
            var currentMonth = DateTime.UtcNow.Month;
            var currentYear = DateTime.UtcNow.Year;
            var requestCount = await _context.tblLibHies.Where(x => x.ApiKeyId == apiKey.Id && x.Month == currentMonth && x.Year == currentYear && x.ApiName == "_api_Library_SearchTrip").SumAsync(x => (long?)x.RequestCount) ?? 0;
            if (requestCount >= apiKey.RequestLimit)
            {
                return new ApiErrorResult<bool>("X-Api-Key is out of request limit");
            }
            return new ApiSuccessResult<bool>(true);
        }
        //update or insert request count
        public async Task<bool> UpdateRequestHis(string Apikey, string path, string domain)
        {
            var currentMonth = DateTime.UtcNow.Month;
            var currentYear = DateTime.UtcNow.Year;
            string apiKey = string.Empty; // Khai báo apiKey cho catch block

            using (var transaction = await _context.Database.BeginTransactionAsync(IsolationLevel.Serializable)) // Tăng Isolation Level
            {
                try
                {

                    var apiKeyId = await _context.tblApiKeyLibs
                        .Where(x => x.XApiKey == Apikey)
                        .Select(x => x.Id)
                        .FirstOrDefaultAsync();

                    if (apiKeyId == Guid.Empty)
                    {
                        return false; 
                    }

                    if (string.IsNullOrEmpty(domain))
                    {
                        return false; 
                    }

                    var functionName = $"{path.Replace("/", "_")}";

                    var requestHis = await _context.tblLibHies
                        .Where(x => x.ApiKeyId == apiKeyId && x.Month == currentMonth && x.Year == currentYear && x.ApiName == functionName)
                        .FirstOrDefaultAsync();

                    if (requestHis == null)
                    {
                        requestHis = new tblLibHis
                        {
                            Id = Guid.NewGuid(),
                            ApiKeyId = apiKeyId,
                            Month = currentMonth,
                            Year = currentYear,
                            ApiName = functionName,
                            Domain = domain,
                            RequestCount = 1,
                            LastUpdated = DateTime.UtcNow
                        };
                        await _context.tblLibHies.AddAsync(requestHis);
                    }
                    else
                    {
                        requestHis.Domain = domain; 
                        requestHis.RequestCount += 1;
                        requestHis.LastUpdated = DateTime.UtcNow;
                        _context.tblLibHies.Update(requestHis);
                    }

                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();
                    return true; 
                }
                catch (DbUpdateException ex)
                {
                    if (IsUniqueConstraintViolation(ex))
                    {
                        var apiKeyId = await _context.tblApiKeyLibs
                            .Where(x => x.XApiKey == apiKey)
                            .Select(x => x.Id)
                            .FirstOrDefaultAsync();

                        var functionName = $"{path.Replace("/", "_")}";
                        var requestHis = await _context.tblLibHies
                            .Where(x => x.ApiKeyId == apiKeyId && x.Month == currentMonth && x.Year == currentYear && x.ApiName == functionName)
                            .FirstOrDefaultAsync();

                        if (requestHis != null)
                        {
                            requestHis.RequestCount++;
                            requestHis.LastUpdated = DateTime.UtcNow;
                            _context.tblLibHies.Update(requestHis);
                            await _context.SaveChangesAsync();
                            await transaction.CommitAsync();
                        }
                        return true;
                    }
                    else
                    {
                        await transaction.RollbackAsync();
                        return false;
                    }
                }
                catch (Exception)
                {
                    await transaction.RollbackAsync();
                    return false; 
                }
            }
        }

        private bool IsUniqueConstraintViolation(DbUpdateException ex)
        {
            return ex.InnerException?.Message.Contains("UNIQUE") == true ||
                   ex.InnerException?.Message.Contains("Violation of PRIMARY KEY") == true;
        }



        public string GetIpAddress()
        {
            string ipAddress;
            try
            {
                ipAddress = _httpContextAccessor.HttpContext?.Request.Headers["X-Forwarded-For"].FirstOrDefault();

                if (string.IsNullOrEmpty(ipAddress) || (ipAddress.ToLower() == "unknown") || ipAddress.Length > 45)
                    ipAddress = _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString();
            }
            catch (Exception ex)
            {
                ipAddress = "Invalid IP:" + ex.Message;
            }

            return ipAddress;
        }

        public static string GetClientDomain(HttpContext context)
        {
            ////if this have ssl return of tlsDomain
            //if (context.Connection?.ClientCertificate != null)
            //{
            //    var tlsDomain = context.Connection.ClientCertificate.GetNameInfo(X509NameType.DnsName, false);
            //    if (!string.IsNullOrEmpty(tlsDomain))
            //    {
            //        return tlsDomain;
            //    }
            //}

            //// 2. Lấy domain từ Host header
            //var hostDomain = context.Request.Host.Value;

            // 3. Lấy domain từ X-Forwarded-Host (nếu có proxy)
            var forwardedHost = context.Request.Headers["X-Forwarded-Host"].FirstOrDefault();

            // 4. Lấy domain từ Origin / Referer (nếu có)
            var originDomain = context.Request.Headers["Origin"].FirstOrDefault() ??
                               context.Request.Headers["Referer"].FirstOrDefault();

            // Chọn domain ưu tiên (TLS/SNI > X-Forwarded-Host > Host > Origin/Referer)
            return forwardedHost  ?? originDomain ?? "";
        }

        private async Task<bool> CheckSslCertificate(string domain)
        {
            try
            {
                using (var tcpClient = new TcpClient())
                {
                    await tcpClient.ConnectAsync(domain, 443);
                    using (var sslStream = new SslStream(tcpClient.GetStream(), false,
                        new RemoteCertificateValidationCallback((sender, certificate, chain, sslPolicyErrors) => true)))
                    {
                        await sslStream.AuthenticateAsClientAsync(domain);

                        var cert = sslStream.RemoteCertificate as X509Certificate2;
                        if (cert == null) return false;

                        // Lấy CN (Common Name) từ Subject của chứng chỉ
                        string commonName = cert.GetNameInfo(X509NameType.DnsName, false);

                        // Lấy danh sách SAN (Subject Alternative Name) từ chứng chỉ
                        var sanNames = cert.Extensions
                            .OfType<X509Extension>()
                            .Where(ext => ext?.Oid?.Value == "2.5.29.17") // OID của SAN
                            .Select(ext => new AsnEncodedData(ext.Oid, ext.RawData).Format(true))
                            .FirstOrDefault()
                            ?.Split(',')
                            .Select(s => s.Trim().Replace("DNS Name=", ""))
                            .ToList();

                        // Kiểm tra nếu domain khớp với CN hoặc nằm trong danh sách SAN
                        return commonName.Equals(domain, StringComparison.OrdinalIgnoreCase) ||
                               (sanNames != null && sanNames.Contains(domain, StringComparer.OrdinalIgnoreCase));
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SSL validation failed for {domain}: {ex.Message}");
                return false;
            }
        }

        public async Task<ApiResult<List<XapiKeyItem>>> GetItems()
        {
            var result = await _context.tblApiKeyLibs.Where(x=>x.IsActive).Select(x => new XapiKeyItem()
            {
                Value = x.Id,
                Name = x.Name
            }).ToListAsync();

            return new ApiSuccessResult<List<XapiKeyItem>>(result);
        }

        public async Task<ApiResult<PartnerEmailSetting>> GetPartnerEmailSetting()
        {
            var userName = _httpContextAccessor.HttpContext?.User.Identity?.Name;
            var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == userName);
            if (user == null)
            {
                return new ApiErrorResult<PartnerEmailSetting>("User not found");
            }

            var XapiKey = await (from u in _context.Users
                                 join g in _context.GroupUsers on u.GroupUserId equals g.Id
                                 join k in _context.tblApiKeyLibs on g.XApiKeyID equals k.Id
                                 where u.Id == user.Id
                                 select k.Id).FirstOrDefaultAsync();

            if(XapiKey == null)
            {
                return new ApiErrorResult<PartnerEmailSetting>("XApiKey not found");
            }
            var result = await _context.tblApiKeyLibs.Where(x => x.Id == XapiKey).Select(x => new PartnerEmailSetting()
            {
                SenderEmail = x.SenderEmail,
                SMTPServer = x.SmtpServer,
                EmailPass = x.EmailPass,
                EmailPort = x.EmailPort
            }).FirstOrDefaultAsync();

            if (result == null)
            {
                return new ApiErrorResult<PartnerEmailSetting>("Email setting not found");
            }
            return new ApiSuccessResult<PartnerEmailSetting>(result);
        }

        public async Task<ApiResult<bool>> SavePartnerEmailSetting(PartnerEmailSetting request)
        {
            var userName = _httpContextAccessor.HttpContext?.User.Identity?.Name;
            var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == userName);
            if (user == null)
            {
                return new ApiErrorResult<bool>("User not found");
            }

            var XapiKey = await (from u in _context.Users
                                 join g in _context.GroupUsers on u.GroupUserId equals g.Id
                                 join k in _context.tblApiKeyLibs on g.XApiKeyID equals k.Id
                                 where u.Id == user.Id
                                 select k.Id).FirstOrDefaultAsync();

            if (XapiKey == null)
            {
                return new ApiErrorResult<bool>("XApiKey not found");
            }

            var xApiKeyModel = await _context.tblApiKeyLibs.FirstOrDefaultAsync(x => x.Id == XapiKey);
            if(xApiKeyModel == null)
            {
                return new ApiErrorResult<bool>("XApiKey model not found");
            }

            xApiKeyModel.SenderEmail = request.SenderEmail;
            xApiKeyModel.SmtpServer = request.SMTPServer;
            xApiKeyModel.EmailPass = request.EmailPass;
            xApiKeyModel.EmailPort = request.EmailPort;
            xApiKeyModel.UpdatedBy = user.Id;
            xApiKeyModel.UpdateAt = DateTime.Now;
            try
            {
                _context.tblApiKeyLibs.Update(xApiKeyModel);
                var result = await _context.SaveChangesAsync();
                if (result > 0)
                {
                    return new ApiSuccessResult<bool>(true);
                }
                return new ApiErrorResult<bool>("Update failed");
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<bool>(ex.Message);
            }
        }

        public async Task<ApiResult<bool>> SavePartnerEmailSetting(PartnerEmailSetting request, Guid id)
        {
            var userName = _httpContextAccessor.HttpContext?.User.Identity?.Name;
            var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == userName);
            if (user == null)
            {
                return new ApiErrorResult<bool>("User not found");
            }

            var XapiKey = await _context.tblApiKeyLibs.Where(x => x.Id == id).Select(x=>x.Id).FirstOrDefaultAsync();

            if (XapiKey == null || XapiKey == Guid.Empty)
            {
                return new ApiErrorResult<bool>("XApiKey not found");
            }

            var xApiKeyModel = await _context.tblApiKeyLibs.FirstOrDefaultAsync(x => x.Id == XapiKey);
            if (xApiKeyModel == null)
            {
                return new ApiErrorResult<bool>("XApiKey model not found");
            }

            xApiKeyModel.EmailRecive = request.EmailRecive;
            xApiKeyModel.SenderEmail = request.SenderEmail;
            xApiKeyModel.SmtpServer = request.SMTPServer;
            xApiKeyModel.EmailPass = request.EmailPass;
            xApiKeyModel.EmailPort = request.EmailPort;
            xApiKeyModel.UpdatedBy = user.Id;
            xApiKeyModel.UpdateAt = DateTime.Now;
            try
            {
                _context.tblApiKeyLibs.Update(xApiKeyModel);
                var result = await _context.SaveChangesAsync();
                if (result > 0)
                {
                    return new ApiSuccessResult<bool>(true);
                }
                return new ApiErrorResult<bool>("Update failed");
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<bool>(ex.Message);
            }
        }

        public async Task<ApiResult<ApiKeySetting>> GetApiSetting()
        {
            var userName = _httpContextAccessor.HttpContext?.User.Identity?.Name;
            var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == userName);
            if (user == null)
            {
                return new ApiErrorResult<ApiKeySetting>("User not found");
            }

            var XapiKey = await (from u in _context.Users
                                 join g in _context.GroupUsers on u.GroupUserId equals g.Id
                                 join k in _context.tblApiKeyLibs on g.XApiKeyID equals k.Id
                                 where u.Id == user.Id
                                 select k.Id).FirstOrDefaultAsync();

            if (XapiKey == null)
            {
                return new ApiErrorResult<ApiKeySetting>("XApiKey not found");
            }

            var raw = await _context.tblApiKeyLibs.Where(x => x.Id == XapiKey).FirstOrDefaultAsync();

            var result = raw == null ? null : new ApiKeySetting
            {
                ApiKey = raw.XApiKey,
                Domains = string.IsNullOrWhiteSpace(raw.Domain)
                    ? new List<string>()
                    : raw.Domain
                        .Split(';', StringSplitOptions.RemoveEmptyEntries)
                        .Select(d => d.Trim())
                        .Where(d => !string.IsNullOrEmpty(d))
                        .ToList(),
                RequestLimit = raw.RequestLimit,
                TimeCreate = raw.CreatedAt.ToString("HH:mm dd/MM/yyyy"),
                TimeExpire = raw.ExpiryDate.HasValue ? raw.ExpiryDate.Value.ToString("HH:mm dd/MM/yyyy") : "",
                IsActive = raw.IsActive
            };


            if (result == null)
            {
                return new ApiErrorResult<ApiKeySetting>("ApiKeySetting not found");
            }
            return new ApiSuccessResult<ApiKeySetting>(result);
        }

        public async Task<ApiResult<bool>> ToggleApikey(ApiKeyToggleRequest request)
        {
            var userName = _httpContextAccessor.HttpContext?.User.Identity?.Name;
            var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == userName);
            if (user == null)
            {
                return new ApiErrorResult<bool>("User not found");
            }

            var XapiKey = await (from u in _context.Users
                                 join g in _context.GroupUsers on u.GroupUserId equals g.Id
                                 join k in _context.tblApiKeyLibs on g.XApiKeyID equals k.Id
                                 where u.Id == user.Id
                                 select k.Id).FirstOrDefaultAsync();

            if (XapiKey == null)
            {
                return new ApiErrorResult<bool>("XApiKey not found");
            }

            var xApiKeyModel = await _context.tblApiKeyLibs.FirstOrDefaultAsync(x => x.Id == XapiKey);
            if (xApiKeyModel == null)
            {
                return new ApiErrorResult<bool>("XApiKey model not found");
            }
            xApiKeyModel.IsActive = request.isActive;
            xApiKeyModel.UpdatedBy = user.Id;
            xApiKeyModel.UpdateAt = DateTime.Now;
            try
            {
                _context.tblApiKeyLibs.Update(xApiKeyModel);
                var result = await _context.SaveChangesAsync();
                if (result > 0)
                {
                    return new ApiSuccessResult<bool>(true);
                }
                return new ApiErrorResult<bool>("Update failed");
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<bool>(ex.Message);
            }
        }

        public async Task<ApiResult<PartnerEmailSetting>> GetPartnerEmailSetting(Guid id)
        {

            var result = await _context.tblApiKeyLibs.Where(x => x.Id == id).Select(x => new PartnerEmailSetting()
            {
                EmailRecive = x.EmailRecive,
                SenderEmail = x.SenderEmail,
                SMTPServer = x.SmtpServer,
                EmailPass = x.EmailPass,
                EmailPort = x.EmailPort
            }).FirstOrDefaultAsync();

            if (result == null)
            {
                return new ApiErrorResult<PartnerEmailSetting>("Email setting not found");
            }
            return new ApiSuccessResult<PartnerEmailSetting>(result);
        }

        public async Task<ApiResult<ApiKeySetting>> GetApiSetting(Guid id)
        {
            var raw = await _context.tblApiKeyLibs.Where(x => x.Id == id).FirstOrDefaultAsync();

            if (raw == null)
            {
                return new ApiErrorResult<ApiKeySetting>("XApiKey not found");
            }

            var result = raw == null ? null : new ApiKeySetting
            {
                ApiKey = raw.XApiKey,
                Domains = string.IsNullOrWhiteSpace(raw.Domain)
                    ? new List<string>()
                    : raw.Domain
                        .Split(';', StringSplitOptions.RemoveEmptyEntries)
                        .Select(d => d.Trim())
                        .Where(d => !string.IsNullOrEmpty(d))
                        .ToList(),
                RequestLimit = raw.RequestLimit,
                TimeCreate = raw.CreatedAt.ToString("HH:mm dd/MM/yyyy"),
                TimeExpire = raw.ExpiryDate.HasValue ? raw.ExpiryDate.Value.ToString("yyyy-MM-dd") : "",
                IsActive = raw.IsActive
            };


            if (result == null)
            {
                return new ApiErrorResult<ApiKeySetting>("ApiKeySetting not found");
            }
            return new ApiSuccessResult<ApiKeySetting>(result);
        }

        public async Task<ApiResult<bool>> ToggleApikey(Guid id, ApiKeyToggleRequest request)
        {
            var userName = _httpContextAccessor.HttpContext?.User.Identity?.Name;
            var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == userName);
            if (user == null)
            {
                return new ApiErrorResult<bool>("User not found");
            }


            var xApiKeyModel = await _context.tblApiKeyLibs.FirstOrDefaultAsync(x => x.Id == id);
            if (xApiKeyModel == null)
            {
                return new ApiErrorResult<bool>("XApiKey model not found");
            }
            xApiKeyModel.IsActive = request.isActive;
            xApiKeyModel.UpdatedBy = user.Id;
            xApiKeyModel.UpdateAt = DateTime.Now;
            try
            {
                _context.tblApiKeyLibs.Update(xApiKeyModel);
                var result = await _context.SaveChangesAsync();
                if (result > 0)
                {
                    return new ApiSuccessResult<bool>(true);
                }
                return new ApiErrorResult<bool>("Update failed");
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<bool>(ex.Message);
            }
        }

        public async Task<ApiResult<string>> UpdateApiKey(Guid id, ApikeyUpdateSettingModelRq request)
        {
            var apiKeyModel = await _context.tblApiKeyLibs.FirstOrDefaultAsync(x => x.Id == id);
            if (apiKeyModel == null)
            {
                return new ApiErrorResult<string>("XApiKey model not found");
            }

            var userName = _httpContextAccessor.HttpContext?.User.Identity?.Name;
            var user = await _context.Users.FirstOrDefaultAsync(x => x.UserName == userName);
            if (user == null)
            {
                return new ApiErrorResult<string>("User not found");
            }

            apiKeyModel.UpdatedBy = user.Id;
            apiKeyModel.UpdateAt = DateTime.Now;
            
            apiKeyModel.Domain = request.domains.Count > 0 ? string.Join(";", request.domains) : apiKeyModel.Domain;
            apiKeyModel.RequestLimit = request.requestLimit > 0 ? request.requestLimit : apiKeyModel.RequestLimit;
            apiKeyModel.ExpiryDate = request.timeExpire != null ? request.timeExpire : apiKeyModel.ExpiryDate;

            try
            {
                _context.tblApiKeyLibs.Update(apiKeyModel);
                await _context.SaveChangesAsync();
                return new ApiSuccessResult<string>(apiKeyModel.XApiKey);
            }
            catch (Exception ex)
            {
                return new ApiErrorResult<string>(ex.Message);
            }
        }

        public async Task<string> GetEmailReciveGDS(string? apikey)
        {
            string emailReciveGDS = _configuration["EmailReciveGDS"] ?? "<EMAIL>";
            if(string.IsNullOrEmpty(apikey))
            {
                return emailReciveGDS;
            }
            var apiKeyModel = await _context.tblApiKeyLibs.FirstOrDefaultAsync(x => x.XApiKey == apikey);
            if (apiKeyModel != null && !string.IsNullOrEmpty(apiKeyModel.EmailRecive))
            {
                return apiKeyModel.EmailRecive;
            }
            return emailReciveGDS;
        }
    }
}
