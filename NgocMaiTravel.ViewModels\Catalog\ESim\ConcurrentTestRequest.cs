using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using System.ComponentModel.DataAnnotations;

namespace NgocMaiTravel.ViewModels.Catalog.ESim
{
    /// <summary>
    /// Request model for testing concurrent request handling
    /// </summary>
    public class ConcurrentTestRequest
    {
        /// <summary>
        /// Search request to test with
        /// </summary>
        [Required]
        public EsimPackagesRq SearchRequest { get; set; } = new EsimPackagesRq();

        /// <summary>
        /// Number of concurrent requests to send
        /// </summary>
        [Range(1, 20)]
        public int ConcurrentCount { get; set; } = 5;
    }
}
