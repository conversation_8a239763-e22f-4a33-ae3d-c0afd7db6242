using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using System;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace NgocMaiTravel.Application.Common.Services
{
    /// <summary>
    /// Generates unique keys for ESim requests to enable deduplication
    /// </summary>
    public static class ESimRequestKeyGenerator
    {
        /// <summary>
        /// Generate a unique key for SearchPlans request
        /// </summary>
        /// <param name="request">Search request parameters</param>
        /// <returns>Unique key for the request</returns>
        public static string GenerateSearchPlansKey(EsimPackagesRq request)
        {
            if (request == null)
                return "search_plans_null";

            // Create a normalized representation of the request
            var normalizedRequest = new
            {
                type = NormalizeString(request.type),
                sku = NormalizeString(request.sku),
                locationCode = NormalizeString(request.locationCode),
                unnamed = NormalizeString(request.unnamed)
            };

            // Serialize to JSON for consistent ordering
            var json = JsonSerializer.Serialize(normalizedRequest, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            });

            // Generate hash for shorter, consistent key
            var hash = GenerateHash(json);
            
            return $"search_plans_{hash}";
        }

        /// <summary>
        /// Generate a unique key for SearchPlansWithContext request
        /// </summary>
        /// <param name="request">Search request parameters</param>
        /// <param name="customerEmail">Customer email (optional)</param>
        /// <param name="customerPhone">Customer phone (optional)</param>
        /// <returns>Unique key for the request</returns>
        public static string GenerateSearchPlansWithContextKey(EsimPackagesRq request, string? customerEmail = null, string? customerPhone = null)
        {
            if (request == null)
                return "search_plans_context_null";

            // For context searches, we might want to include customer info in the key
            // or exclude it if we want to share results across customers
            // Here we'll exclude customer info to allow sharing of search results
            
            var normalizedRequest = new
            {
                type = NormalizeString(request.type),
                sku = NormalizeString(request.sku),
                locationCode = NormalizeString(request.locationCode),
                unnamed = NormalizeString(request.unnamed),
                // Uncomment below if you want customer-specific caching
                // customerEmail = NormalizeString(customerEmail),
                // customerPhone = NormalizeString(customerPhone)
            };

            var json = JsonSerializer.Serialize(normalizedRequest, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            });

            var hash = GenerateHash(json);
            
            return $"search_plans_context_{hash}";
        }

        /// <summary>
        /// Generate a unique key for GetBalance request
        /// </summary>
        /// <returns>Unique key for balance request</returns>
        public static string GenerateGetBalanceKey()
        {
            // Balance requests are always the same, so use a constant key
            return "get_balance";
        }

        /// <summary>
        /// Generate a unique key for GetProfile request
        /// </summary>
        /// <param name="serial">ESim serial number</param>
        /// <returns>Unique key for the request</returns>
        public static string GenerateGetProfileKey(string serial)
        {
            var normalizedSerial = NormalizeString(serial);
            return $"get_profile_{normalizedSerial}";
        }

        /// <summary>
        /// Generate a unique key for CreateOrder request
        /// </summary>
        /// <param name="planId">Plan ID</param>
        /// <param name="customerEmail">Customer email</param>
        /// <param name="customerPhone">Customer phone</param>
        /// <param name="orderId">Order ID (optional)</param>
        /// <returns>Unique key for the request</returns>
        public static string GenerateCreateOrderKey(string planId, string customerEmail, string customerPhone, string? orderId = null)
        {
            // For order creation, we typically don't want deduplication
            // because each order should be unique, but we can use it for very short periods
            // to prevent accidental double-clicks
            
            var normalizedRequest = new
            {
                planId = NormalizeString(planId),
                customerEmail = NormalizeString(customerEmail),
                customerPhone = NormalizeString(customerPhone),
                orderId = NormalizeString(orderId)
            };

            var json = JsonSerializer.Serialize(normalizedRequest, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            });

            var hash = GenerateHash(json);
            
            return $"create_order_{hash}";
        }

        /// <summary>
        /// Normalize string values for consistent key generation
        /// </summary>
        /// <param name="value">String value to normalize</param>
        /// <returns>Normalized string</returns>
        private static string NormalizeString(string? value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return string.Empty;

            return value.Trim().ToLowerInvariant();
        }

        /// <summary>
        /// Generate SHA256 hash of input string
        /// </summary>
        /// <param name="input">Input string</param>
        /// <returns>Hash string</returns>
        private static string GenerateHash(string input)
        {
            if (string.IsNullOrEmpty(input))
                return "empty";

            using var sha256 = SHA256.Create();
            var bytes = Encoding.UTF8.GetBytes(input);
            var hashBytes = sha256.ComputeHash(bytes);
            
            // Convert to hex string (first 16 characters for shorter keys)
            var hex = Convert.ToHexString(hashBytes);
            return hex[..Math.Min(16, hex.Length)].ToLowerInvariant();
        }

        /// <summary>
        /// Generate time-based key for requests that should be cached for short periods
        /// </summary>
        /// <param name="baseKey">Base key</param>
        /// <param name="timeWindowSeconds">Time window in seconds</param>
        /// <returns>Time-based key</returns>
        public static string GenerateTimeBasedKey(string baseKey, int timeWindowSeconds = 60)
        {
            var timeWindow = DateTimeOffset.UtcNow.ToUnixTimeSeconds() / timeWindowSeconds;
            return $"{baseKey}_{timeWindow}";
        }
    }
}
