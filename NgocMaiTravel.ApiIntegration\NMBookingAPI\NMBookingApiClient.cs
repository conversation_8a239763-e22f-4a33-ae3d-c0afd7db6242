﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using NgocMaiTravel.ApiIntegration.Common.Logger;
using NgocMaiTravel.ApiIntegration.Queue;
using NgocMaiTravel.ViewModels.Catalog.Flight;
using NgocMaiTravel.ViewModels.Catalog.TicketIssueFee;
using NgocMaiTravel.ViewModels.Common;
using System.Net.Http;
using System.Net.Sockets;
using System.Text;


namespace NgocMaiTravel.ApiIntegration.NMBookingAPI
{
    public class NMBookingApiClient : INMBookingApiClient
    {
        private readonly NMBooking _nmBooking;
        private readonly IMemoryCache _memoryCache;
        private readonly IHttpClientFactory _httpClientFactory;

        //private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private const string LoginResultCacheKey = "NMBookingLoginKey";
        //test enviroment
        //private const string XApiKey = "3CBxzdYcEgNDrRhMbDpkBF7e4d4Kib46dwL9ZE5egiL0iL5Y3dzREUBSUYVUwUkN";
        //private const string Username = "uatITVU";
        //private const string Password = "xCmRqus$/dcJ28P";

        //prod enviroment
        private const string XApiKey = "RDPgNKeL3BFbXmwGj6DVoe3Ve7GADd46dwL9ZE5egiL0iL5Y3dzREUBSUYVUwU68";
        private const string Username = "prodNMvn";
        private const string Password = "Wy47f93h68!r68@";

        private readonly ILoggerService _loggerService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IQueueService _airportQueue;

        public NMBookingApiClient(NMBooking nmBooking, IMemoryCache memoryCache, IHttpClientFactory httpClientFactory,
            IConfiguration configuration, ILoggerService loggerService, IHttpContextAccessor httpContextAccessor, IQueueService airportQueue)
        {
            _nmBooking = nmBooking;
            _memoryCache = memoryCache;
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
            _loggerService = loggerService;
            _httpContextAccessor = httpContextAccessor;
            _airportQueue = airportQueue;
        }

        public async Task<ApiResult<RpSearchItineraryModel>> SearchTrip(RqSearchTripModel request)
        {
            request.TypeTrip = request.OriginDestinationTrip.Count == 1 ? "OW" : "RT";
            _airportQueue.Enqueue(async (serviceProvider) =>
            {
                var airportService = serviceProvider.GetRequiredService<IScoreService>();
                var airportCodes = string.Join(";", request.OriginDestinationTrip.Select(x => x.OriginCode).Distinct().ToList());
                airportCodes += ";" + string.Join(";", request.OriginDestinationTrip.Select(x => x.DestinationCode).Distinct().ToList());
                await airportService.UpdateScoreAirport(airportCodes);
            });
            var result = await ExecuteApiCallManual<RqSearchTripModel, RpSearchItineraryModel>(request, "/Flight/SearchTrip", getSearchTripCacheKey);
            
            return result;
        }



        public async Task<ApiResult<RpPriceItineraryModel>> PriceAncillary(RqPriceAncillary request)
        {
            var result = await ExecuteApiCallManual<RqPriceAncillary, RpPriceItineraryModel>(request, "/Flight/PriceAncillary");
            //return await ExecuteApiCall(request, _nmBooking.PriceAncillaryAsync, getRqPriceAncillaryToCacheKey);
            return result;
        }

        public async Task<ApiResult<RpBookRetrieveModel>> BookTrip(RqBookTripModel request)
        {
            var result = await ExecuteApiCallManual<RqBookTripModel, RpBookRetrieveModel>(request, "/Flight/BookTrip");
            //var result = await ExecuteApiCall(request, _nmBooking.BookTripAsync);
            try
            {
                if (result.IsSuccessed)
                {
                    if (result.ResultObj?.ListRetrieve != null)
                    {
                        var pnrCode = result.ResultObj.ListRetrieve.FirstOrDefault()?.Pnr;
                        if (!string.IsNullOrEmpty(pnrCode) && pnrCode.Length == 6)
                        {
                            return result;
                        }
                    }
                }
            }catch(Exception ex)
            {

            }
            
            return new ApiErrorResult<RpBookRetrieveModel>
            {
                ResultObj = result.ResultObj
            };
        }

        public async Task<ApiResult<RpRetrieveBookingModel>> Retrieve(RqRetrieveBookingModel request)
        {
            return await ExecuteApiCall(request, _nmBooking.RetrieveAsync, getRqRetrieveBookingModelToKeyCache);
        }

        

        private async Task<ApiResult<TResponse>> ExecuteApiCallManual<TRequest, TResponse>(TRequest request, string apiUrl, Func<TRequest, string>? cacheKeyFunc = null, int retryCount = 0)
        {
            
            var context = _httpContextAccessor.HttpContext;
            var cacheKey = cacheKeyFunc?.Invoke(request);
            var logId = Guid.NewGuid().ToString();
            var isSearchTrip = request is RqSearchTripModel;
            var requestString = "";
            if (isSearchTrip)
            {
                var rq = request as RqSearchTripModel;
                requestString = CreateSearchTripFileName(rq);

            }
            _loggerService.LogRequest(context, JsonConvert.SerializeObject(request), logId, requestString, isSearchTrip);

            if (cacheKey != null && _memoryCache.TryGetValue(cacheKey, out TResponse cachedResult))
            {
                _loggerService.LogResponse(context, JsonConvert.SerializeObject(cachedResult), logId, requestString, isSearchTrip);
                return new ApiSuccessResult<TResponse>(cachedResult);
            }

            try
            {
                var loginResult = await GetLoginResultAsync();
                if (loginResult == null)
                {
                    _loggerService.LogResponse(context, "ERROR: lg_00 Login failed", logId, requestString, isSearchTrip);
                    return new ApiErrorResult<TResponse>("ERROR: lg_00 Login failed");
                }
                var response = await this.SendRequestAsync<TResponse>(apiUrl, request, loginResult.accessToken);
                if (response.IsSuccessed && response.ResultObj != null)
                {
                    if (cacheKey != null && isSearchTrip)
                    {
                        var searchTripAirsProperty = response.ResultObj.GetType().GetProperty("SearchTripAirs");
                        if (searchTripAirsProperty != null)
                        {
                            _memoryCache.Set(cacheKey, response.ResultObj, TimeSpan.FromMinutes(10));
                        }
                    }
                    else if (cacheKey != null)
                    {
                        _memoryCache.Set(cacheKey, response.ResultObj, TimeSpan.FromMinutes(4.5));
                    }

                    _loggerService.LogResponse(context, JsonConvert.SerializeObject(response.ResultObj), logId, requestString.ToString(), isSearchTrip);
                    return new ApiSuccessResult<TResponse>(response.ResultObj);
                }

                _loggerService.LogResponse(context, "ERROR: ex_01\n" + response.Message, logId, requestString, isSearchTrip);
                _memoryCache.Remove(LoginResultCacheKey);
                if ((response.Message.Contains("401") || response.Message.Contains("403")) && retryCount < 0)
                {
                    _loggerService.LogResponse(context, "Retrying due to unauthorized response...", logId, requestString.ToString(), isSearchTrip);
                    return await ExecuteApiCallManual<TRequest, TResponse>(request, apiUrl, cacheKeyFunc, retryCount + 1);

                }

                _loggerService.LogResponse(context, "ERROR: ex_02\n" + response.Message, logId, requestString.ToString(), isSearchTrip);
                return new ApiErrorResult<TResponse>("ERROR: ex_02\n" + response.Message);
            }
            catch (Exception ex)
            {
                _loggerService.LogResponse(context, "ERROR: ex_03\n" + ex.Message, logId, requestString, isSearchTrip);
                return new ApiErrorResult<TResponse>("ERROR: ex_03\n" + ex.Message);
            }

        }

        private string CreateSearchTripFileName(RqSearchTripModel rq)
        {
            var requestString = $"{rq.Airlines}";
            var steps = rq.OriginDestinationTrip.ToList();
            if (steps != null && steps.Count > 0)
            {
                requestString += $"_{steps[0].OriginCode}_{steps[0].DestinationCode}_{steps[0].OriginDate.Replace('-', '_')}";
                if (rq.OriginDestinationTrip.Count > 1)
                {
                    requestString += $"_{steps[1].OriginDate.Replace('-', '_')}";
                }
                requestString += $"_{rq.Adult}_{rq.Child}_{rq.Infant}";
            }

            return requestString;
        }


        private async Task<ApiResult<TResponse>> ExecuteApiCall<TRequest, TResponse>(TRequest request, Func<TRequest, Task<TResponse>> apiCall, Func<TRequest, string>? cacheKeyFunc = null)
        {
            var context = _httpContextAccessor.HttpContext;
            var cacheKey = cacheKeyFunc?.Invoke(request);
            var logId = Guid.NewGuid().ToString();
            var isSearchTrip = request is RqSearchTripModel;
            var requestString = "";
            var checkIsSearchTrip = request is RqSearchTripModel;
            if (request is RqSearchTripModel)
            {
                var rq = request as RqSearchTripModel;
                requestString = $"{rq.Airlines}";
                var steps = rq.OriginDestinationTrip.ToList();
                if (steps != null && steps.Count > 0)
                {
                    requestString += $"_{steps[0].OriginCode}_{steps[0].DestinationCode}_{steps[0].OriginDate.Replace('-', '_')}";
                    if (rq.OriginDestinationTrip.Count > 1)
                    {
                        requestString += $"_{steps[1].OriginDate.Replace('-', '_')}";
                    }
                    requestString += $"_{rq.Adult}_{rq.Child}_{rq.Infant}";
                }
            }

            _loggerService.LogRequest(context, JsonConvert.SerializeObject(request), logId, requestString, isSearchTrip);

            // check cache before call api
            if (cacheKey != null && _memoryCache.TryGetValue(cacheKey, out TResponse cachedResult))
            {
                _loggerService.LogResponse(context, JsonConvert.SerializeObject(cachedResult), logId, requestString, isSearchTrip);
                return new ApiSuccessResult<TResponse>(cachedResult);
            }
            // get access token or login if not exist
            var loginResult = await GetLoginResultAsync();
            if (loginResult == null)
            {
                _loggerService.LogResponse(context, "ERROR: lg_00 Login failed", logId, requestString, isSearchTrip);
                return new ApiErrorResult<TResponse>("ERROR: lg_00 Login failed");
            }

            _nmBooking._httpClient.DefaultRequestHeaders.Remove("Authorization");
            _nmBooking._httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", "Bearer " + loginResult.accessToken);

            try
            {
                var result = await apiCall(request);
                if (result != null)
                {
                    if (cacheKey != null)
                    {
                        if (checkIsSearchTrip)
                        {
                            //check if reuslt success and have data ==> cache 10 minutes
                            if (result.GetType()?.GetProperty("SearchTripAirs") != null)
                                _memoryCache.Set(cacheKey, result, TimeSpan.FromMinutes(10));
                        }
                        else
                        {
                            _memoryCache.Set(cacheKey, result, TimeSpan.FromMinutes(4.5));
                        }
                    }

                    _loggerService.LogResponse(context, JsonConvert.SerializeObject(result), logId, requestString, isSearchTrip);
                    return new ApiSuccessResult<TResponse>(result);
                }
            }
            catch (Exception ex)
            {
                //clear cache login
                _loggerService.LogResponse(context, "ERROR: ex_01\n" + ex.Message, logId, requestString, isSearchTrip);
                _memoryCache.Remove(LoginResultCacheKey);

                //check status 401, call login again and retry api call
                if (ex.Message.Contains("401") || ex.Message.Contains("403"))
                {
                    return await ExecuteApiCall(request, apiCall, cacheKeyFunc);
                }
                else
                {
                    _loggerService.LogResponse(context, "ERROR: ex_02\n" + ex.Message, logId, requestString, isSearchTrip);
                    return new ApiErrorResult<TResponse>("ERROR: ex_02\n" + ex.Message);
                }
            }
            _loggerService.LogResponse(context, "ERROR: ex_03", logId, requestString, isSearchTrip);
            return new ApiErrorResult<TResponse>("ERROR: ex_03");

        }

        private async Task<FlightLoginResponse> GetLoginResultAsync()
        {
            if (!_memoryCache.TryGetValue(LoginResultCacheKey, out FlightLoginResponse loginResult))
            {
                var loginRequest = new FlightLoginRequest()
                {
                    username = Username,
                    password = Password
                };

                var loginResultResponse = await SendRequestAsync<FlightLoginResponse>("/Auth/Token", loginRequest);
                if (!loginResultResponse.IsSuccessed)
                {
                    return null;
                }

                loginResult = loginResultResponse.ResultObj;
                //remove cache after 4.5 minutes
                _memoryCache.Set(LoginResultCacheKey, loginResult, TimeSpan.FromMinutes(4.5));
            }

            return loginResult;
        }

        private async Task<ApiResult<T>> SendRequestAsync<T>(string endpoint, object request, string accessToken = null)
        {
            var json = JsonConvert.SerializeObject(request);
            var httpContent = new StringContent(json, Encoding.UTF8, "application/json");

            // Use configured HttpClient instead of creating new one
            var _httpClient = _httpClientFactory.CreateClient("NMBookingClient");

            // Clear any existing headers to avoid conflicts
            _httpClient.DefaultRequestHeaders.Remove("X-API-Key");
            _httpClient.DefaultRequestHeaders.Remove("Authorization");

            // Add required headers
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("X-API-Key", XApiKey);

            if (!string.IsNullOrEmpty(accessToken))
            {
                _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", "Bearer " + accessToken);
            }

            // Implement simple retry logic for network issues
            var maxRetries = 3;
            var retryDelay = TimeSpan.FromSeconds(1);

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    var response = await _httpClient.PostAsync(endpoint, httpContent);

                    if (response.IsSuccessStatusCode)
                    {
                        var responseContent = await response.Content.ReadAsStringAsync();
                        var data = JsonConvert.DeserializeObject<T>(responseContent);
                        return new ApiSuccessResult<T>(data);
                    }

                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorMessage = $"HTTP {(int)response.StatusCode} {response.StatusCode}: {errorContent}";
                    return new ApiErrorResult<T>(errorMessage);
                }
                catch (HttpRequestException httpEx) when (attempt < maxRetries)
                {
                    // Network-related errors - retry
                    await Task.Delay(retryDelay * attempt);
                    continue;
                }
                catch (HttpRequestException httpEx)
                {
                    // Final attempt failed
                    var errorMessage = $"ERROR: ex_01\nNetwork error after {maxRetries} attempts: {httpEx.Message}";
                    return new ApiErrorResult<T>(errorMessage);
                }
                catch (TaskCanceledException tcEx) when (tcEx.InnerException is TimeoutException)
                {
                    // Timeout errors - don't retry
                    var errorMessage = $"ERROR: ex_02\nRequest timeout: {tcEx.Message}";
                    return new ApiErrorResult<T>(errorMessage);
                }
                catch (TaskCanceledException tcEx)
                {
                    // Cancellation errors - don't retry
                    var errorMessage = $"ERROR: ex_03\nRequest cancelled: {tcEx.Message}";
                    return new ApiErrorResult<T>(errorMessage);
                }
                catch (Exception ex) when (attempt < maxRetries && IsRetryableException(ex))
                {
                    // Other retryable errors
                    await Task.Delay(retryDelay * attempt);
                    continue;
                }
                catch (Exception ex)
                {
                    // Non-retryable or final attempt
                    var errorMessage = $"ERROR: ex_04\nUnexpected error: {ex.Message}";
                    return new ApiErrorResult<T>(errorMessage);
                }
            }

            // This should never be reached, but just in case
            return new ApiErrorResult<T>("ERROR: ex_05\nMax retries exceeded");
        }

        /// <summary>
        /// Determine if an exception is retryable
        /// </summary>
        private static bool IsRetryableException(Exception ex)
        {
            // Retry on socket errors, connection errors, etc.
            return ex is SocketException ||
                   ex is System.Net.WebException ||
                   (ex is HttpRequestException &&
                    (ex.Message.Contains("socket") ||
                     ex.Message.Contains("connection") ||
                     ex.Message.Contains("buffer space") ||
                     ex.Message.Contains("queue was full")));
        }

        private string getSearchTripCacheKey(RqSearchTripModel request)
        {
            var keyParts = new List<string>
            {
                $"Airlines:{request.Airlines ?? "N/A"}",
                $"PromoCode:{request.PromoCode ?? "N/A"}",
                $"Passengers:Adult={request.Adult},Child={request.Child},Infant={request.Infant}",
                $"TypeTrip:{request.TypeTrip ?? "N/A"}"
            };

            // Thêm thông tin các chặng bay
            if (request.OriginDestinationTrip != null && request.OriginDestinationTrip.Any())
            {
                var tripKeys = request.OriginDestinationTrip
                    .Select(trip => getOriginDestinationTripItemToKeyCache(trip) ?? "EmptyTrip")
                    .ToList();
                keyParts.Add($"Trips:[{string.Join("|", tripKeys)}]");
            }
            else
            {
                keyParts.Add("Trips:[]");
            }

            // Kết hợp tất cả thành một chuỗi duy nhất
            return string.Join(";", keyParts);
        }

        private string getOriginDestinationTripItemToKeyCache(RqSearchTripOriginDestinationTripModel trip)
        {
            return $"From:{trip.OriginCode ?? "N/A"}," +
                   $"To:{trip.DestinationCode ?? "N/A"}," +
                   $"On:{trip.OriginDate ?? "N/A"}";
        }

        public string getRqPriceAncillaryToCacheKey(RqPriceAncillary request)
        {
            return $"SessionID:{request.SessionID ?? "N/A"};GroupCodeRef:{request.GroupCodeRef ?? "N/A"};ListCode:[{string.Join("|", request.ListCode.Select(x => getCodeRefCacheKey(x.CodeRef)))}]";
        }

        public string getCodeRefCacheKey(string CodeRef)
        {
            return $"CodeRef:{CodeRef ?? "N/A"}";
        }

        private string getRqRetrieveBookingModelToKeyCache(RqRetrieveBookingModel request)
        {
            return $"Pnr:{request.Pnr ?? "N/A"};BookGDS:{request.BookGDS ?? "N/A"}";
        }


        public RpSearchItineraryModel SearchTripAddIssuanceFee(RpSearchItineraryModel request, double ticketIssuanceFee)
        {
            try
            {
                if (request.SearchTripAirs == null || request.SearchTripAirs.Count == 0)
                {
                    return request;
                }

                #region add ticket issuance fee
                foreach (var air in request.SearchTripAirs)
                {
                    var combine = air.Combine;
                    if (air.AirItinerary != null && air.AirItinerary.Count > 0)
                    {
                        foreach (var airItinerary in air.AirItinerary)
                        {
                            if(airItinerary.ListCalendar != null && airItinerary.ListCalendar.Count > 0)
                            {
                                foreach (var calendar in airItinerary.ListCalendar)
                                {
                                    calendar.Tax += ticketIssuanceFee * (request.Adult + request.Child + request.Infant);
                                    calendar.Price = calendar.Tax + calendar.Fare;
                                }
                            }
                            if (airItinerary.AirSegments != null && airItinerary.AirSegments.Count > 0)
                            {
                                foreach (var segment in airItinerary.AirSegments)
                                {
                                    //update price for lowest inventory
                                    if(segment.LowestInventory != null)
                                    {
                                        if (segment.LowestInventory.FareInfos != null && segment.LowestInventory.FareInfos.Count > 0)
                                        {
                                            segment.LowestInventory.SumFare = 0;
                                            segment.LowestInventory.SumPrice = segment.LowestInventory.SumTax;
                                            foreach (var fareInfo in segment.LowestInventory.FareInfos)
                                            {
                                                fareInfo.Fare = combine ? fareInfo.Fare + ticketIssuanceFee * 2 : fareInfo.Fare + ticketIssuanceFee;
                                                if (fareInfo.PaxType == "ADT")
                                                {
                                                    segment.LowestInventory.SumFare += request.Adult * fareInfo.Fare;
                                                    segment.LowestInventory.SumPrice += request.Adult * fareInfo.Fare;
                                                }
                                                else if (fareInfo.PaxType == "CHD")
                                                {
                                                    segment.LowestInventory.SumFare += request.Child * fareInfo.Fare;
                                                    segment.LowestInventory.SumPrice += request.Child * fareInfo.Fare;
                                                }
                                                else if (fareInfo.PaxType == "INF")
                                                {
                                                    segment.LowestInventory.SumFare += request.Infant * fareInfo.Fare;
                                                    segment.LowestInventory.SumPrice += request.Infant * fareInfo.Fare;
                                                }
                                            }
                                        }

                                    }


                                    //update price for list inventory
                                    if (segment.Inventories != null && segment.Inventories.Count > 0)
                                    {
                                        foreach (var inventory in segment.Inventories)
                                        {
                                            inventory.SumFare = 0;
                                            inventory.SumPrice = inventory.SumTax;
                                            foreach (var fareInfo in inventory.FareInfos)
                                            {
                                                fareInfo.Fare = combine ? fareInfo.Fare + ticketIssuanceFee * 2 : fareInfo.Fare + ticketIssuanceFee;
                                                if (fareInfo.PaxType == "ADT")
                                                {
                                                    inventory.SumFare += request.Adult * fareInfo.Fare;
                                                    inventory.SumPrice += request.Adult * fareInfo.Fare;
                                                }
                                                else if (fareInfo.PaxType == "CHD")
                                                {
                                                    inventory.SumFare += request.Child * fareInfo.Fare;
                                                    inventory.SumPrice += request.Child * fareInfo.Fare;
                                                }
                                                else if (fareInfo.PaxType == "INF")
                                                {
                                                    inventory.SumFare += request.Infant * fareInfo.Fare;
                                                    inventory.SumPrice += request.Infant * fareInfo.Fare;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                }
                #endregion
            }
            catch (Exception Ex) { }

            return request;
        }

        public RpSearchItineraryModel SearchTripAddIssuanceFeeAdvance(RpSearchItineraryModel request, TicketIssueFeeModel fee)
        {
            if (fee == null)
                return request;
            try
            {
                if (request.SearchTripAirs == null || request.SearchTripAirs.Count == 0)
                {
                    return request;
                }

                #region add ticket issuance fee
                foreach (var air in request.SearchTripAirs)
                {
                    var combine = air.Combine;
                    if (air.AirItinerary != null && air.AirItinerary.Count > 0)
                    {
                        foreach (var airItinerary in air.AirItinerary)
                        {
                            if (airItinerary.ListCalendar != null && airItinerary.ListCalendar.Count > 0)
                            {
                                foreach (var calendar in airItinerary.ListCalendar)
                                {
                                    calendar.Tax += combine ? (double)fee.FeeAmountAdult * 2 * request.Adult + (double)fee.FeeAmountChild * 2 * request.Child + (double)fee.FeeAmountInfant * 2 * request.Infant
                                        : (double)fee.FeeAmountAdult * request.Adult + (double)fee.FeeAmountChild * request.Child + (double)fee.FeeAmountInfant * request.Infant;

                                    calendar.Price = calendar.Tax + calendar.Fare;
                                }
                            }

                            if (airItinerary.AirSegments != null && airItinerary.AirSegments.Count > 0)
                            {
                                foreach (var segment in airItinerary.AirSegments)
                                {
                                    //update price for lowest inventory
                                    if(segment.LowestInventory != null)
                                    {
                                        if (segment.LowestInventory.FareInfos != null && segment.LowestInventory.FareInfos.Count > 0)
                                        {
                                            segment.LowestInventory.SumTax = 0;
                                            segment.LowestInventory.SumPrice = segment.LowestInventory.SumFare;
                                            foreach (var fareInfo in segment.LowestInventory.FareInfos)
                                            {
                                                if (fareInfo.PaxType == "ADT")
                                                {
                                                    fareInfo.Tax = combine ? fareInfo.Tax + (double)fee.FeeAmountAdult * 2 : fareInfo.Tax + (double)fee.FeeAmountAdult;
                                                    segment.LowestInventory.SumTax += request.Adult * fareInfo.Tax;
                                                    segment.LowestInventory.SumPrice += request.Adult * fareInfo.Tax;
                                                }
                                                else if (fareInfo.PaxType == "CHD")
                                                {
                                                    fareInfo.Tax = combine ? fareInfo.Tax + (double)fee.FeeAmountChild * 2 : fareInfo.Tax + (double)fee.FeeAmountChild;
                                                    segment.LowestInventory.SumTax += request.Child * fareInfo.Tax;
                                                    segment.LowestInventory.SumPrice += request.Child * fareInfo.Tax;
                                                }
                                                else if (fareInfo.PaxType == "INF")
                                                {
                                                    fareInfo.Tax = combine ? fareInfo.Tax + (double)fee.FeeAmountInfant * 2 : fareInfo.Tax + (double)fee.FeeAmountInfant;
                                                    segment.LowestInventory.SumTax += request.Infant * fareInfo.Tax;
                                                    segment.LowestInventory.SumPrice += request.Infant * fareInfo.Tax;
                                                }
                                            }
                                        }

                                    }


                                    //update price for list inventory
                                    if (segment.Inventories != null && segment.Inventories.Count > 0)
                                    {
                                        foreach (var inventory in segment.Inventories)
                                        {
                                            inventory.SumTax = 0;
                                            inventory.SumPrice = inventory.SumFare;
                                            foreach (var fareInfo in inventory.FareInfos)
                                            {
                                                if (fareInfo.PaxType == "ADT")
                                                {
                                                    fareInfo.Tax = combine ? fareInfo.Tax + (double)fee.FeeAmountAdult * 2 : fareInfo.Tax + (double)fee.FeeAmountAdult;
                                                    inventory.SumTax += request.Adult * fareInfo.Tax;
                                                    inventory.SumPrice += request.Adult * fareInfo.Tax;
                                                }
                                                else if (fareInfo.PaxType == "CHD")
                                                {
                                                    fareInfo.Tax = combine ? fareInfo.Tax + (double)fee.FeeAmountChild * 2 : fareInfo.Tax + (double)fee.FeeAmountChild;
                                                    inventory.SumTax += request.Child * fareInfo.Tax;
                                                    inventory.SumPrice += request.Child * fareInfo.Tax;
                                                }
                                                else if (fareInfo.PaxType == "INF")
                                                {
                                                    fareInfo.Tax = combine ? fareInfo.Tax + (double)fee.FeeAmountInfant * 2 : fareInfo.Tax + (double)fee.FeeAmountInfant;
                                                    inventory.SumTax += request.Infant * fareInfo.Tax;
                                                    inventory.SumPrice += request.Infant * fareInfo.Tax;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                }
                #endregion
            }
            catch (Exception Ex) { }

            return request;
        }
    }
}
