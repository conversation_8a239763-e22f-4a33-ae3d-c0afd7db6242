using System;
using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Common.Diagnostics
{
    /// <summary>
    /// Proxy and firewall diagnostics utilities
    /// </summary>
    public static class ProxyDiagnostics
    {
        /// <summary>
        /// Detect proxy settings and test connectivity
        /// </summary>
        public static async Task<string> DiagnoseProxySettingsAsync(string targetUrl)
        {
            var results = new global::System.Text.StringBuilder();
            results.AppendLine("=== Proxy and Firewall Diagnostics ===");
            results.AppendLine($"Target URL: {targetUrl}");
            results.AppendLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            results.AppendLine();

            try
            {
                // Check system proxy settings
                results.AppendLine("1. System Proxy Detection:");
                var systemProxy = WebRequest.GetSystemWebProxy();
                var uri = new Uri(targetUrl);
                var proxyUri = systemProxy.GetProxy(uri);
                
                if (proxyUri.ToString() == uri.ToString())
                {
                    results.AppendLine("   ✅ No proxy detected for this URL");
                }
                else
                {
                    results.AppendLine($"   🔍 Proxy detected: {proxyUri}");
                    results.AppendLine($"   📝 System proxy is configured");
                }
                results.AppendLine();

                // Test direct connection (no proxy)
                results.AppendLine("2. Direct Connection Test (No Proxy):");
                var directResult = await TestHttpConnectionAsync(targetUrl, useProxy: false);
                results.AppendLine($"   Result: {(directResult.Success ? "SUCCESS" : "FAILED")}");
                results.AppendLine($"   Details: {directResult.Details}");
                results.AppendLine();

                // Test with system proxy
                results.AppendLine("3. System Proxy Connection Test:");
                var proxyResult = await TestHttpConnectionAsync(targetUrl, useProxy: true);
                results.AppendLine($"   Result: {(proxyResult.Success ? "SUCCESS" : "FAILED")}");
                results.AppendLine($"   Details: {proxyResult.Details}");
                results.AppendLine();

                // Environment variables check
                results.AppendLine("4. Environment Variables Check:");
                var httpProxy = Environment.GetEnvironmentVariable("HTTP_PROXY") ?? 
                               Environment.GetEnvironmentVariable("http_proxy");
                var httpsProxy = Environment.GetEnvironmentVariable("HTTPS_PROXY") ?? 
                                Environment.GetEnvironmentVariable("https_proxy");
                var noProxy = Environment.GetEnvironmentVariable("NO_PROXY") ?? 
                             Environment.GetEnvironmentVariable("no_proxy");

                results.AppendLine($"   HTTP_PROXY: {httpProxy ?? "Not set"}");
                results.AppendLine($"   HTTPS_PROXY: {httpsProxy ?? "Not set"}");
                results.AppendLine($"   NO_PROXY: {noProxy ?? "Not set"}");
                results.AppendLine();

                // Recommendations
                results.AppendLine("5. Recommendations:");
                if (!directResult.Success && !proxyResult.Success)
                {
                    results.AppendLine("   ❌ Both direct and proxy connections failed");
                    results.AppendLine("   📋 Troubleshooting steps:");
                    results.AppendLine("   1. Check if outbound HTTPS (port 443) is allowed");
                    results.AppendLine("   2. Contact network administrator about firewall rules");
                    results.AppendLine("   3. Verify if corporate proxy authentication is required");
                    results.AppendLine("   4. Check if the target domain is whitelisted");
                    results.AppendLine("   5. Try accessing from a different network");
                }
                else if (directResult.Success && !proxyResult.Success)
                {
                    results.AppendLine("   ✅ Direct connection works, proxy connection fails");
                    results.AppendLine("   💡 Recommendation: Configure HttpClient to bypass proxy");
                }
                else if (!directResult.Success && proxyResult.Success)
                {
                    results.AppendLine("   ✅ Proxy connection works, direct connection fails");
                    results.AppendLine("   💡 Recommendation: Ensure HttpClient uses system proxy");
                }
                else
                {
                    results.AppendLine("   ✅ Both connections work - network connectivity is good");
                    results.AppendLine("   💡 Issue may be application-specific or authentication-related");
                }

            }
            catch (Exception ex)
            {
                results.AppendLine($"Error during proxy diagnostics: {ex.Message}");
            }

            return results.ToString();
        }

        /// <summary>
        /// Test HTTP connection with or without proxy
        /// </summary>
        private static async Task<(bool Success, string Details)> TestHttpConnectionAsync(string url, bool useProxy)
        {
            try
            {
                var handler = new HttpClientHandler();
                
                if (useProxy)
                {
                    handler.UseProxy = true;
                    handler.Proxy = WebRequest.GetSystemWebProxy();
                }
                else
                {
                    handler.UseProxy = false;
                    handler.Proxy = null;
                }

                using var client = new HttpClient(handler)
                {
                    Timeout = TimeSpan.FromSeconds(30)
                };

                client.DefaultRequestHeaders.Add("User-Agent", "NgocMaiTravel-ProxyDiagnostics/1.0");

                var stopwatch = Stopwatch.StartNew();
                var response = await client.GetAsync(url);
                stopwatch.Stop();

                var details = $"Status: {response.StatusCode}, Time: {stopwatch.ElapsedMilliseconds}ms";
                return (response.IsSuccessStatusCode, details);
            }
            catch (Exception ex)
            {
                return (false, $"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Test specific proxy configuration
        /// </summary>
        public static async Task<string> TestProxyConfigurationAsync(string proxyUrl, string targetUrl, string username = null, string password = null)
        {
            var results = new global::System.Text.StringBuilder();
            results.AppendLine($"=== Testing Proxy Configuration ===");
            results.AppendLine($"Proxy: {proxyUrl}");
            results.AppendLine($"Target: {targetUrl}");
            results.AppendLine($"Authentication: {(string.IsNullOrEmpty(username) ? "None" : "Provided")}");
            results.AppendLine();

            try
            {
                var proxy = new WebProxy(proxyUrl);
                
                if (!string.IsNullOrEmpty(username))
                {
                    proxy.Credentials = new NetworkCredential(username, password);
                }

                var handler = new HttpClientHandler()
                {
                    UseProxy = true,
                    Proxy = proxy
                };

                using var client = new HttpClient(handler)
                {
                    Timeout = TimeSpan.FromSeconds(30)
                };

                var stopwatch = Stopwatch.StartNew();
                var response = await client.GetAsync(targetUrl);
                stopwatch.Stop();

                results.AppendLine($"✅ Connection successful");
                results.AppendLine($"   Status: {response.StatusCode}");
                results.AppendLine($"   Time: {stopwatch.ElapsedMilliseconds}ms");
                results.AppendLine($"   Content Length: {response.Content.Headers.ContentLength ?? 0}");

                return results.ToString();
            }
            catch (Exception ex)
            {
                results.AppendLine($"❌ Connection failed");
                results.AppendLine($"   Error: {ex.Message}");
                results.AppendLine($"   Type: {ex.GetType().Name}");
                
                if (ex.InnerException != null)
                {
                    results.AppendLine($"   Inner Error: {ex.InnerException.Message}");
                }

                return results.ToString();
            }
        }

        /// <summary>
        /// Get current proxy configuration summary
        /// </summary>
        public static string GetProxyConfigurationSummary()
        {
            var results = new global::System.Text.StringBuilder();
            results.AppendLine("=== Current Proxy Configuration ===");

            try
            {
                var systemProxy = WebRequest.GetSystemWebProxy();
                results.AppendLine($"System Proxy Type: {systemProxy.GetType().Name}");

                // Test with a common URL
                var testUri = new Uri("https://www.google.com");
                var proxyUri = systemProxy.GetProxy(testUri);
                
                if (proxyUri.ToString() == testUri.ToString())
                {
                    results.AppendLine("System Proxy: Not configured or bypassed");
                }
                else
                {
                    results.AppendLine($"System Proxy: {proxyUri}");
                }

                // Check credentials
                var credentials = systemProxy.Credentials;
                results.AppendLine($"Proxy Credentials: {(credentials != null ? "Configured" : "None")}");

            }
            catch (Exception ex)
            {
                results.AppendLine($"Error getting proxy configuration: {ex.Message}");
            }

            return results.ToString();
        }
    }
}
