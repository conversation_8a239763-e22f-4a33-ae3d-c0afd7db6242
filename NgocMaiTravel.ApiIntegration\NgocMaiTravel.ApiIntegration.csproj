﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <EmbeddedResource Include="Resources\BookTripEmail.html" />
    <EmbeddedResource Include="Resources\SupportEndMail.html" />
    <EmbeddedResource Include="Resources\BookTripResult.html" />
    <EmbeddedResource Include="Resources\Ticket.html" />
    <EmbeddedResource Include="Resources\MainNoteResult.html" />
    <EmbeddedResource Include="Resources\NoteResult.html" />
  </ItemGroup>

  <ItemGroup>
    <OpenApiReference Include="OpenAPIs\swagger.json" ClassName="NMBooking">
      <SourceUri>https://prod-api-b2b.ngocmaitravel.vn/swagger/v1.3/swagger.json</SourceUri>
    </OpenApiReference>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MailKit" Version="4.11.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.Extensions.ApiDescription.Client" Version="7.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NSwag.ApiDescription.Client" Version="13.18.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>



	<ItemGroup>
    <ProjectReference Include="..\NgocMaiTravel.Data\NgocMaiTravel.Data.csproj" />
    <ProjectReference Include="..\NgocMaiTravel.ViewModels\NgocMaiTravel.ViewModels.csproj" />
  </ItemGroup>

</Project>
