{
  "Kestrel": {
    "Limits": {
      "MaxRequestBodySize": 1073741824 // 1 GB
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ConnectionStrings": {
    "NgocMaiTravelSolutionDb": "Data Source=localhost;Initial Catalog=NgocMaiTravel;Persist Security Info=True;User ID=sa;Password=************;TrustServerCertificate=True;"
    //"NgocMaiTravelSolutionDb": "Data Source=localhost;Initial Catalog=NgocMaiTravelTest;Persist Security Info=True;User ID=sa;Password=************;TrustServerCertificate=True;"
  },
  "AllowedHosts": "*",
  //"AllowedHosts": "ngocmaitravel.vn;api.ngocmaitravel;localhost",
  "Tokens": {
    "Key": "DangQuocVu@22102001_Raeiji_key_NgocMaiTravel_Storage_Solution_1231jhkjh12319asj9@#@#asda@#^@KJSKJ",
    "Issuer": "https://api.ngocmaitravel.vn"

  },
  "AuthSetupSettings": {
    "TokenProviderName": "ngocmaitravelProvider",
    "Audience": "https://api.ngocmaitravel.vn",
    "Issuer": "https://api.ngocmaitravel.vn",
    "SymmetricSecurityKeyString": "DangQuocVu@22102001_Raeiji_key_NgocMaiTravel_Storage_Solution_1231jhkjh12319asj9@#@#asda@#^@KJSKJ",
    "AuthTokenExpirySpanSeconds": 300
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/log-.txt",
          "rollingInterval": "Day"
        }
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithThreadId"
    ],
    "Properties": {
      "Application": "Flight.Api"
    }
  },
  "CtyptographySettings": {
    "signature": {
      "ID": "24dc902c-d55c-4dfb-a208-27f90407f0f9",
      "publicKey": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0NCk1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0NBUUVBeC9DT1ZhTWpQMGt5c0RiRlhkZkhyLzhQOEg5bEsveVoNCis2NERvZWdTSERSeENqb0NQMGxMZEtqb2VMa1hWaWE4UTBzQ3dzU1VIeHhIc3lkeWVhY0tUN0hHWG1WbFBaQXlJbHh1RjRPc0ZvWkoNCmEzQUVrSTdPMEpBeFFrcmFENzdURGJoeHBzSHdKUmtnWEI4aVR5NzQwOS83NjQ2VForcGdGTzMvSVlVT0M2SEplMEIvdkVadzkzeTkNCitodGlWa0ZtZEorQ2JkeGZyaEZrWHU5ZGRWeWptTENRODFIOTRJV2xOdm5PdlFHTkNpNWNHenpIZTVGTldpVy9PRGZrNkRKN0tJUUINCmlieUovVVoxdnJlKzExTytPWGZ0bkwwcHFybThvTnE5dURneWZQUW1US1Rxb0pDaGl6ZEFFTVlyNDVsQ3UybUJJNmVtVFhJWWVJYlANCm9uenZpUUlEQVFBQg0KLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tDQo=",
      "privateKey": "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"
    }
  },
  //"BaseAddress": "https://uat-api-b2b.ngocmaitravel.vn",
  "BaseAddress": "https://prod-api-b2b.ngocmaitravel.vn",
  "Momo": {
    "partnerCode": "MOMO79TT20250317",
    "accessKey": "9NtTXQUwYi3UreSL",
    "serectkey": "FljemTiFX52DrbS4q6hxsVZGdIX5VMZQ",
    "endpoint": "https://payment.momo.vn"
  },
  "VNPay": {
    "vnp_Url": "https://pay.vnpay.vn/vpcpay.html",
    "vnp_Api": "https://sandbox.vnpayment.vn/merchant_webapi/api/transaction", //Chưa dùng tới
    "vnp_TmnCode": "NMTRAVEL",
    "vnp_HashSecret": "JOOYD3H75BZMTBTVFW15YU1VPOU0W9XO",
    "vnp_Returnurl": "http://localhost:16262/vnpay_return.aspx" //đã thiết lập trong request
  },
  //"Momo": {
  //  "partnerCode": "MOMO79TT20250317_TEST",
  //  "accessKey": "j89BEViC4zYz3EcY",
  //  "serectkey": "KraKyW7aWI54Ax1rkt4zJybTwN1SShmR",
  //  "endpoint": "https://test-payment.momo.vn"
  //},
  //"VNPay": {
  //  "vnp_Url": "https://sandbox.vnpayment.vn/paymentv2/vpcpay.html",
  //  "vnp_Api": "https://sandbox.vnpayment.vn/merchant_webapi/api/transaction",
  //  "vnp_TmnCode": "NMTEST11",
  //  "vnp_HashSecret": "8NIEI8I6BPA77JM3RZCEZNDKNGIZO7TY",
  //  "vnp_Returnurl": "http://localhost:16262/vnpay_return.aspx"
  //},
  "EmailSettings": {
    "SmtpServer": "smtp.gmail.com",
    "Port": "587",
    "SenderEmail": "<EMAIL>",
    "Password": "zpikegeewtolyxfp"
  },
  "EmailReciveGDS": "<EMAIL>",
  "EmailReciveAdminDefault": "<EMAIL>",
  "Redis": {
    "Configuration": "localhost:6379,abortConnect=false,connectTimeout=15000",
    "InstanceName": "NMT_"
  },
  "Kafka": {
    "BootstrapServers": "localhost:9092",
    "Topic": "booking-events"
  },
  "ESimBlue": {
    "BaseUrl": "https://dev-api.peacom.co",
    "ApiKey": "b0159970-80bd-4685-b619-dce33796862a"
  },
  "ESimLogging": {
    "BasePath": "C:\\Logs\\ESim"
  }

}