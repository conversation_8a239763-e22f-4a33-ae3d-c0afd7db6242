using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NgocMaiTravel.Data.Entities.ESim
{
    /// <summary>
    /// ESim Group entity (Countries/Regions)
    /// </summary>
    [Table("tblESimGroup")]
    public class ESimGroup
    {
        /// <summary>
        /// Primary key
        /// </summary>
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// Country/Region code like 'VN', 'JP', 'ASIA'
        /// </summary>
        [Required]
        [StringLength(10)]
        public string GroupCode { get; set; } = string.Empty;

        /// <summary>
        /// Display name like 'Vietnam', 'Japan', 'Asia Region'
        /// </summary>
        [Required]
        [StringLength(255)]
        public string GroupName { get; set; } = string.Empty;

        /// <summary>
        /// Type: COUNTRY, REGION, GLOBAL
        /// </summary>
        [Required]
        [StringLength(50)]
        public string GroupType { get; set; } = "COUNTRY";

        /// <summary>
        /// Optional description
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// Whether the group is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for sorting
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// User who created the record
        /// </summary>
        [StringLength(255)]
        public string? CreatedBy { get; set; }

        /// <summary>
        /// User who last updated the record
        /// </summary>
        [StringLength(255)]
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// Navigation property to group-package mappings
        /// </summary>
        public virtual ICollection<ESimGroupPackage> GroupPackages { get; set; } = new List<ESimGroupPackage>();

        /// <summary>
        /// Navigation property to packages (through GroupPackages)
        /// </summary>
        public virtual IEnumerable<ESimPackage> Packages => GroupPackages.Select(gp => gp.Package);
    }
}
