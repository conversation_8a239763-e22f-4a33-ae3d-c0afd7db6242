﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI
{
    public partial class BalanceRp
    {
        [JsonProperty("companyId")]
        public long CompanyId { get; set; }

        [JsonProperty("balance")]
        public long Balance { get; set; }

        [JsonProperty("currency")]
        public Currency Currency { get; set; }
    }

    public partial class Currency
    {
        [JsonProperty("id")]
        public long Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("code")]
        public string Code { get; set; }
    }


}
