# SearchPlans Request Deduplication Guide

## 📋 Overview

Hệ thống đã được implement request deduplication cho `SearchPlansAsync` method. <PERSON><PERSON> <PERSON><PERSON> nhiề<PERSON> requests giống nhau gửi đến cùng lúc, hệ thống sẽ:

1. **<PERSON><PERSON><PERSON> requests** - Chỉ thực hiện 1 API call thực tế
2. **Chia sẻ kết quả** - <PERSON><PERSON><PERSON> c<PERSON> requests nhận cùng 1 kết quả
3. **Cache ngắn hạn** - Kết quả được cache 30 giây

## 🔧 How It Works

### **Request Key Generation:**
```csharp
// Tạo unique key dựa trên request parameters
var requestKey = ESimRequestKeyGenerator.GenerateSearchPlansKey(rq);
// Example: "search_plans_a1b2c3d4e5f6"
```

### **Deduplication Logic:**
```csharp
public async Task<ApiResult<EsimPackagesRp>> SearchPlansAsync(EsimPackagesRq rq)
{
    // Generate unique key for request deduplication
    var requestKey = ESimRequestKeyGenerator.GenerateSearchPlansKey(rq);
    
    // Use deduplication service to handle concurrent requests
    return await _deduplicationService.ExecuteAsync(
        key: requestKey,
        factory: () => ExecuteSearchPlansInternalAsync(rq),
        cacheDurationSeconds: 30 // Cache for 30 seconds
    );
}
```

## 📊 Key Generation Logic

### **Parameters Used:**
- `type` (normalized to lowercase)
- `sku` (normalized to lowercase)  
- `locationCode` (normalized to lowercase)
- `unnamed` (normalized to lowercase)

### **Example Keys:**
```csharp
// Request 1: { type: "Vietnam", locationCode: "VN" }
// Key: "search_plans_a1b2c3d4"

// Request 2: { type: "vietnam", locationCode: "vn" }  
// Key: "search_plans_a1b2c3d4" (same as Request 1)

// Request 3: { type: "Thailand", locationCode: "TH" }
// Key: "search_plans_e5f6g7h8" (different)
```

## 🚀 Benefits

### **1. Performance Improvement:**
- **Reduced API Calls**: 10 concurrent identical requests → 1 actual API call
- **Faster Response**: Subsequent requests get cached result instantly
- **Lower Latency**: No waiting for duplicate API calls

### **2. Resource Optimization:**
- **Reduced Server Load**: Less CPU/memory usage
- **Lower Network Traffic**: Fewer external API calls
- **Cost Savings**: Reduced third-party API usage

### **3. Better User Experience:**
- **Consistent Results**: All users get same data
- **Faster Loading**: Cached responses are instant
- **No Race Conditions**: Synchronized access to same data

## 📈 Performance Metrics

### **Before Deduplication:**
```
10 concurrent requests → 10 API calls → 10 × 500ms = 5000ms total
```

### **After Deduplication:**
```
10 concurrent requests → 1 API call → 1 × 500ms = 500ms total
Cache hits: 9 × ~1ms = 9ms total
```

### **Improvement:**
- **90% reduction** in API calls
- **~50x faster** for concurrent requests
- **Significant cost savings**

## 🔍 Monitoring & Debugging

### **Logging:**
```csharp
_logger.LogDebug("SearchPlansAsync called with key: {RequestKey}", requestKey);
_logger.LogInformation("Executing SearchPlans request for traceId: {TraceId}", traceId);
```

### **Cache Statistics:**
```csharp
var stats = _deduplicationService.GetCacheStats();
// Returns:
// {
//   "TotalRequests": 100,
//   "CacheHits": 75,
//   "Deduplications": 60,
//   "CacheHitRate": 75.0,
//   "DeduplicationRate": 60.0
// }
```

## 🧪 Testing Scenarios

### **Scenario 1: Identical Concurrent Requests**
```javascript
// Send 5 identical requests simultaneously
const requests = Array(5).fill().map(() => 
    fetch('/api/ESim/search?type=vietnam&locationCode=VN')
);

const responses = await Promise.all(requests);
// Result: Only 1 actual API call, all get same response
```

### **Scenario 2: Similar but Different Requests**
```javascript
// These will be treated as different requests
const request1 = fetch('/api/ESim/search?type=vietnam&locationCode=VN');
const request2 = fetch('/api/ESim/search?type=thailand&locationCode=TH');

// Result: 2 separate API calls (different keys)
```

### **Scenario 3: Case Insensitive Deduplication**
```javascript
// These will be deduplicated (same normalized key)
const request1 = fetch('/api/ESim/search?type=Vietnam&locationCode=VN');
const request2 = fetch('/api/ESim/search?type=vietnam&locationCode=vn');

// Result: Only 1 API call (same normalized key)
```

## ⚙️ Configuration

### **Cache Duration:**
```csharp
// Current: 30 seconds
cacheDurationSeconds: 30

// Can be adjusted based on needs:
// - Shorter (10s): More fresh data, less deduplication
// - Longer (60s): More deduplication, potentially stale data
```

### **Key Normalization:**
```csharp
private static string NormalizeString(string? value)
{
    if (string.IsNullOrWhiteSpace(value))
        return string.Empty;

    return value.Trim().ToLowerInvariant();
}
```

## 🚨 Important Considerations

### **1. Cache Invalidation:**
- Cache expires after 30 seconds
- No manual invalidation currently implemented
- Consider adding manual clear for critical updates

### **2. Memory Usage:**
- Each cached result uses memory
- Automatic cleanup of expired entries
- Monitor memory usage in production

### **3. Error Handling:**
- Failed requests are not cached
- Each request gets individual error handling
- No shared error states

### **4. Customer Context:**
- `SearchPlansAsync` doesn't include customer info in key
- All customers share same cached results
- Use `SearchPlansWithContextAsync` for customer-specific logging

## 📝 Usage Examples

### **Frontend JavaScript:**
```javascript
// Multiple users searching same criteria
async function searchPlans() {
    const response = await fetch('/api/ESim/search?type=vietnam&locationCode=VN');
    const result = await response.json();
    
    // First request: ~500ms (actual API call)
    // Subsequent requests: ~1ms (cached result)
    return result;
}
```

### **Load Testing:**
```bash
# Test concurrent requests
for i in {1..10}; do
    curl -X GET "http://localhost:5000/api/ESim/search?type=vietnam&locationCode=VN" &
done
wait

# Result: Only 1 actual API call logged
```

### **Monitoring Query:**
```sql
-- Check deduplication effectiveness
SELECT 
    Action,
    COUNT(*) as TotalLogs,
    COUNT(DISTINCT TraceId) as UniqueRequests,
    (COUNT(*) - COUNT(DISTINCT TraceId)) as DeduplicatedRequests
FROM tblESimLogs 
WHERE Action = 'search_plans'
    AND Timestamp >= DATEADD(hour, -1, GETDATE())
GROUP BY Action;
```

## ✅ Best Practices

1. **Monitor Cache Hit Rates**: Aim for >50% hit rate during peak times
2. **Adjust Cache Duration**: Balance freshness vs performance
3. **Log Key Generation**: Debug duplicate key issues
4. **Test Edge Cases**: Empty parameters, special characters
5. **Monitor Memory**: Watch for memory leaks in cache

This deduplication system significantly improves performance for concurrent identical requests while maintaining data consistency and reducing external API costs.
