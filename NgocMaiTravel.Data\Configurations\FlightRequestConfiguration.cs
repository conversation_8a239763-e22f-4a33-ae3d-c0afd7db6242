﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NgocMaiTravel.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Data.Configurations
{
    public class FlightRequestConfiguration : IEntityTypeConfiguration<FlightRequest>
    {
        public void Configure(EntityTypeBuilder<FlightRequest> builder)
        {
            builder.ToTable("FlightRequests");
            builder.HasKey(x => x.Id);
            builder.Property(x => x.Id).IsRequired().HasMaxLength(15);
            builder.Property(x => x.Depart).IsRequired().HasMaxLength(50);
            builder.Property(x => x.Arrival).IsRequired().HasMaxLength(50);
            builder.Property(x => x.DepartDate).IsRequired();
            builder.Property(x => x.ReturnDate).IsRequired(false);
            builder.Property(x => x.Adult).IsRequired();
            builder.Property(x => x.Child).IsRequired();
            builder.Property(x => x.Infant).IsRequired();
            builder.Property(x => x.CustomerName).IsRequired().HasMaxLength(100);
            builder.Property(x => x.PhoneNumber).IsRequired().HasMaxLength(15);
            builder.Property(x => x.Email).IsRequired().HasMaxLength(50);
            builder.Property(x => x.Status).HasDefaultValue(0);
            builder.Property(x => x.TimeCreate).HasDefaultValueSql("GETDATE()");
            builder.Property(x => x.TimeCompletion).IsRequired(false);
            builder.Property(x => x.PaymentMethod).HasMaxLength(50);
            builder.Property(x => x.Pnrs).HasMaxLength(50);
            builder.Property(x => x.TicketNumbers).HasMaxLength(50);
            builder.Property(x=>x.TotalPrice).HasDefaultValue(0);
            builder.Property(x=>x.TransactionId).IsRequired(false);
            builder.Property(x=>x.OwnerID).IsRequired(false);
            builder.Property(x => x.UserNote).IsRequired(false).HasMaxLength(255);

            // Voucher configuration
            builder.Property(x => x.VoucherCode).HasMaxLength(50).IsRequired(false);
            builder.Property(x => x.VoucherDiscount).HasColumnType("decimal(18,2)").IsRequired(false);

            builder.HasIndex(x => x.Depart);
            builder.HasIndex(x => x.PhoneNumber);
            builder.HasIndex(x => x.Arrival);
        }
    }
}
