using System;
using System.Diagnostics;
using System.Net;
using System.Net.NetworkInformation;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Common.Diagnostics
{
    /// <summary>
    /// HttpClient and network diagnostics utilities
    /// </summary>
    public static class HttpClientDiagnostics
    {
        /// <summary>
        /// Log current ServicePointManager settings
        /// </summary>
        public static void LogServicePointManagerSettings()
        {
            try
            {
                Debug.WriteLine("=== ServicePointManager Settings ===");
                Debug.WriteLine($"DefaultConnectionLimit: {ServicePointManager.DefaultConnectionLimit}");
                Debug.WriteLine($"Expect100Continue: {ServicePointManager.Expect100Continue}");
                Debug.WriteLine($"UseNagleAlgorithm: {ServicePointManager.UseNagleAlgorithm}");
                Debug.WriteLine($"EnableDnsRoundRobin: {ServicePointManager.EnableDnsRoundRobin}");
                Debug.WriteLine($"DnsRefreshTimeout: {ServicePointManager.DnsRefreshTimeout}ms");
                Debug.WriteLine($"MaxServicePointIdleTime: {ServicePointManager.MaxServicePointIdleTime}ms");
                Debug.WriteLine($"MaxServicePoints: {ServicePointManager.MaxServicePoints}");
                Debug.WriteLine("");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error logging ServicePointManager settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Get ServicePoint information for a specific URI
        /// </summary>
        public static void LogServicePointInfo(string uri)
        {
            try
            {
                var servicePoint = ServicePointManager.FindServicePoint(new Uri(uri));
                Debug.WriteLine($"=== ServicePoint Info for {uri} ===");
                Debug.WriteLine($"Address: {servicePoint.Address}");
                Debug.WriteLine($"ConnectionLimit: {servicePoint.ConnectionLimit}");
                Debug.WriteLine($"CurrentConnections: {servicePoint.CurrentConnections}");
                Debug.WriteLine($"IdleSince: {servicePoint.IdleSince}");
                Debug.WriteLine($"MaxIdleTime: {servicePoint.MaxIdleTime}ms");
                Debug.WriteLine($"ProtocolVersion: {servicePoint.ProtocolVersion}");
                Debug.WriteLine($"SupportsPipelining: {servicePoint.SupportsPipelining}");
                Debug.WriteLine($"UseNagleAlgorithm: {servicePoint.UseNagleAlgorithm}");
                Debug.WriteLine($"Expect100Continue: {servicePoint.Expect100Continue}");
                Debug.WriteLine("");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting ServicePoint info for {uri}: {ex.Message}");
            }
        }

        /// <summary>
        /// Test HTTP connectivity with detailed diagnostics
        /// </summary>
        public static async Task<string> TestHttpConnectivityAsync(string url)
        {
            var results = new global::System.Text.StringBuilder();
            results.AppendLine($"=== HTTP Connectivity Test for {url} ===");
            results.AppendLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            results.AppendLine();

            try
            {
                var uri = new Uri(url);
                
                // Log ServicePoint info before request
                LogServicePointInfo(url);
                
                // Create HttpClient with same configuration as production
                using var handler = new HttpClientHandler()
                {
                    MaxConnectionsPerServer = 20,
                    UseProxy = false,
                    UseCookies = false,
                    PreAuthenticate = false,
                    AllowAutoRedirect = false,
                    AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
                    MaxRequestContentBufferSize = 1024 * 1024 * 10,
                    MaxResponseHeadersLength = 1024 * 64,
                    MaxAutomaticRedirections = 0
                };

                using var client = new HttpClient(handler)
                {
                    BaseAddress = uri,
                    Timeout = TimeSpan.FromSeconds(30)
                };

                client.DefaultRequestHeaders.Add("User-Agent", "NgocMaiTravel-Diagnostics/1.0");
                client.DefaultRequestHeaders.Add("Connection", "keep-alive");
                client.DefaultRequestHeaders.ConnectionClose = false;

                var stopwatch = Stopwatch.StartNew();
                
                // Test basic connectivity
                results.AppendLine("1. Basic HTTP GET Test:");
                try
                {
                    var response = await client.GetAsync("/");
                    stopwatch.Stop();
                    
                    results.AppendLine($"   Status: {response.StatusCode}");
                    results.AppendLine($"   Response Time: {stopwatch.ElapsedMilliseconds}ms");
                    results.AppendLine($"   Content Length: {response.Content.Headers.ContentLength ?? 0} bytes");
                    results.AppendLine($"   Server: {response.Headers.Server}");
                    results.AppendLine($"   Connection: {response.Headers.Connection}");
                    results.AppendLine("   Result: SUCCESS");
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    results.AppendLine($"   Error: {ex.Message}");
                    results.AppendLine($"   Time: {stopwatch.ElapsedMilliseconds}ms");
                    results.AppendLine("   Result: FAILED");
                }
                results.AppendLine();

                // Test multiple concurrent requests
                results.AppendLine("2. Concurrent Requests Test (5 requests):");
                var tasks = new Task[5];
                var concurrentStopwatch = Stopwatch.StartNew();
                
                for (int i = 0; i < 5; i++)
                {
                    int requestId = i + 1;
                    tasks[i] = Task.Run(async () =>
                    {
                        try
                        {
                            var sw = Stopwatch.StartNew();
                            var resp = await client.GetAsync("/");
                            sw.Stop();
                            results.AppendLine($"   Request {requestId}: {resp.StatusCode} ({sw.ElapsedMilliseconds}ms)");
                        }
                        catch (Exception ex)
                        {
                            results.AppendLine($"   Request {requestId}: FAILED - {ex.Message}");
                        }
                    });
                }
                
                await Task.WhenAll(tasks);
                concurrentStopwatch.Stop();
                results.AppendLine($"   Total Time: {concurrentStopwatch.ElapsedMilliseconds}ms");
                results.AppendLine();

                // Log ServicePoint info after requests
                results.AppendLine("3. ServicePoint Status After Requests:");
                LogServicePointInfo(url);
                
            }
            catch (Exception ex)
            {
                results.AppendLine($"Error during HTTP connectivity test: {ex.Message}");
            }

            return results.ToString();
        }

        /// <summary>
        /// Monitor network performance metrics
        /// </summary>
        public static void LogNetworkPerformanceMetrics()
        {
            try
            {
                Debug.WriteLine("=== Network Performance Metrics ===");
                
                // Get network interfaces
                var interfaces = NetworkInterface.GetAllNetworkInterfaces();
                foreach (var ni in interfaces)
                {
                    if (ni.OperationalStatus == OperationalStatus.Up && 
                        ni.NetworkInterfaceType != NetworkInterfaceType.Loopback)
                    {
                        var stats = ni.GetIPv4Statistics();
                        Debug.WriteLine($"Interface: {ni.Name}");
                        Debug.WriteLine($"  Bytes Sent: {stats.BytesSent:N0}");
                        Debug.WriteLine($"  Bytes Received: {stats.BytesReceived:N0}");
                        Debug.WriteLine($"  Packets Sent: {stats.UnicastPacketsSent:N0}");
                        Debug.WriteLine($"  Packets Received: {stats.UnicastPacketsReceived:N0}");
                        Debug.WriteLine($"  Errors: {stats.IncomingPacketsWithErrors + stats.OutgoingPacketsWithErrors}");
                        Debug.WriteLine("");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting network performance metrics: {ex.Message}");
            }
        }
    }
}
