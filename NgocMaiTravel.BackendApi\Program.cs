﻿using Confluent.Kafka;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Cors.Infrastructure;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using NgocMaiTravel.ApiIntegration;
using NgocMaiTravel.ApiIntegration.Common.Logger;
using NgocMaiTravel.ApiIntegration.Email;
using NgocMaiTravel.ApiIntegration.NMBookingAPI;
using NgocMaiTravel.ApiIntegration.Payment.MoMo;
using NgocMaiTravel.ApiIntegration.Payment.VNPay;
using NgocMaiTravel.ApiIntegration.Queue;
using NgocMaiTravel.ApiIntegration.RateLimiter.BookTrip;
using NgocMaiTravel.ApiIntegration.VoucherRedis;
using NgocMaiTravel.Application.Catalog.Address;
using NgocMaiTravel.Application.Catalog.Airport;
using NgocMaiTravel.Application.Catalog.CategoryAirport;
using NgocMaiTravel.Application.Catalog.CategoryHotel;
using NgocMaiTravel.Application.Catalog.CategoryNews;
using NgocMaiTravel.Application.Catalog.CategoryTours;
using NgocMaiTravel.Application.Catalog.CategoryVisas;
using NgocMaiTravel.Application.Catalog.ContactCooperation;
using NgocMaiTravel.Application.Catalog.Countries;
using NgocMaiTravel.Application.Catalog.ESim;
using NgocMaiTravel.Application.Common.Logging;
using NgocMaiTravel.Application.Common.Services;
using NgocMaiTravel.ApiIntegration.ESimBlueAPI;
using NgocMaiTravel.ApiIntegration.Esim;
//using NgocMaiTravel.BackendApi.ApiIntegration.NMBookingAPI;

using NgocMaiTravel.Application.Catalog.FareRule;
using NgocMaiTravel.Application.Catalog.Feature;
using NgocMaiTravel.Application.Catalog.FileStorage;
using NgocMaiTravel.Application.Catalog.Flight;
using NgocMaiTravel.Application.Catalog.Flight.ValidatorBooking;
using NgocMaiTravel.Application.Catalog.Holiday;
using NgocMaiTravel.Application.Catalog.Hotels;
using NgocMaiTravel.Application.Catalog.HtmlService;
using NgocMaiTravel.Application.Catalog.ImageTour;
using NgocMaiTravel.Application.Catalog.News_s;
using NgocMaiTravel.Application.Catalog.PaymentFeeConfig;
using NgocMaiTravel.Application.Catalog.Promotion;
using NgocMaiTravel.Application.Catalog.Provinces;
using NgocMaiTravel.Application.Catalog.QRCodes;
using NgocMaiTravel.Application.Catalog.Report;
using NgocMaiTravel.Application.Catalog.TicketIssueFee;
using NgocMaiTravel.Application.Catalog.Tours;
using NgocMaiTravel.Application.Catalog.Visa;
using NgocMaiTravel.Application.Catalog.Voucher;
using NgocMaiTravel.Application.Catalog.World;
using NgocMaiTravel.Application.Common;
using NgocMaiTravel.Application.Common.Crypto;
using NgocMaiTravel.Application.Common.DiffieHellman;
using NgocMaiTravel.Application.Hub;
using NgocMaiTravel.Application.System.ActivityLog;
using NgocMaiTravel.Application.System.AntiDoubleClick;
using NgocMaiTravel.Application.System.GroupUser;
using NgocMaiTravel.Application.System.KafkaConsumer;
using NgocMaiTravel.Application.System.KafkaProducer;
using NgocMaiTravel.Application.System.Origin;
using NgocMaiTravel.Application.System.Roles;
using NgocMaiTravel.Application.System.Screens;
using NgocMaiTravel.Application.System.TokenBlacklist;
using NgocMaiTravel.Application.System.Translate;
using NgocMaiTravel.Application.System.Users;
using NgocMaiTravel.Application.System.XApiKey;
using NgocMaiTravel.Application.System.XDevice;
using NgocMaiTravel.Application.Utilities;
using NgocMaiTravel.BackendApi;
using NgocMaiTravel.BackendApi.Handler;
using NgocMaiTravel.BackendApi.Hub;
using NgocMaiTravel.BackendApi.MiddleWare;
using NgocMaiTravel.BackendApi.MiddleWare.ApiKey;
using NgocMaiTravel.Data.EF;
using NgocMaiTravel.Data.Entities;
using NgocMaiTravel.Utilities.Constants;
using NgocMaiTravel.ViewModels.System.Kafka;
using NgocMaiTravel.ViewModels.Utilities.Auth;
using Serilog;
using StackExchange.Redis;
using System.IO.Compression;
using System.Reflection;
using static NgocMaiTravel.Utilities.Constants.SystemConstants;
using Microsoft.Extensions.Options;
using System.Net;

// Configure ServicePointManager for better network performance
ServicePointManager.DefaultConnectionLimit = 100;
ServicePointManager.Expect100Continue = false;
ServicePointManager.UseNagleAlgorithm = false;
ServicePointManager.EnableDnsRoundRobin = true;
ServicePointManager.DnsRefreshTimeout = 120000; // 2 minutes

var builder = WebApplication.CreateBuilder(args);
builder.WebHost.ConfigureKestrel(options =>
{
    options.Limits.MaxRequestBodySize = 1_073_741_824; // 1 GB
});

#region redis setting
// Get Redis configuration from appsettings
var redisConfiguration = builder.Configuration.GetSection("Redis:Configuration").Value;
var instanceName = builder.Configuration.GetSection("Redis:InstanceName").Value;

// Enhance Redis configuration to avoid connection issues
var options = ConfigurationOptions.Parse(redisConfiguration);
options.AbortOnConnectFail = false;   // Avoid aborting connection when Redis is unavailable
options.ConnectTimeout = 15000;        // Set connection timeout to 15 seconds
options.SyncTimeout = 15000;           // Set sync operation timeout to 15 seconds
options.KeepAlive = 180;              // Set keep-alive interval in seconds

// Configure Redis connection and register it in DI container
builder.Services.AddSingleton<IConnectionMultiplexer>(ConnectionMultiplexer.Connect(options));
builder.Services.AddSingleton<IDatabase>(sp =>
{
    var connectionMultiplexer = sp.GetRequiredService<IConnectionMultiplexer>();
    return connectionMultiplexer.GetDatabase();
});

// Add StackExchangeRedisCache to the container
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = redisConfiguration;
    options.InstanceName = instanceName;
});
#endregion end redis setting




// Set up Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration) // Read config from appsettings.json
    .Enrich.FromLogContext()
    //.WriteTo.Console()
    //.WriteTo.File("logs/log-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

// Replace the default logging provider with Serilog
builder.Host.UseSerilog();

builder.Services.AddDbContext<NgocMaiTravelDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString(SystemConstants.MainConnectionString));
    options.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
}, ServiceLifetime.Transient);

#region cookie settings
//// Đăng ký dịch vụ IHttpContextAccessor
// Configure default HttpClient with proper settings
builder.Services.AddHttpClient(Options.DefaultName, client =>
{
    client.Timeout = TimeSpan.FromSeconds(100); // Default timeout
    client.DefaultRequestHeaders.Add("User-Agent", "NgocMaiTravel-API/1.0");
})
.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
{
    MaxConnectionsPerServer = 20, // Allow more concurrent connections
    UseProxy = false,
    UseCookies = false // Disable cookies for API calls
});

builder.Services.AddHttpContextAccessor();
//builder.Services.Configure<CookiePolicyOptions>(options =>
//{
//    options.MinimumSameSitePolicy = SameSiteMode.None;
//    options.OnAppendCookie = cookieContext =>
//    {
//        CheckSameSite(cookieContext.Context, cookieContext.CookieOptions);
//    };
//    options.OnDeleteCookie = cookieContext =>
//    {
//        CheckSameSite(cookieContext.Context, cookieContext.CookieOptions);
//    };
//});
// Thêm vào sau cấu hình CORS
builder.Services.Configure<CookiePolicyOptions>(options =>
{
    options.MinimumSameSitePolicy = SameSiteMode.Unspecified;
    options.HttpOnly = Microsoft.AspNetCore.CookiePolicy.HttpOnlyPolicy.Always;
    options.Secure = CookieSecurePolicy.Always;
    options.CheckConsentNeeded = context => false;
    options.OnAppendCookie = cookieContext =>
    {
        cookieContext.CookieOptions.SameSite = SameSiteMode.None;
        cookieContext.CookieOptions.Secure = true;
    };
    options.OnDeleteCookie = cookieContext =>
    {
        cookieContext.CookieOptions.SameSite = SameSiteMode.None;
        cookieContext.CookieOptions.Secure = true;
    };
});

// Thêm hàm phát hiện trình duyệt di động
bool DisallowsSameSiteNone(string userAgent)
{
    // Kiểm tra iOS 12 hoặc cũ hơn
    if (userAgent.Contains("CPU iPhone OS 12") || userAgent.Contains("iPad; CPU OS 12"))
    {
        return true;
    }

    // Kiểm tra macOS Safari 10-12
    if (userAgent.Contains("Safari") && !userAgent.Contains("Chrome") && 
        userAgent.Contains("Macintosh; Intel Mac OS X 10_") && 
        (userAgent.Contains("Version/10") || userAgent.Contains("Version/11") || userAgent.Contains("Version/12")))
    {
        return true;
    }

    // Kiểm tra Chrome 51-66 trên iOS
    if (userAgent.Contains("CriOS/5") || userAgent.Contains("CriOS/6"))
    {
        return true;
    }

    // Kiểm tra Chrome 51-66 trên các nền tảng khác
    if (userAgent.Contains("Chrome/5") || userAgent.Contains("Chrome/6"))
    {
        return true;
    }

    // Kiểm tra UC Browser
    if (userAgent.Contains("UCBrowser/"))
    {
        return true;
    }

    return false;
}

void CheckSameSite(HttpContext httpContext, CookieOptions options)
{
    if (options.SameSite == SameSiteMode.None)
    {
        var userAgent = httpContext.Request.Headers["User-Agent"].ToString();
        if (DisallowsSameSiteNone(userAgent))
        {
            // Đối với trình duyệt không hỗ trợ SameSite=None
            options.SameSite = SameSiteMode.Unspecified;
        }
        options.Secure = true;
    }
}
#endregion

IAuthSetupSecrets authSetupSettings = null;
IAuthSettings authSettings = null;
var authSetupSettingsSection = builder.Configuration.GetSection("AuthSetupSettings");
var authSetupSettingsObject = new AuthSetupSettings();
authSetupSettingsSection.Bind(authSetupSettingsObject);
authSetupSettings = authSetupSettingsObject;
authSettings = authSetupSettingsObject;

var issuerSigningKey = new SymmetricSecurityKey(System.Text.Encoding.UTF8.GetBytes(authSetupSettings.SymmetricSecurityKeyString));
builder.Services.AddSingleton(issuerSigningKey);

builder.Services.AddSingleton(authSettings);

builder.Services.AddIdentity<AppUser, AppRole>(options =>
{
    // Cấu hình Lockout (khóa tài khoản)
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5); // Thời gian bị khóa
    options.Lockout.MaxFailedAccessAttempts = 5; // Số lần đăng nhập sai tối đa
    options.Lockout.AllowedForNewUsers = true; // Áp dụng cho user mới
})
    .AddEntityFrameworkStores<NgocMaiTravelDbContext>()
.AddDefaultTokenProviders()
.AddTokenProvider(authSettings.TokenProviderName, typeof(DataProtectorTokenProvider<AppUser>));

// Configure NMBooking with proper HttpClient settings
builder.Services.AddHttpClient("NMBookingClient", client =>
{
    client.BaseAddress = new Uri("https://prod-api-b2b.ngocmaitravel.vn");
    client.Timeout = TimeSpan.FromSeconds(120); // 2 minutes timeout
    client.DefaultRequestHeaders.Add("User-Agent", "NgocMaiTravel-API/1.0");
    client.DefaultRequestHeaders.Add("Connection", "keep-alive");
    client.DefaultRequestHeaders.ConnectionClose = false; // Keep connections alive
})
.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
{
    MaxConnectionsPerServer = 20, // Increase concurrent connections
    UseProxy = false, // Disable proxy - this might be the issue on server
    Proxy = null, // Explicitly set proxy to null
    UseCookies = false,
    PreAuthenticate = false,
    AllowAutoRedirect = false, // Disable automatic redirects
    AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
    MaxRequestContentBufferSize = 1024 * 1024 * 10, // 10MB buffer
    MaxResponseHeadersLength = 1024 * 64 // 64KB headers
});

// Alternative NMBooking client with system proxy (for testing)
builder.Services.AddHttpClient("NMBookingClientWithProxy", client =>
{
    client.BaseAddress = new Uri("https://prod-api-b2b.ngocmaitravel.vn");
    client.Timeout = TimeSpan.FromSeconds(120);
    client.DefaultRequestHeaders.Add("User-Agent", "NgocMaiTravel-API/1.0");
    client.DefaultRequestHeaders.Add("Connection", "keep-alive");
    client.DefaultRequestHeaders.ConnectionClose = false;
})
.ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
{
    MaxConnectionsPerServer = 20,
    UseProxy = true, // Use system proxy
    Proxy = WebRequest.GetSystemWebProxy(), // Use system proxy settings
    UseCookies = false,
    PreAuthenticate = false,
    AllowAutoRedirect = false,
    AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
    MaxRequestContentBufferSize = 1024 * 1024 * 10,
    MaxResponseHeadersLength = 1024 * 64
});

// Register NMBooking with configured HttpClient
builder.Services.AddSingleton<NMBooking>(services =>
{
    var httpClientFactory = services.GetRequiredService<IHttpClientFactory>();
    var httpClient = httpClientFactory.CreateClient("NMBookingClient");
    return new NMBooking("https://prod-api-b2b.ngocmaitravel.vn", httpClient);
});

// ESimBlue client configuration
//builder.Services.AddSingleton<ESimBlue>(services =>
//{
//    var configuration = services.GetRequiredService<IConfiguration>();
//    var baseUrl = configuration["ESimBlue:BaseUrl"] ?? "https://dev-api.peacom.co";
//    var apiKey = configuration["ESimBlue:ApiKey"];
//    var httpClient = new HttpClient();
//    if (!string.IsNullOrEmpty(apiKey))
//    {
//        httpClient.DefaultRequestHeaders.Add("x-api-key", apiKey);
//    }
//    return new ESimBlue(baseUrl, httpClient);
//});

builder.Services.AddScoped<IOriginService, OriginService>();
builder.Services.AddSingleton<ICorsPolicyProvider, CustomCorsPolicyProvider>();



// Add services to the container.
builder.Services.AddScoped<IUserStore<AppUser>, UserStore<AppUser, AppRole, NgocMaiTravelDbContext, Guid>>();
builder.Services.AddTransient<UserManager<AppUser>, UserManager<AppUser>>();
builder.Services.AddTransient<SignInManager<AppUser>, SignInManager<AppUser>>();
builder.Services.AddTransient<RoleManager<AppRole>, RoleManager<AppRole>>();

//builder.Services.AddTransient<IRoleService, RoleService>();
builder.Services.AddTransient<IUserService, UserService>();
builder.Services.AddTransient<IFileService, FileService>();
builder.Services.AddTransient<IFileStorageService, FileStorageService>();
builder.Services.AddTransient<IRoleService, RoleService>();
builder.Services.AddTransient<ICategoryNewsService, CategoryNewsService>();
builder.Services.AddTransient<INewsService, NewsService>();
builder.Services.AddTransient<ICategoryToursService, CategoryToursService>();
builder.Services.AddTransient<IAddressService, AddressService>();
builder.Services.AddTransient<IImageTourService, ImageTourService>();
builder.Services.AddTransient<IHtmlProcessorService, HtmlProcessorService>();
builder.Services.AddTransient<IToursService, ToursService>();
builder.Services.AddTransient<ICategoryHotelService, CategoryHotelService>();
builder.Services.AddTransient<IHotelService, HotelService>();
builder.Services.AddScoped<IPromotionService, PromotionService>();
builder.Services.AddTransient<IContactService, ContactService>();
builder.Services.AddTransient<IVisaService, VisaService>();
builder.Services.AddTransient<IQRCodeService, QRCodeService>();
builder.Services.AddTransient<IProvinceService, ProvinceService>();
builder.Services.AddTransient<ICountryService, CountryService>();
builder.Services.AddTransient<IHolidayService, HolidayService>();
builder.Services.AddTransient<IFlightService, FlightService>();
builder.Services.AddTransient<ICategoryAirportService, CategoryAirportService>();
builder.Services.AddTransient<IAirportService, AirportService>();
builder.Services.AddTransient<ICategoryVisaService, CategoryVisaService>();
builder.Services.AddTransient<ICryptoService, CryptoService>();
builder.Services.AddTransient<IScreenService, ScreenService>();
builder.Services.AddTransient<ITranslateService, TranslateService>();
builder.Services.AddTransient<IWorldService, WorldService>();
builder.Services.AddTransient<IGroupUserService, GroupUserService>();
builder.Services.AddTransient<IReportService, ReportService>();
builder.Services.AddTransient<ICookie, HttpCookie>();
builder.Services.AddTransient<IXDeviceService, XDeviceService>();
builder.Services.AddTransient<IDiffieHellmanService, DiffieHellmanService>();
builder.Services.AddTransient<INMBookingApiClient, NMBookingApiClient>();
builder.Services.AddTransient<ILoggerService, LoggerService>();
builder.Services.AddTransient<IFareRuleService, FareRuleService>();
builder.Services.AddTransient<IMoMoService, MoMoService>();
builder.Services.AddTransient<IVNPayService, VNPayService>();
builder.Services.AddTransient<IFeatureService, FeatureService>();
builder.Services.AddTransient<IBookingRateLimiter, BookingRateLimiter>();
builder.Services.AddTransient<IEmailService, EmailService>();
builder.Services.AddTransient<IXApiKeyService, XApiKeyService>();
builder.Services.AddTransient<IScoreService, ScoreService>();
builder.Services.AddTransient<ITicketIssueFeeService, TicketIssueFeeService>();
builder.Services.AddTransient<IActivityLogService, ActivityLogService>();
builder.Services.AddTransient<IValidatorBookingService, ValidatorBookingService>();
builder.Services.AddTransient<IPaymentFeeConfigService, PaymentFeeConfigService>();
builder.Services.AddTransient<IVoucherService, VoucherService>();
builder.Services.AddTransient<IVoucherRedisService, VoucherRedisService>();
// ESim Services
builder.Services.AddTransient<IESimService, ESimService>();
builder.Services.AddTransient<IESimFileLogger, ESimFileLogger>();
builder.Services.AddSingleton<IRequestDeduplicationService, RequestDeduplicationService>();

// ESimBlue API Client - simplified without logging dependencies
builder.Services.AddHttpClient<IESimBlueAPIClient, ESimBlueAPIClient>();


builder.Services.AddTransient<UserTokenHelper>();
builder.Services.AddTransient<VNPayUtils>();

builder.Services.AddSingleton<ITokenBlacklistService, TokenBlacklistService>();
builder.Services.AddSingleton<IAntiDoubleClickService, AntiDoubleClickService>();
builder.Services.AddSingleton<IQueueService, QueueService>();



builder.Services.AddHostedService<EmailBackgroundService>();
builder.Services.AddHostedService<AirportBackgroundService>();
builder.Services.AddHostedService<CounterRequestBackgroundService>();


//#region start kafka

////builder.Services.AddHostedService<FlightPriceConsumerService>();

//builder.Services.Configure<KafkaSettings>(builder.Configuration.GetSection("Kafka"));
//builder.Services.AddSingleton<IKafkaProducerService, KafkaProducerService>();
////builder.Services.AddHostedService<KafkaConsumerHostedService>();

//builder.Services.AddHostedService<FlightPriceConsumerService>();


//#endregion end kafka 

// Caching services
builder.Services.AddMemoryCache(); // In-memory caching
builder.Services.AddDistributedMemoryCache(); // Distributed caching (can be Redis or SQL Server)

builder.Services.AddControllers();

builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<BrotliCompressionProvider>();
    options.Providers.Add<GzipCompressionProvider>();
});

builder.Services.Configure<BrotliCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.Fastest;
});

builder.Services.Configure<GzipCompressionProviderOptions>(options =>
{
    options.Level = CompressionLevel.SmallestSize;
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSignalR();
builder.Services.AddSingleton<IDictionary<string, UserRoomConnection>>(opt => new Dictionary<string, UserRoomConnection>());


builder.Services.AddSwaggerGen(c =>
{
    c.OperationFilter<SwaggerFileOperationFilter>();
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Swagger Ngoc Mai Travel Solution", Version = "v1" });

    // ✅ Thêm Bearer Token vào Swagger
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme.\nEnter `Bearer` [space] and then your token in the text input below.\r\nExample: 'Bearer 12345abcdef'",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.Http,
        Scheme = "Bearer",
        BearerFormat = "JWT"
    });

    // ✅ Thêm API Key vào Swagger
    c.AddSecurityDefinition("ApiKey", new OpenApiSecurityScheme
    {
        Description = "Enter the API Key in the `X-Api-Key` header to access the API.",
        Name = "X-Api-Key",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "ApiKeyScheme"
    });

    var filename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var filePath = Path.Combine(AppContext.BaseDirectory, filename);
    c.IncludeXmlComments(filePath);

    // ✅ Thêm Security Requirement cho cả Bearer và API Key
    c.AddSecurityRequirement(new OpenApiSecurityRequirement()
    {
        {
           new OpenApiSecurityScheme
           {
               Reference = new OpenApiReference
               {
                   Type = ReferenceType.SecurityScheme,
                   Id = "Bearer"
               },
               Scheme = "Bearer", // ✅ Đúng định dạng Bearer
               Name = "Authorization",
               In = ParameterLocation.Header,
           },
            new List<string>()
        },
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "ApiKey"
                },
                Scheme = "ApiKeyScheme",
                Name = "X-Api-Key",
                In = ParameterLocation.Header,
            },
            new List<string>()
        }
    });

    // ✅ Chỉ hiển thị API Key ở API có `[ApiKey]`
    c.OperationFilter<ApiKeyOperationFilter>();
});


// Configure token lifespan
builder.Services.Configure<DataProtectionTokenProviderOptions>(options =>
{
    options.TokenLifespan = TimeSpan.FromDays(3);
});


builder.Services.AddAuthentication(opt =>
{
    opt.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    opt.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    opt.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = true;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters()
    {
        ValidateIssuer = true,
        ValidIssuer = authSettings.Issuer,

        ValidateAudience = true,
        ValidAudience = authSettings.Audience,

        ValidateIssuerSigningKey = true,
        IssuerSigningKey = issuerSigningKey,

        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
    options.Events = new JwtBearerEvents
    {
        OnMessageReceived = async context =>
        {
            var ip = context.HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault() ??
             context.HttpContext.Connection.RemoteIpAddress?.ToString();

            var userAgent = context.HttpContext.Request.Headers["User-Agent"].ToString();
            //check path request
            var path = context.HttpContext.Request.Path;
            if (path.StartsWithSegments("/api/files") || path.StartsWithSegments("/api/Users/<USER>"))
            {
                return;
            }

            var access_token = context.Request.Cookies[AppSettings.AccessToken];
            var refresh_token = context.Request.Cookies[AppSettings.RefreshToken];
            var connection_id = context.Request.Cookies[AppSettings.ConnectionID];

            if (!string.IsNullOrEmpty(access_token))
            {
                context.HttpContext.Request.Headers?.Add("Authorization", $"Bearer {access_token}");
            }
            return;
        }
    };
});
// Increase multipart body length limit
builder.Services.Configure<FormOptions>(options =>
{
    options.MultipartBodyLengthLimit = 1_073_741_824;
});
//builder.Services.AddHsts(options =>
//{
//    options.Preload = true;
//    options.IncludeSubDomains = true;
//    options.MaxAge = TimeSpan.FromDays(60);
//});


var app = builder.Build();

// Configure the HTTP request pipeline.`
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
else
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}
app.UseHttpsRedirection();
// Use response compression middleware
app.UseResponseCompression();
app.Use(async (context, next) =>
{
    // Kiểm tra nếu request là file tĩnh
    var path = context.Request.Path.Value;
    if (path.EndsWith(".js") || path.EndsWith(".css"))
    {
        context.Response.Headers.Add("Access-Control-Allow-Origin", "*");
    }
    await next();
});

app.UseStaticFiles();


//set middleWate for api payment callback with ip whitelist
app.MapWhen(ctx => ctx.Request.Path.StartsWithSegments("/api/Payments", StringComparison.OrdinalIgnoreCase), cb =>
{
    cb.UseWhen(ctx => ctx.Request.Path.StartsWithSegments("/api/Payments/VNPay/Callback", StringComparison.OrdinalIgnoreCase), vnpay =>
    {
        // Log IP trước khi vào middleware whitelist
        vnpay.Use(async (context, next) =>
        {
            var ip = context.Connection.RemoteIpAddress?.ToString();
            var logger = context.RequestServices.GetRequiredService<ILoggerFactory>()
                                                .CreateLogger("VnPayIpLogger");
            logger.LogInformation("VNPay Callback from IP: {IP}", ip);
            await next();
        });

        vnpay.UseMiddleware<VnPayIpWhitelistMiddleware>();
    });

    cb.UseWhen(ctx => ctx.Request.Path.StartsWithSegments("/api/Payments/MoMo/Callback", StringComparison.OrdinalIgnoreCase), momo =>
    {
        momo.Use(async (context, next) =>
        {
            var ip = context.Connection.RemoteIpAddress?.ToString();
            var logger = context.RequestServices.GetRequiredService<ILoggerFactory>()
                                                .CreateLogger("MoMoIpLogger");
            logger.LogInformation("MoMo Callback from IP: {IP}", ip);
            await next();
        });

        momo.UseMiddleware<MoMoIpWhitelistMiddleware>();
    });

    cb.UseRouting();
    cb.UseEndpoints(endpoints => endpoints.MapControllers());
});

//app.UseHsts();
//app.UseHttpsRedirection();

app.UseRouting();
app.UseCors();
app.UseMiddleware<MobileCookieMiddleware>();



// Đảm bảo sử dụng cookie policy
app.UseCookiePolicy();
// Authentication and Authorization
app.UseAuthentication();
app.UseMiddleware<TokenBlacklistMiddleware>();
app.UseAuthorization();
app.UseMiddleware<ActivityTrackingMiddleware>();

//Swagger
//if (app.Environment.IsDevelopment())
//{

//}
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Swagger Ngoc Mai Travel Solution V1");
});



// Endpoint Routing
app.UseEndpoints(endpoints =>
{
    endpoints.MapControllers();
});

app.MapControllers();
app.MapHub<ChatHub>("/chat");

app.Run();
