﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Data.Entities
{
    public class FlightRequest
    {
        public string Id { get; set; }
        public string Depart { get; set; }
        public string Arrival { get; set; }
        public DateTime DepartDate { get; set; }
        public DateTime? ReturnDate { get; set; }
        public int Adult { get; set; }
        public int Child { get; set; }
        public int Infant { get; set; }
        public string CustomerName { get; set; }
        public string PhoneNumber { get; set; }
        public string Email { get; set; }
        public string Note { get; set; }
        public string? PaymentMethod { get; set; }
        public string? Pnrs { get; set; }
        public string? TicketNumbers { get; set; }
        public long TotalPrice { get; set; }
        public string? TransactionId { get; set; }
        public int Status { get; set; }// -1 Reject, 0 Pending, 1 Approve, 2 process
        public DateTime TimeCreate { get; set; }
        public DateTime? TimeCompletion { get; set; }
        public DateTime? TimeUpdate { get; set; }
        public string? Airlines { get; set; }
        public Guid? UpdateBy { get; set; }
        public Guid? OwnerID { get; set; } //id of XApiKey

        // Voucher information
        public string? VoucherCode { get; set; }
        public decimal? VoucherDiscount { get; set; }
        public string? UserNote { get; set; }


    }
}
