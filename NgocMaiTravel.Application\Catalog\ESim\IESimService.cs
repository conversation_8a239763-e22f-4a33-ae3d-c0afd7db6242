using NgocMaiTravel.ApiIntegration.Esim;
using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Catalog.ESim
{
    public interface IESimService
    {
        // Balance
        Task<ApiResult<BalanceRp>> GetBalanceAsync();
        // Search and Browse
        Task<ApiResult<EsimPackagesRp>> SearchPlansAsync(EsimPackagesRq rq);
        Task<ApiResult<EsimProflieRp>> GetEsimProflieAsync(string serial);

    }
}
