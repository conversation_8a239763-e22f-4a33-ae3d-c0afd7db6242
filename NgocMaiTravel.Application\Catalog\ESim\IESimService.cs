using NgocMaiTravel.ApiIntegration.Esim;
using NgocMaiTravel.ViewModels.Catalog.ESim;
using NgocMaiTravel.ViewModels.Catalog.ESimBlueAPI;
using NgocMaiTravel.ViewModels.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NgocMaiTravel.Application.Catalog.ESim
{
    public interface IESimService
    {
        // Balance
        Task<ApiResult<BalanceRp>> GetBalanceAsync();

        // Search and Browse
        Task<ApiResult<EsimPackagesRp>> SearchPlansAsync(EsimPackagesRq rq);
        Task<ApiResult<EsimPackagesRp>> SearchPlansWithContextAsync(EsimPackagesRq rq, string? customerEmail = null, string? customerPhone = null);

        // Profile Management
        Task<ApiResult<EsimProflieRp>> GetEsimProflieAsync(string serial);

        // Order Management
        Task<ApiResult<string>> CreateOrderAsync(string planId, string customerEmail, string customerPhone, string? orderId = null);

        // Logging and Analytics
        Task<ApiResult<List<ESimLogVM>>> GetRecentLogsAsync(int count = 50);
        Task<ApiResult<object>> GetPerformanceStatsAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // Request Deduplication Management
        Task<ApiResult<object>> GetDeduplicationStatsAsync();
        Task<ApiResult<string>> ClearDeduplicationCacheAsync(string? specificKey = null);
        Task<ApiResult<object>> TestConcurrentRequestsAsync(EsimPackagesRq rq, int concurrentCount = 5);
    }
}
